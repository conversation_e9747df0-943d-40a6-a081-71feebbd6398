### Spring AI Alibaba API 测试文件
### 使用 IntelliJ IDEA 或 VS Code 的 REST Client 插件运行

### 1. 健康检查
GET http://localhost:8080/ai/health
Accept: application/json

###

### 2. 文本对话 - 基础问答
GET http://localhost:8080/ai/chat?message=你好，请介绍一下Spring AI
Accept: application/json

###

### 3. 文本对话 - 技术问题
GET http://localhost:8080/ai/chat?message=Spring Boot和Spring Cloud的区别是什么？
Accept: application/json

###

### 4. 流式文本对话
GET http://localhost:8080/ai/chat/stream?message=请详细介绍Java 17的新特性
Accept: application/json

###

### 5. 图像生成 - 简单描述
GET http://localhost:8080/ai/image?prompt=一只可爱的小猫在花园里玩耍
Accept: application/json

###

### 6. 图像生成 - 复杂场景
GET http://localhost:8080/ai/image?prompt=未来科技城市的夜景，霓虹灯闪烁，飞行汽车在空中穿梭
Accept: application/json

###

### 7. 语音合成 - 中文
GET http://localhost:8080/ai/speech?text=欢迎使用Spring AI Alibaba，这是一个功能强大的AI集成框架
Accept: application/json

###

### 8. 语音合成 - 技术介绍
GET http://localhost:8080/ai/speech?text=Spring AI为Java开发者提供了简单易用的人工智能集成能力
Accept: application/json

###
