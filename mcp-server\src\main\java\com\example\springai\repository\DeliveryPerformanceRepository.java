package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 交付业绩数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface DeliveryPerformanceRepository extends JpaRepository<Order, Long> {

    /**
     * 查询交付业绩统计数据
     * 基于复杂的CTE查询，包含复购分析
     */
    @Query(value = """
        WITH o AS (
            SELECT
                id,
                billno,
                BuChannel,
                memberid,
                ProductName,
                RealTotalAmount 
            FROM [Order] 
            WHERE
                OrderState <> 99 
                AND PaySettlementTime BETWEEN :startTime AND :endTime 
                AND IsGroup = 0 
        ),
        os AS (
            SELECT
                id,
                billno,
                BuChannel,
                memberid,
                ProductName,
                RealTotalAmount,
                ROW_NUMBER() OVER (partition BY MemberId ORDER BY id) AS num 
            FROM [Order] 
            WHERE
                OrderState <> 99 
                AND PaySettlementTime BETWEEN :startTime AND :endTime 
                AND IsGroup = 0 
        ),
        m AS (
            SELECT DISTINCT(memberid) AS mid 
            FROM [Order] 
            WHERE OrderState <> 99 
              AND PaySettlementTime BETWEEN :startTime AND :endTime 
              AND IsGroup = 0
        )
        SELECT
            t1.总订单数 as totalOrderCount,
            t1.总业绩 as totalPerformance,
            t2.下单人数 as orderUserCount,
            COALESCE(t3.复购业绩, 0) as repurchasePerformance,
            COALESCE(t4.复购人数, 0) as repurchaseUserCount,
            CASE
                WHEN t2.下单人数 > 0 THEN
                    ROUND(CAST(COALESCE(t4.复购人数, 0) AS FLOAT) / t2.下单人数, 4)
                ELSE 0 
            END AS repurchaseRate
        FROM
            (SELECT COUNT(1) 总订单数, SUM(RealTotalAmount) 总业绩 FROM o) t1
            LEFT JOIN (SELECT COUNT(DISTINCT(o.memberid)) AS 下单人数 FROM o) t2 ON 1 = 1
            LEFT JOIN (
                SELECT SUM(RealTotalAmount) AS 复购业绩 
                FROM (SELECT * FROM os) a 
                WHERE a.num > 1 
                  AND memberid IN (SELECT mid FROM m) 
            ) t3 ON 1 = 1
            LEFT JOIN (
                SELECT COUNT(DISTINCT memberid) AS 复购人数 
                FROM (SELECT * FROM os) a 
                WHERE a.num > 1 
                  AND memberid IN (SELECT mid FROM m) 
            ) t4 ON 1 = 1
        """, nativeQuery = true)
    Object[] getDeliveryPerformanceStatistics(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询交付业绩产品明细
     */
    @Query(value = """
        SELECT 
            ProductName,
            COUNT(*) as orderCount,
            SUM(RealTotalAmount) as totalAmount,
            COUNT(DISTINCT memberid) as userCount
        FROM [Order]
        WHERE OrderState <> 99 
          AND PaySettlementTime BETWEEN :startTime AND :endTime 
          AND IsGroup = 0
        GROUP BY ProductName
        ORDER BY SUM(RealTotalAmount) DESC
        """, nativeQuery = true)
    List<Object[]> getDeliveryPerformanceProductDetails(@Param("startTime") LocalDateTime startTime, 
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询交付业绩渠道统计
     */
    @Query(value = """
        SELECT 
            BuChannel,
            COUNT(*) as orderCount,
            SUM(RealTotalAmount) as totalAmount,
            COUNT(DISTINCT memberid) as userCount
        FROM [Order]
        WHERE OrderState <> 99 
          AND PaySettlementTime BETWEEN :startTime AND :endTime 
          AND IsGroup = 0
        GROUP BY BuChannel
        ORDER BY SUM(RealTotalAmount) DESC
        """, nativeQuery = true)
    List<Object[]> getDeliveryPerformanceChannelStats(@Param("startTime") LocalDateTime startTime, 
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查询交付业绩每日趋势
     */
    @Query(value = """
        SELECT 
            CAST(PaySettlementTime AS DATE) as settlementDate,
            COUNT(*) as dailyOrderCount,
            SUM(RealTotalAmount) as dailyPerformance,
            COUNT(DISTINCT memberid) as dailyUserCount
        FROM [Order]
        WHERE OrderState <> 99 
          AND PaySettlementTime BETWEEN :startTime AND :endTime 
          AND IsGroup = 0
        GROUP BY CAST(PaySettlementTime AS DATE)
        ORDER BY settlementDate DESC
        """, nativeQuery = true)
    List<Object[]> getDeliveryPerformanceDailyTrend(@Param("startTime") LocalDateTime startTime, 
                                                   @Param("endTime") LocalDateTime endTime);
}
