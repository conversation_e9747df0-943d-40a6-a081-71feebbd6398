package com.example.springai.service;

import com.example.springai.repository.PrivateDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 私域ABC收款业绩数据服务
 * 处理私域ABC收款业绩相关的查询
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class PrivateDomainService {
    
    @Autowired
    private PrivateDomainRepository privateDomainRepository;
    
    // 每月目标金额：621万元
    private static final BigDecimal MONTHLY_TARGET = new BigDecimal("6210000");
    
    /**
     * 获取私域ABC收款业绩统计数据
     */
    public Map<String, Object> getPrivateDomainStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询私域ABC收款业绩统计: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            // 获取总金额和订单数
            List<Object[]> totalResultList = privateDomainRepository.getPrivateDomainTotalAmount(startTime, endTime);
            BigDecimal totalAmount = BigDecimal.ZERO;
            Long totalOrderCount = 0L;

            if (totalResultList != null && !totalResultList.isEmpty()) {
                Object[] totalResult = totalResultList.get(0);
                if (totalResult != null && totalResult.length >= 2) {
                    totalAmount = totalResult[0] != null ? (BigDecimal) totalResult[0] : BigDecimal.ZERO;
                    totalOrderCount = totalResult[1] != null ? ((Number) totalResult[1]).longValue() : 0L;
                }
            }

            // 获取详细统计
            List<Object[]> detailResultList = privateDomainRepository.getPrivateDomainDetailedStats(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "私域ABC收款业绩统计");
            result.put("message", "查询私域ABC收款业绩数据统计");
            
            // 基础统计数据
            result.put("totalAmount", totalAmount);
            result.put("totalOrderCount", totalOrderCount);

            // 处理详细统计数据
            if (detailResultList != null && !detailResultList.isEmpty()) {
                Object[] detailResult = detailResultList.get(0);
                if (detailResult != null && detailResult.length >= 4) {
                    BigDecimal detailTotalAmount = detailResult[0] != null ? (BigDecimal) detailResult[0] : BigDecimal.ZERO;
                    int detailOrderCount = detailResult[1] != null ? ((Number) detailResult[1]).intValue() : 0;
                    int productCount = detailResult[2] != null ? ((Number) detailResult[2]).intValue() : 0;
                    BigDecimal avgAmount = detailResult[3] != null ? (BigDecimal) detailResult[3] : BigDecimal.ZERO;

                    result.put("orderAmount", detailTotalAmount);
                    result.put("orderCount", detailOrderCount);
                    result.put("productCount", productCount);
                    result.put("avgAmount", avgAmount);

                    log.info("私域ABC收款业绩统计结果 - 总金额: {}, 订单数: {}, 产品数: {}, 平均金额: {}",
                            detailTotalAmount, detailOrderCount, productCount, avgAmount);
                } else {
                    log.warn("私域ABC收款业绩详细统计数据格式异常");
                    result.put("orderAmount", totalAmount);
                    result.put("orderCount", totalOrderCount);
                    result.put("productCount", 0);
                    result.put("avgAmount", BigDecimal.ZERO);
                }
            } else {
                log.warn("私域ABC收款业绩详细统计查询返回空结果");
                result.put("orderAmount", totalAmount);
                result.put("orderCount", totalOrderCount);
                result.put("productCount", 0);
                result.put("avgAmount", BigDecimal.ZERO);
            }
            
            // 计算目标完成情况
            calculateTargetCompletion(result, totalAmount, startDate, endDate);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询私域ABC收款业绩数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取私域ABC收款业绩趋势数据
     */
    public Map<String, Object> getPrivateDomainTrend(LocalDate startDate, LocalDate endDate) {
        log.info("查询私域ABC收款业绩趋势: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> dailyStats = privateDomainRepository.getPrivateDomainDailyStats(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "私域ABC收款业绩趋势");
            result.put("message", "查询私域ABC收款业绩趋势数据");
            result.put("dailyStats", formatDailyStats(dailyStats));
            
            // 计算总体统计
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalPayments = 0;
            
            for (Object[] daily : dailyStats) {
                BigDecimal orderAmount = daily[1] != null ? (BigDecimal) daily[1] : BigDecimal.ZERO;
                BigDecimal directAmount = daily[2] != null ? (BigDecimal) daily[2] : BigDecimal.ZERO;
                int payments = daily[3] != null ? ((Number) daily[3]).intValue() : 0;
                
                totalAmount = totalAmount.add(orderAmount).add(directAmount);
                totalPayments += payments;
            }
            
            result.put("totalAmount", totalAmount);
            result.put("totalPayments", totalPayments);
            
            // 计算目标完成情况
            calculateTargetCompletion(result, totalAmount, startDate, endDate);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询私域ABC收款业绩趋势失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 计算目标完成情况
     */
    private void calculateTargetCompletion(Map<String, Object> result, BigDecimal actualAmount, 
                                         LocalDate startDate, LocalDate endDate) {
        
        // 判断查询时间范围类型
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        boolean isMonthlyQuery = isMonthlyPeriod(startDate, endDate);
        
        if (isMonthlyQuery) {
            // 月度目标完成率计算
            BigDecimal completionRate = actualAmount.divide(MONTHLY_TARGET, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            BigDecimal remainingTarget = MONTHLY_TARGET.subtract(actualAmount);
            
            result.put("monthlyTarget", MONTHLY_TARGET);
            result.put("completionRate", completionRate);
            result.put("remainingTarget", remainingTarget.max(BigDecimal.ZERO));
            result.put("isTargetAchieved", actualAmount.compareTo(MONTHLY_TARGET) >= 0);
            
            log.info("月度目标完成情况 - 目标: {}, 实际: {}, 完成率: {}%, 剩余: {}", 
                    MONTHLY_TARGET, actualAmount, completionRate, remainingTarget);
        } else {
            // 非月度查询，按比例计算目标
            BigDecimal proportionalTarget = calculateProportionalTarget(startDate, endDate);
            if (proportionalTarget.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal completionRate = actualAmount.divide(proportionalTarget, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                BigDecimal remainingTarget = proportionalTarget.subtract(actualAmount);

                // 期间目标信息
                result.put("proportionalTarget", proportionalTarget);
                result.put("completionRate", completionRate);
                result.put("remainingTarget", remainingTarget.max(BigDecimal.ZERO));
                result.put("isTargetAchieved", actualAmount.compareTo(proportionalTarget) >= 0);

                // 同时返回总目标信息
                result.put("monthlyTarget", MONTHLY_TARGET);
                BigDecimal monthlyCompletionRate = actualAmount.divide(MONTHLY_TARGET, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                BigDecimal monthlyRemainingTarget = MONTHLY_TARGET.subtract(actualAmount);
                result.put("monthlyCompletionRate", monthlyCompletionRate);
                result.put("monthlyRemainingTarget", monthlyRemainingTarget.max(BigDecimal.ZERO));
                result.put("isMonthlyTargetAchieved", actualAmount.compareTo(MONTHLY_TARGET) >= 0);

                // 计算查询天数和月度天数信息
                long queryDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                long monthDays = startDate.lengthOfMonth();
                result.put("queryDays", queryDays);
                result.put("monthDays", monthDays);
                result.put("targetRatio", BigDecimal.valueOf(queryDays).divide(BigDecimal.valueOf(monthDays), 4, RoundingMode.HALF_UP));

                log.info("比例目标完成情况 - 期间目标: {}, 月度目标: {}, 实际: {}, 期间完成率: {}%, 月度完成率: {}%",
                        proportionalTarget, MONTHLY_TARGET, actualAmount, completionRate, monthlyCompletionRate);
            }
        }
    }
    
    /**
     * 判断是否为月度查询
     */
    private boolean isMonthlyPeriod(LocalDate startDate, LocalDate endDate) {
        return startDate.getDayOfMonth() == 1 && 
               endDate.equals(startDate.withDayOfMonth(startDate.lengthOfMonth()));
    }
    
    /**
     * 计算比例目标
     */
    private BigDecimal calculateProportionalTarget(LocalDate startDate, LocalDate endDate) {
        long queryDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        long monthDays = startDate.lengthOfMonth();
        
        return MONTHLY_TARGET.multiply(BigDecimal.valueOf(queryDays))
            .divide(BigDecimal.valueOf(monthDays), 2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化每日统计数据
     */
    private List<Map<String, Object>> formatDailyStats(List<Object[]> dailyStats) {
        return dailyStats.stream()
                .map(data -> {
                    Map<String, Object> dailyStat = new HashMap<>();
                    dailyStat.put("date", data[0] != null ? data[0].toString() : "");
                    dailyStat.put("orderAmount", data[1] != null ? (BigDecimal) data[1] : BigDecimal.ZERO);
                    dailyStat.put("directAmount", data[2] != null ? (BigDecimal) data[2] : BigDecimal.ZERO);
                    dailyStat.put("totalPayments", data[3] != null ? ((Number) data[3]).intValue() : 0);

                    // 计算当日总金额
                    BigDecimal orderAmount = (BigDecimal) dailyStat.get("orderAmount");
                    BigDecimal directAmount = (BigDecimal) dailyStat.get("directAmount");
                    dailyStat.put("totalAmount", orderAmount.add(directAmount));

                    return dailyStat;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
