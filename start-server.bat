@echo off
chcp 65001 >nul
echo ==========================================
echo 🚀 启动MCP服务器
echo ==========================================
echo.

echo 编译项目...
call .\mvnw.cmd clean compile -pl mcp-common,mcp-server
if %errorlevel% neq 0 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)

echo.
echo 启动MCP服务器...
echo 注意：请确保已在mcp-server/src/main/resources/application.yml中配置正确的API Key
echo.
cd mcp-server
call ..\mvnw.cmd spring-boot:run

pause
