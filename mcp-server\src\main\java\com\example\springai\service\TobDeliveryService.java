package com.example.springai.service;

import com.example.springai.repository.TobDeliveryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TOB交付业绩数据服务
 * 处理TOB交付业绩相关的查询
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class TobDeliveryService {
    
    @Autowired
    private TobDeliveryRepository tobDeliveryRepository;
    
    /**
     * 获取TOB交付业绩统计数据
     */
    public Map<String, Object> getTobDeliveryStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询TOB交付业绩统计: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        log.info("TOB交付业绩查询参数 - startTime: {}, endTime: {}", startTime, endTime);
        
        try {
            List<Object[]> statsResultList = tobDeliveryRepository.getTobDeliveryStatistics(startTime, endTime);
            log.info("TOB交付业绩统计查询原始结果: {}", statsResultList);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "TOB交付业绩统计");
            result.put("message", "查询TOB交付业绩数据统计");
            
            if (statsResultList != null && !statsResultList.isEmpty()) {
                Object[] statsResult = statsResultList.get(0);
                log.info("第一行结果: {}", statsResult != null ? java.util.Arrays.toString(statsResult) : "null");
                
                if (statsResult != null && statsResult.length >= 3) {
                    int totalOrderCount = statsResult[0] != null ? ((Number) statsResult[0]).intValue() : 0;
                    BigDecimal totalAmount = statsResult[1] != null ? (BigDecimal) statsResult[1] : BigDecimal.ZERO;
                    int productCount = statsResult[2] != null ? ((Number) statsResult[2]).intValue() : 0;
                    
                    result.put("totalOrderCount", totalOrderCount);
                    result.put("totalAmount", totalAmount);
                    result.put("productCount", productCount);
                    
                    // 计算平均订单金额
                    if (totalOrderCount > 0) {
                        result.put("avgOrderAmount", totalAmount.divide(BigDecimal.valueOf(totalOrderCount), 2, RoundingMode.HALF_UP));
                    }
                    
                    // 计算平均产品金额
                    if (productCount > 0) {
                        result.put("avgProductAmount", totalAmount.divide(BigDecimal.valueOf(productCount), 2, RoundingMode.HALF_UP));
                    }
                    
                    log.info("TOB交付业绩统计结果 - 总订单: {}, 总金额: {}, 产品数: {}", 
                            totalOrderCount, totalAmount, productCount);
                } else {
                    log.warn("TOB交付业绩统计查询返回空结果");
                    result.put("totalOrderCount", 0);
                    result.put("totalAmount", BigDecimal.ZERO);
                    result.put("productCount", 0);
                }
            } else {
                log.warn("TOB交付业绩查询结果列表为空");
                result.put("totalOrderCount", 0);
                result.put("totalAmount", BigDecimal.ZERO);
                result.put("productCount", 0);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("查询TOB交付业绩数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取TOB交付业绩产品明细
     */
    public Map<String, Object> getTobDeliveryProductDetails(LocalDate startDate, LocalDate endDate) {
        log.info("查询TOB交付业绩产品明细: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> productDetails = tobDeliveryRepository.getTobDeliveryProductDetails(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "TOB交付业绩产品明细");
            result.put("message", "查询TOB交付业绩产品明细数据");
            
            List<Map<String, Object>> productStats = formatProductDetails(productDetails);
            result.put("productDetails", productStats);
            
            // 计算总体统计
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalOrders = 0;
            
            for (Map<String, Object> product : productStats) {
                Object amount = product.get("totalAmount");
                Object orderCount = product.get("orderCount");
                
                if (amount instanceof BigDecimal) {
                    totalAmount = totalAmount.add((BigDecimal) amount);
                }
                if (orderCount instanceof Number) {
                    totalOrders += ((Number) orderCount).intValue();
                }
            }
            
            result.put("totalAmount", totalAmount);
            result.put("totalOrderCount", totalOrders);
            result.put("productCount", productStats.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询TOB交付业绩产品明细失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取TOB交付业绩排行（按金额）
     */
    public Map<String, Object> getTobDeliveryRankingByAmount(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询TOB交付业绩排行(按金额): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> rankingData = tobDeliveryRepository.getTobDeliveryRankingByAmount(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "TOB交付业绩排行(金额)");
            result.put("message", "查询TOB交付业绩排行，按金额排序");
            result.put("rankingType", "金额");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = rankingData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            List<Map<String, Object>> productRanking = formatRankingData(limitedData);
            result.put("productRanking", productRanking);
            
            // 计算累加统计（基于所有数据，不仅仅是限制的数据）
            List<Map<String, Object>> allProductStats = formatRankingData(rankingData);
            calculateRankingStatistics(result, allProductStats, productRanking);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询TOB交付业绩排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取TOB交付业绩排行（按订单数）
     */
    public Map<String, Object> getTobDeliveryRankingByOrderCount(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询TOB交付业绩排行(按订单数): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> rankingData = tobDeliveryRepository.getTobDeliveryRankingByOrderCount(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "TOB交付业绩排行(订单数)");
            result.put("message", "查询TOB交付业绩排行，按订单数排序");
            result.put("rankingType", "订单数");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = rankingData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            List<Map<String, Object>> productRanking = formatRankingData(limitedData);
            result.put("productRanking", productRanking);
            
            // 计算累加统计（基于所有数据，不仅仅是限制的数据）
            List<Map<String, Object>> allProductStats = formatRankingData(rankingData);
            calculateRankingStatistics(result, allProductStats, productRanking);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询TOB交付业绩排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }

    /**
     * 格式化产品明细数据
     */
    private List<Map<String, Object>> formatProductDetails(List<Object[]> productDetails) {
        return productDetails.stream()
                .map(data -> {
                    Map<String, Object> productDetail = new HashMap<>();
                    productDetail.put("productName", data[0] != null ? data[0].toString() : "未知产品");
                    productDetail.put("orderCount", data[1] != null ? ((Number) data[1]).intValue() : 0);
                    productDetail.put("totalAmount", data[2] != null ? (BigDecimal) data[2] : BigDecimal.ZERO);
                    productDetail.put("avgAmount", data[3] != null ? (BigDecimal) data[3] : BigDecimal.ZERO);
                    productDetail.put("minAmount", data[4] != null ? (BigDecimal) data[4] : BigDecimal.ZERO);
                    productDetail.put("maxAmount", data[5] != null ? (BigDecimal) data[5] : BigDecimal.ZERO);
                    return productDetail;
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化排行数据
     */
    private List<Map<String, Object>> formatRankingData(List<Object[]> rankingData) {
        return rankingData.stream()
                .map(data -> {
                    Map<String, Object> rankingDetail = new HashMap<>();
                    rankingDetail.put("productName", data[0] != null ? data[0].toString() : "未知产品");
                    rankingDetail.put("orderCount", data[1] != null ? ((Number) data[1]).intValue() : 0);
                    rankingDetail.put("totalAmount", data[2] != null ? (BigDecimal) data[2] : BigDecimal.ZERO);
                    return rankingDetail;
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算排行统计数据
     */
    private void calculateRankingStatistics(Map<String, Object> result,
                                          List<Map<String, Object>> allProductStats,
                                          List<Map<String, Object>> limitedProductStats) {

        BigDecimal totalAmount = BigDecimal.ZERO;
        int totalOrders = 0;

        for (Map<String, Object> product : allProductStats) {
            Object amount = product.get("totalAmount");
            Object orderCount = product.get("orderCount");

            if (amount instanceof BigDecimal) {
                totalAmount = totalAmount.add((BigDecimal) amount);
            }

            if (orderCount instanceof Number) {
                totalOrders += ((Number) orderCount).intValue();
            }
        }

        result.put("productCount", allProductStats.size());
        result.put("totalOrderCount", totalOrders);
        result.put("totalAmount", totalAmount);
    }

    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
