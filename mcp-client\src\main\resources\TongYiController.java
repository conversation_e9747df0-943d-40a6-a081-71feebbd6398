package com.example.springai.controller;

import com.example.springai.service.TongYiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.image.ImageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 通义千问AI控制器
 * 提供RESTful API接口
 * 
 * <AUTHOR> AI Alibaba Demo
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ai")
@CrossOrigin(origins = "*")
public class TongYiController {

    private final TongYiService tongYiService;

    @Autowired
    public TongYiController(TongYiService tongYiService) {
        this.tongYiService = tongYiService;
    }

    /**
     * 文本对话接口
     * 
     * @param message 用户输入的消息
     * @return AI回复内容
     */
    @GetMapping("/chat")
    public ResponseEntity<Map<String, Object>> chat(
            @RequestParam(value = "message", defaultValue = "你好，请介绍一下Spring AI") String message) {
        
        log.info("收到文本对话请求：{}", message);
        
        try {
            String response = tongYiService.completion(message);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "对话成功");
            result.put("data", Map.of(
                "question", message,
                "answer", response,
                "timestamp", System.currentTimeMillis()
            ));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("文本对话处理失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                        "success", false,
                        "message", "对话失败: " + e.getMessage(),
                        "timestamp", System.currentTimeMillis()
                    ));
        }
    }

    /**
     * 流式文本对话接口
     * 
     * @param message 用户输入的消息
     * @return AI流式回复内容
     */
    @GetMapping("/chat/stream")
    public ResponseEntity<Map<String, Object>> streamChat(
            @RequestParam(value = "message", defaultValue = "请详细介绍Spring Boot的特性") String message) {
        
        log.info("收到流式文本对话请求：{}", message);
        
        try {
            String response = tongYiService.streamCompletion(message);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "流式对话成功");
            result.put("data", Map.of(
                "question", message,
                "answer", response,
                "timestamp", System.currentTimeMillis()
            ));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("流式文本对话处理失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                        "success", false,
                        "message", "流式对话失败: " + e.getMessage(),
                        "timestamp", System.currentTimeMillis()
                    ));
        }
    }

    /**
     * 图像生成接口
     * 
     * @param prompt 图像生成提示词
     * @return 生成的图像信息
     */
    @GetMapping("/image")
    public ResponseEntity<Map<String, Object>> generateImage(
            @RequestParam(value = "prompt", defaultValue = "一只可爱的小猫在花园里玩耍") String prompt) {
        
        log.info("收到图像生成请求：{}", prompt);
        
        try {
            ImageResponse imageResponse = tongYiService.generateImage(prompt);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "图像生成成功");
            result.put("data", Map.of(
                "prompt", prompt,
                "images", imageResponse.getResults(),
                "timestamp", System.currentTimeMillis()
            ));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {





            log.error("图像生成处理失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                        "success", false,
                        "message", "图像生成失败: " + e.getMessage(),
                        "timestamp", System.currentTimeMillis()
                    ));
        }
    }

    /**
     * 语音合成接口
     * 
     * @param text 需要转换为语音的文本
     * @return 音频文件路径
     */
    @GetMapping("/speech")
    public ResponseEntity<Map<String, Object>> generateSpeech(
            @RequestParam(value = "text", defaultValue = "欢迎使用Spring AI Alibaba") String text) {
        
        log.info("收到语音合成请求，文本长度：{}", text.length());
        
        try {
            String audioPath = tongYiService.generateSpeech(text);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "语音合成成功");
            result.put("data", Map.of(
                "text", text,
                "audioPath", audioPath,
                "timestamp", System.currentTimeMillis()
            ));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("语音合成处理失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                        "success", false,
                        "message", "语音合成失败: " + e.getMessage(),
                        "timestamp", System.currentTimeMillis()
                    ));
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 应用状态信息
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("application", "Spring AI Alibaba Demo");
        result.put("version", "1.0.0");
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(result);
    }
}
