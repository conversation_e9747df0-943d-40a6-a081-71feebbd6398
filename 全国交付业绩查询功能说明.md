# 全国交付业绩查询功能说明

## 🎯 功能概述

新增全国交付业绩数据查询服务，基于StoreId=1038的订单数据，支持自然语言查询全国交付业绩统计、排行等信息。

## 📊 数据源说明

### SQL基础查询
```sql
SELECT
    t.RealTotalAmount AS 订单金额,
    t.ProductName AS 产品名称,
    t.CreateTime AS 订单时间,
    (SELECT ProductType FROM Product WHERE productid = id) AS 类别 
FROM [Order] AS t 
WHERE t.StoreId = 1038 
    AND t.OrderState <> 99 
    AND t.PaySettlementTime BETWEEN '2025/1/1' AND '2025/3/1'
```

### 查询条件
- **StoreId = 1038** - 全国交付专用门店ID
- **OrderState <> 99** - 排除无效订单
- **PaySettlementTime** - 基于支付结算时间范围
- **有效产品** - 关联Product表获取产品信息

## 🔍 支持的查询类型

### 1. 基础统计查询
**触发关键词：** "全国交付业绩"、"全国交付统计"、"全国交付情况"

**查询示例：**
- "本月全国交付业绩"
- "上周全国交付统计"
- "全国交付业绩情况"

**返回内容：**
- 总体概况（产品数量、订单数、总营业额）
- 平均订单金额
- 产品明细列表

### 2. 排行榜查询
**触发关键词：** "全国交付排行"、"全国交付业绩排行"

**查询示例：**
- "全国交付业绩排行"
- "全国交付营业额排行"
- "全国交付订单数排行"

**排序方式：**
- 默认按订单数排序
- 包含"营业额"或"收入"时按营业额排序

### 3. 时间范围支持
- **今天、昨天**
- **本周、上周**
- **本月、上月**
- **近7天、近30天**
- **自定义时间范围**

## 📋 返回数据格式

### 统计查询返回示例
```
**全国交付业绩统计**

查询时间：30天

**📊 总体概况**
• **产品数量：25个** 🛍️
• **总订单数：1,256单** 📦
• **总营业额：45.67万元** 💰
• 平均订单金额：363.58元 📊

**📋 产品明细**
🥇 **基础搬家2.5h套餐**：156单，5.62万元
🥈 **标准搬家3h套餐**：98单，8.13万元
🥉 **深度净护(子单)**：87单，3.19万元
🏅 **双面精细擦窗**：76单，2.09万元
🏅 **厨房焕亮组合(基础款)**：65单，2.23万元
```

### 排行榜查询返回示例
```
**全国交付业绩排行(订单数)**

查询时间：30天

**📊 总体概况**
• **产品数量：25个** 🛍️
• **总订单数：1,256单** 📦
• **总营业额：45.67万元** 💰
• 平均订单金额：363.58元 📊

**🏆 产品排行榜（按订单数）**
🥇 **基础搬家2.5h套餐**：156单，5.62万元
🥈 **标准搬家3h套餐**：98单，8.13万元
🥉 **深度净护(子单)**：87单，3.19万元
... 还有22个产品未显示
```

## 🔧 技术架构

### 1. 服务层结构
- **NationalDeliveryRepository** - 数据访问层
- **NationalDeliveryService** - 业务逻辑层
- **BusinessAIService** - AI集成层

### 2. 核心方法
```java
// 基础统计查询
getNationalDeliveryStatistics(startDate, endDate)

// 按订单数排行
getNationalDeliveryRankingByOrderCount(startDate, endDate, limit)

// 按营业额排行
getNationalDeliveryRankingByRevenue(startDate, endDate, limit)

// 总体统计概览
getNationalDeliveryOverview(startDate, endDate)
```

### 3. 数据处理特性
- **累加统计验证** - 从明细数据累加验证总体统计
- **智能数据选择** - 优先使用准确的非零数据
- **货币格式化** - 大金额自动转换为万元显示
- **排名图标** - 前5名使用emoji图标显示

## 🧪 测试用例

### 正常查询测试
```
✅ "本月全国交付业绩" → 返回本月统计数据
✅ "全国交付业绩排行" → 返回排行榜数据
✅ "上周全国交付统计" → 返回上周数据
✅ "全国交付营业额排行" → 按营业额排序
```

### 不合理查询测试
```
❌ "下月全国交付业绩" → 幽默拒绝
❌ "明天全国交付统计" → 未来时间拒绝
❌ "去年全国交付业绩" → 久远时间提示
```

### 边界情况测试
```
🔍 无数据时 → 显示"暂无统计数据"
🔍 单个产品 → 正常显示统计信息
🔍 大量产品 → 限制显示前5个，提示剩余数量
```

## 📈 数据统计特性

### 1. 多维度统计
- **产品维度** - 按产品分组统计
- **时间维度** - 支持各种时间范围
- **排序维度** - 订单数/营业额双重排序

### 2. 智能计算
- **平均订单金额** - 总营业额 ÷ 总订单数
- **平均单品营业额** - 总营业额 ÷ 产品数量
- **占比分析** - 头部产品占总体的百分比

### 3. 数据验证
- **数据库统计** - SQL聚合查询结果
- **程序计算** - 从明细数据累加计算
- **一致性检查** - 确保数据准确性

## 🎨 用户体验优化

### 1. 友好的回复格式
- 使用emoji图标增强视觉效果
- 清晰的层级结构
- 重要数据加粗显示

### 2. 智能查询识别
- 支持多种表达方式
- 自动识别查询意图
- 容错性强的关键词匹配

### 3. 合理的数据展示
- 限制显示数量避免信息过载
- 提供剩余数据数量提示
- 突出显示关键统计信息

通过这个全国交付业绩查询功能，用户可以方便地了解全国交付业务的整体表现和产品排行情况！
