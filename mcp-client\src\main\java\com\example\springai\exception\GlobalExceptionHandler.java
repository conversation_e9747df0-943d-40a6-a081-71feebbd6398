package com.example.springai.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 拦截所有异常并返回友好的提示信息
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 友好的错误提示信息
     */
    private static final String FRIENDLY_ERROR_MESSAGE = "当前问题我还没学会，请给我点时间，我正在学习中哦~";

    /**
     * 处理所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        // 记录详细的异常信息到日志
        log.error("系统异常: {}", e.getMessage(), e);
        
        // 返回友好的提示信息
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("data", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("data", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理MCP连接异常
     */
    @ExceptionHandler({
        java.util.concurrent.CompletionException.class,
        java.util.concurrent.ExecutionException.class
    })
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleMcpException(Exception e) {
        log.error("MCP连接异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("data", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理网络连接异常
     */
    @ExceptionHandler({
        java.net.ConnectException.class,
        java.net.SocketTimeoutException.class,
        java.io.IOException.class
    })
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleNetworkException(Exception e) {
        log.error("网络连接异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("data", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}
