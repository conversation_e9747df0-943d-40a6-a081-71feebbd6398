package com.example.springai.jingang.service;

import com.example.springai.jingang.repository.CompanyTransactionRepository;
import com.example.springai.util.DateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 金刚到家软件收入查询服务
 * 
 * <AUTHOR>
 */
@Service
@Transactional("jingangTransactionManager")
public class JingangIncomeService {

    private static final Logger log = LoggerFactory.getLogger(JingangIncomeService.class);

    // 每月目标金额：21万元
    private static final BigDecimal MONTHLY_TARGET = new BigDecimal("210000");

    @Autowired
    private CompanyTransactionRepository companyTransactionRepository;

    /**
     * 获取金刚软件收入统计数据
     */
    public Map<String, Object> getJingangIncomeStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询金刚软件收入统计: {} 到 {}", startDate, endDate);

        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);

        try {
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "金刚软件收入统计");
            result.put("message", "查询金刚软件收入数据统计");

            // 获取汇总数据
            List<Object[]> summaryList = companyTransactionRepository.findIncomeSummary(startTime, endTime);

            if (summaryList != null && !summaryList.isEmpty()) {
                Object[] summary = summaryList.get(0);
                log.info("金刚软件收入统计查询原始结果: {}", summary != null ? java.util.Arrays.toString(summary) : "null");
                log.info("金刚软件收入统计查询结果长度: {}", summary != null ? summary.length : 0);

                if (summary != null && summary.length >= 2) {
                    // 根据用户反馈，检查数据是否都在summary[0]中
                    log.info("summary[0]: {} (类型: {})", summary[0], summary[0] != null ? summary[0].getClass().getSimpleName() : "null");
                    log.info("summary[1]: {} (类型: {})", summary[1], summary[1] != null ? summary[1].getClass().getSimpleName() : "null");

                    BigDecimal totalAmount = summary[0] != null ? (BigDecimal) summary[0] : BigDecimal.ZERO;
                    Long totalCount = summary[1] != null ? ((Number) summary[1]).longValue() : 0L;

                    result.put("totalAmount", totalAmount);
                    result.put("totalCount", totalCount);

                    // 计算平均金额
                    if (totalCount > 0) {
                        result.put("avgAmount", totalAmount.divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP));
                    } else {
                        result.put("avgAmount", BigDecimal.ZERO);
                    }

                    log.info("金刚软件收入统计结果 - 总金额: {}, 总笔数: {}", totalAmount, totalCount);
                } else {
                    log.warn("金刚软件收入统计查询返回数据格式异常");
                    result.put("totalAmount", BigDecimal.ZERO);
                    result.put("totalCount", 0L);
                    result.put("avgAmount", BigDecimal.ZERO);
                }
            } else {
                log.warn("金刚软件收入统计查询返回空结果");
                result.put("totalAmount", BigDecimal.ZERO);
                result.put("totalCount", 0L);
                result.put("avgAmount", BigDecimal.ZERO);
            }

            // 获取明细数据
            List<Object[]> details = companyTransactionRepository.findIncomeDetails(startTime, endTime);
            List<Map<String, Object>> transactionDetails = formatTransactionDetails(details);
            result.put("transactionDetails", transactionDetails);

            // 计算目标完成情况
            BigDecimal totalAmount = (BigDecimal) result.get("totalAmount");
            calculateTargetCompletion(result, totalAmount, startDate, endDate);

            return result;

        } catch (Exception e) {
            log.error("查询金刚软件收入数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }

    /**
     * 测试方法 - 查看表结构
     */
    public String testTableStructure() {
        try {
            log.info("开始查看company_transaction表结构");

            // 查看表结构
            List<Object[]> columns = companyTransactionRepository.showTableColumns();
            StringBuilder sb = new StringBuilder();
            sb.append("**company_transaction表结构**\n\n");

            for (Object[] column : columns) {
                if (column != null && column.length >= 6) {
                    sb.append(String.format("字段: %s, 类型: %s, 是否为空: %s, 键: %s, 默认值: %s, 额外: %s\n",
                        column[0], column[1], column[2], column[3], column[4], column[5]));
                    log.info("字段信息: {}", java.util.Arrays.toString(column));
                } else {
                    log.warn("字段信息不完整: 长度={}", column != null ? column.length : 0);
                }
            }

            // 查看样本数据
            List<Object[]> samples = companyTransactionRepository.findSampleRecords();
            sb.append("\n**样本数据**\n\n");

            for (int i = 0; i < Math.min(3, samples.size()); i++) {
                Object[] row = samples.get(i);
                if (row != null && row.length >= 6) {
                    sb.append(String.format("记录%d: ID=%s, 类型=%s, 时间=%s, 金额=%s, 业务类型=%s, 描述=%s\n",
                            i+1, row[0], row[1], row[2], row[3], row[4], row[5]));
                    log.info("样本数据{}: ID={}, 类型={}, 时间={}, 金额={}, 业务类型={}, 描述={}",
                            i+1, row[0], row[1], row[2], row[3], row[4], row[5]);
                } else {
                    sb.append(String.format("记录%d: 数据不完整\n", i+1));
                    log.warn("样本数据{}不完整: 长度={}", i+1, row != null ? row.length : 0);
                }
            }

            return sb.toString();

        } catch (Exception e) {
            log.error("查看表结构时出现错误", e);
            return "查看表结构时出现错误: " + e.getMessage();
        }
    }

    /**
     * 测试方法 - 查看所有收入记录
     */
    public String testAllIncomeRecords() {
        try {
            log.info("开始查看所有收入记录");

            // 查看所有收入汇总
            Object[] summary = companyTransactionRepository.findAllIncomeSummary();
            log.info("所有收入汇总: {}", summary != null ? java.util.Arrays.toString(summary) : "null");

            // 查看所有收入记录
            List<Object[]> records = companyTransactionRepository.findAllIncomeRecords();
            log.info("所有收入记录数量: {}", records.size());

            StringBuilder sb = new StringBuilder();
            sb.append("**所有金刚到家软件收入记录**\n\n");

            if (summary != null && summary.length >= 1) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                Long totalCount = 0L;

                try {
                    // 处理汇总数据的类型转换问题
                    log.info("汇总数据原始值: {}", java.util.Arrays.toString(summary));

                    // 检查summary[0]是否包含聚合结果
                    if (summary[0] != null) {
                        Object firstElement = summary[0];
                        log.info("汇总第一个元素类型: {}, 值: {}",
                                firstElement.getClass().getName(), firstElement);

                        // 如果第一个元素是数组，说明聚合结果在数组中
                        if (firstElement instanceof Object[]) {
                            Object[] innerArray = (Object[]) firstElement;
                            log.info("汇总内部数组长度: {}, 内容: {}", innerArray.length, java.util.Arrays.toString(innerArray));

                            if (innerArray.length >= 1 && innerArray[0] != null) {
                                Object amountObj = innerArray[0];
                                if (amountObj instanceof BigDecimal) {
                                    totalAmount = (BigDecimal) amountObj;
                                } else if (amountObj instanceof Number) {
                                    totalAmount = new BigDecimal(amountObj.toString());
                                }
                            }

                            if (innerArray.length >= 2 && innerArray[1] != null) {
                                Object countObj = innerArray[1];
                                if (countObj instanceof Number) {
                                    totalCount = ((Number) countObj).longValue();
                                }
                            }
                        } else {
                            // 如果第一个元素不是数组，尝试直接解析
                            if (firstElement instanceof BigDecimal) {
                                totalAmount = (BigDecimal) firstElement;
                            } else if (firstElement instanceof Number) {
                                totalAmount = new BigDecimal(firstElement.toString());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("汇总数据转换异常: {}", e.getMessage());
                }

                sb.append(String.format("💰 **总收入**: %,.2f元\n", totalAmount));
                sb.append(String.format("📊 **总笔数**: %d笔\n\n", totalCount));
            }

            sb.append("**详细记录**:\n\n");

            for (int i = 0; i < Math.min(10, records.size()); i++) {
                Object[] row = records.get(i);
                log.info("收入记录{}: {}", i+1, java.util.Arrays.toString(row));

                try {
                    String transactionTime = row.length > 0 && row[0] != null ? row[0].toString() : "";
                    String transactionType = row.length > 1 && row[1] != null ? row[1].toString() : "";
                    String amount = row.length > 2 && row[2] != null ? row[2].toString() : "";
                    String businessType = row.length > 3 && row[3] != null ? row[3].toString() : "";
                    String description = row.length > 4 && row[4] != null ? row[4].toString() : "";

                    sb.append(String.format("**第%d笔**\n", i + 1));
                    sb.append(String.format("    时间: %s\n", transactionTime));
                    sb.append(String.format("    类型: %s\n", transactionType));
                    sb.append(String.format("    金额: %s元\n", amount));
                    sb.append(String.format("    业务: %s\n", businessType));
                    sb.append(String.format("    描述: %s\n\n", description));

                } catch (Exception e) {
                    log.error("处理收入记录异常: {}", e.getMessage());
                }
            }

            return sb.toString();

        } catch (Exception e) {
            log.error("查看所有收入记录时出现错误", e);
            return "查看所有收入记录时出现错误: " + e.getMessage();
        }
    }

    /**
     * 查询金刚到家软件收入总额
     */
    public String getJingangIncomeSummary(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            log.info("查询金刚到家软件收入 - 时间范围: {}", timeRange);
            log.info("查询金刚到家软件收入 - 开始时间: {}", startTime);
            log.info("查询金刚到家软件收入 - 结束时间: {}", endTime);

            List<Object[]> resultList = companyTransactionRepository.findIncomeSummary(startTime, endTime);

            if (resultList != null && !resultList.isEmpty()) {
                Object[] result = resultList.get(0);
                if (result != null && result.length >= 1) {
                    log.info("查询金刚到家软件收入 - 查询结果原始数据: {}", java.util.Arrays.toString(result));

                // 检查result[0]是否是数组
                if (result[0] instanceof Object[]) {
                    Object[] innerArray = (Object[]) result[0];
                    log.info("查询金刚到家软件收入 - 内部数组: 总金额={}, 记录数={}",
                            innerArray.length > 0 ? innerArray[0] : "无",
                            innerArray.length > 1 ? innerArray[1] : "无");
                } else {
                    log.info("查询金刚到家软件收入 - 单个值: {}", result[0]);
                }
            } else {
                log.info("查询金刚到家软件收入 - 查询结果: null 或空数组");
            }

            if (result != null) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                Long recordCount = 0L;

                try {
                    log.info("查询结果数组长度: {}, 内容: {}", result.length, java.util.Arrays.toString(result));

                    // 检查result[0]是否包含聚合结果
                    if (result.length >= 1 && result[0] != null) {
                        Object firstElement = result[0];
                        log.info("第一个元素类型: {}, 值: {}",
                                firstElement.getClass().getName(), firstElement);

                        // 如果第一个元素是数组，说明聚合结果在数组中
                        if (firstElement instanceof Object[]) {
                            Object[] innerArray = (Object[]) firstElement;
                            log.info("内部数组长度: {}, 内容: {}", innerArray.length, java.util.Arrays.toString(innerArray));

                            // 从内部数组获取总金额和记录数
                            if (innerArray.length >= 1 && innerArray[0] != null) {
                                Object amountObj = innerArray[0];
                                if (amountObj instanceof BigDecimal) {
                                    totalAmount = (BigDecimal) amountObj;
                                } else if (amountObj instanceof Number) {
                                    totalAmount = new BigDecimal(amountObj.toString());
                                }
                                log.info("解析出总金额: {}", totalAmount);
                            }

                            if (innerArray.length >= 2 && innerArray[1] != null) {
                                Object countObj = innerArray[1];
                                if (countObj instanceof Number) {
                                    recordCount = ((Number) countObj).longValue();
                                }
                                log.info("解析出记录数: {}", recordCount);
                            }
                        } else {
                            // 如果第一个元素不是数组，尝试直接解析
                            if (firstElement instanceof BigDecimal) {
                                totalAmount = (BigDecimal) firstElement;
                            } else if (firstElement instanceof Number) {
                                totalAmount = new BigDecimal(firstElement.toString());
                            }
                            log.info("直接解析总金额: {}", totalAmount);
                        }
                    }
                } catch (Exception e) {
                    log.error("金刚到家收入数据类型转换异常: {}", e.getMessage(), e);
                    totalAmount = BigDecimal.ZERO;
                    recordCount = 0L;
                }

                if (recordCount > 0) {
                    return String.format("**金刚到家软件收入统计（%s）**\n\n" +
                            "💰 **收入总额**: %,.2f元\n" +
                            "📊 **交易笔数**: %d笔\n" +
                            "💵 **平均交易金额**: %,.2f元\n\n" +
                            "📅 **查询时间**: %s 至 %s\n" +
                            "✅ **数据来源**: 金刚到家MySQL数据库",
                            timeRange,
                            totalAmount,
                            recordCount,
                            totalAmount.divide(BigDecimal.valueOf(recordCount), 2, RoundingMode.HALF_UP),
                            startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                            endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                } else {
                    return String.format("**金刚到家软件收入统计（%s）**\n\n" +
                            "💰 **收入总额**: 0.00元\n" +
                            "📊 **交易笔数**: 0笔\n\n" +
                            "暂无相关收入数据。", timeRange);
                }
            } else {
                return String.format("**金刚到家软件收入统计（%s）**\n\n" +
                        "💰 **收入总额**: 0.00元\n" +
                        "📊 **交易笔数**: 0笔\n\n" +
                        "查询结果异常，请稍后重试。", timeRange);
            }
        } catch (Exception e) {
            log.error("查询金刚到家软件收入时出现错误", e);
            return "查询金刚到家软件收入时出现错误: " + e.getMessage();
        }
    }

    /**
     * 查询金刚到家软件收入明细
     */
    public String getJingangIncomeDetails(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            log.info("查询金刚到家软件收入明细 - 时间范围: {}", timeRange);

            List<Object[]> results = companyTransactionRepository.findIncomeDetails(startTime, endTime);

            log.info("查询金刚到家软件收入明细 - 查询结果数量: {}", results.size());
            if (!results.isEmpty()) {
                log.info("=== 明细查询数据结构调试 ===");
                for (int i = 0; i < Math.min(3, results.size()); i++) {
                    Object[] record = results.get(i);
                    log.info("记录{} - 数组长度: {}, 完整内容: {}", i+1,
                            record != null ? record.length : 0,
                            record != null ? java.util.Arrays.toString(record) : "null");

                    if (record != null) {
                        for (int j = 0; j < record.length; j++) {
                            Object item = record[j];
                            log.info("  record[{}]: {} (类型: {})", j, item,
                                    item != null ? item.getClass().getSimpleName() : "null");
                        }
                    }
                }
                log.info("=== 数据结构调试结束 ===");
            }

            if (results.isEmpty()) {
                return String.format("**金刚到家软件收入明细（%s）**\n\n暂无相关收入数据。", timeRange);
            }

            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**金刚到家软件收入明细（%s）**\n\n", timeRange));

            BigDecimal totalAmount = BigDecimal.ZERO;
            int count = 0;

            for (int i = 0; i < Math.min(20, results.size()); i++) {
                Object[] row = results.get(i);

                // 安全检查数组长度
                if (row == null || row.length < 4) {
                    log.warn("明细记录{}数据不完整，跳过处理: 长度={}", i+1,
                            row != null ? row.length : 0);
                    continue;
                }

                log.info("收入明细第{}条数据: {}", i+1, java.util.Arrays.toString(row));

                try {
                    LocalDateTime transactionTime = null;
                    BigDecimal amount = BigDecimal.ZERO;
                    String description = "";
                    String remark = "";

                    // 根据实际数据结构提取数据
                    if (row.length >= 4) {
                        // 标准情况：数据分布在不同位置
                        log.info("使用标准数据提取方式 - 4个字段分别在不同位置");

                        // 获取交易时间
                        if (row[0] instanceof LocalDateTime) {
                            transactionTime = (LocalDateTime) row[0];
                        } else if (row[0] != null) {
                            log.warn("交易时间类型不正确: {}", row[0].getClass().getName());
                        }

                        // 获取金额
                        if (row[1] != null) {
                            if (row[1] instanceof BigDecimal) {
                                amount = (BigDecimal) row[1];
                            } else if (row[1] instanceof Number) {
                                amount = new BigDecimal(row[1].toString());
                            }
                        }

                        // 获取描述和备注
                        description = row[2] != null ? row[2].toString() : "";
                        remark = row[3] != null ? row[3].toString() : "";

                    } else if (row.length >= 1 && row[0] != null) {
                        // 特殊情况：所有数据都在row[0]中
                        log.info("检测到特殊数据结构 - 尝试从row[0]中提取数据");
                        Object dataContainer = row[0];

                        if (dataContainer instanceof Object[]) {
                            Object[] innerArray = (Object[]) dataContainer;
                            log.info("row[0]是数组，长度: {}, 内容: {}", innerArray.length, java.util.Arrays.toString(innerArray));

                            if (innerArray.length >= 4) {
                                // 从内部数组提取数据
                                if (innerArray[0] instanceof LocalDateTime) {
                                    transactionTime = (LocalDateTime) innerArray[0];
                                }
                                if (innerArray[1] instanceof BigDecimal) {
                                    amount = (BigDecimal) innerArray[1];
                                } else if (innerArray[1] instanceof Number) {
                                    amount = new BigDecimal(innerArray[1].toString());
                                }
                                description = innerArray[2] != null ? innerArray[2].toString() : "";
                                remark = innerArray[3] != null ? innerArray[3].toString() : "";
                            }
                        } else {
                            log.warn("row[0]不是数组类型: {}", dataContainer.getClass().getName());
                            continue;
                        }
                    } else {
                        log.warn("数据格式不符合预期，跳过此记录");
                        continue;
                    }

                    totalAmount = totalAmount.add(amount);
                    count++;

                    if (transactionTime != null) {
                        sb.append(String.format("**第%d笔** - %s\n", i + 1,
                                transactionTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))));
                    } else {
                        sb.append(String.format("**第%d笔** - 时间未知\n", i + 1));
                    }
                    sb.append(String.format("    💰 金额: %,.2f元\n", amount));
                    if (!description.isEmpty()) {
                        sb.append(String.format("    📝 描述: %s\n", description));
                    }
                    if (!remark.isEmpty()) {
                        sb.append(String.format("    💬 备注: %s\n", remark));
                    }
                    sb.append("\n");

                } catch (Exception e) {
                    log.error("处理收入明细数据异常: {}", e.getMessage());
                }
            }

            sb.append(String.format("📊 **汇总统计**:\n"));
            sb.append(String.format("    总收入: %,.2f元\n", totalAmount));
            sb.append(String.format("    总笔数: %d笔\n", count));
            sb.append(String.format("📅 **查询时间**: %s 至 %s\n",
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

            return sb.toString();
        } catch (Exception e) {
            log.error("查询金刚到家软件收入明细时出现错误", e);
            return "查询金刚到家软件收入明细时出现错误: " + e.getMessage();
        }
    }

    /**
     * 查询金刚到家软件收入分类统计
     */
    public String getJingangIncomeByCategory(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            List<Object[]> results = companyTransactionRepository.findIncomeByCategory(startTime, endTime);

            log.info("查询金刚到家软件收入分类统计 - 查询结果数量: {}", results.size());
            if (!results.isEmpty()) {
                log.info("分类统计结果:");
                for (int i = 0; i < Math.min(5, results.size()); i++) {
                    Object[] record = results.get(i);
                    if (record != null && record.length >= 3) {
                        log.info("  业务类型={}, 总金额={}, 记录数={}", record[0], record[1], record[2]);
                    } else {
                        log.warn("  记录{}数据不完整: 长度={}, 内容={}", i,
                                record != null ? record.length : 0,
                                record != null ? java.util.Arrays.toString(record) : "null");
                    }
                }
            }

            if (results.isEmpty()) {
                return String.format("**金刚到家软件收入分类统计（%s）**\n\n暂无相关收入数据。", timeRange);
            }

            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**金刚到家软件收入分类统计（%s）**\n\n", timeRange));

            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalCount = 0;

            for (int i = 0; i < Math.min(10, results.size()); i++) {
                Object[] row = results.get(i);

                // 安全检查数组长度
                if (row == null || row.length < 3) {
                    log.warn("分类统计记录{}数据不完整，跳过处理", i);
                    continue;
                }

                String businessType = row[0] != null ? row[0].toString() : "未分类";
                BigDecimal categoryAmount = BigDecimal.ZERO;
                Long categoryCount = 0L;

                try {
                    // 安全地获取金额（第二个值）
                    if (row.length >= 2 && row[1] != null) {
                        if (row[1] instanceof BigDecimal) {
                            categoryAmount = (BigDecimal) row[1];
                        } else if (row[1] instanceof Number) {
                            categoryAmount = new BigDecimal(row[1].toString());
                        }
                    }

                    // 安全地获取数量（第三个值）
                    if (row.length >= 3 && row[2] != null) {
                        if (row[2] instanceof Number) {
                            categoryCount = ((Number) row[2]).longValue();
                        }
                    }
                } catch (Exception e) {
                    log.error("处理分类统计数据异常: {}", e.getMessage());
                }

                totalAmount = totalAmount.add(categoryAmount);
                totalCount += categoryCount;

                sb.append(String.format("🏷️ **%s**\n", businessType));
                sb.append(String.format("    收入金额: %,.2f元\n", categoryAmount));
                sb.append(String.format("    交易笔数: %d笔\n", categoryCount));
                sb.append(String.format("    平均金额: %,.2f元\n\n", 
                        categoryCount > 0 ? categoryAmount.divide(BigDecimal.valueOf(categoryCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO));
            }

            sb.append(String.format("📊 **汇总统计**:\n"));
            sb.append(String.format("    总收入: %,.2f元\n", totalAmount));
            sb.append(String.format("    总笔数: %d笔\n", totalCount));

            return sb.toString();
        } catch (Exception e) {
            log.error("查询金刚到家软件收入分类统计时出现错误", e);
            return "查询金刚到家软件收入分类统计时出现错误: " + e.getMessage();
        }
    }

    /**
     * 格式化交易明细数据
     */
    private List<Map<String, Object>> formatTransactionDetails(List<Object[]> details) {
        return details.stream()
                .map(detail -> {
                    Map<String, Object> transactionDetail = new HashMap<>();
                    transactionDetail.put("交易时间", detail[0] != null ? detail[0].toString() : "");
                    transactionDetail.put("交易金额", detail[1] != null ? (BigDecimal) detail[1] : BigDecimal.ZERO);
                    transactionDetail.put("交易描述", detail[2] != null ? detail[2].toString() : "");
                    transactionDetail.put("交易备注", detail[3] != null ? detail[3].toString() : "");
                    return transactionDetail;
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算目标完成情况
     */
    private void calculateTargetCompletion(Map<String, Object> result, BigDecimal actualAmount,
                                         LocalDate startDate, LocalDate endDate) {

        // 空值检查，确保actualAmount不为null
        if (actualAmount == null) {
            actualAmount = BigDecimal.ZERO;
        }

        // 判断查询时间范围类型
        boolean isMonthlyQuery = isMonthlyPeriod(startDate, endDate);

        if (isMonthlyQuery) {
            // 月度目标完成率计算
            BigDecimal completionRate = actualAmount.divide(MONTHLY_TARGET, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            BigDecimal remainingTarget = MONTHLY_TARGET.subtract(actualAmount);

            result.put("monthlyTarget", MONTHLY_TARGET);
            result.put("completionRate", completionRate);
            result.put("remainingTarget", remainingTarget.max(BigDecimal.ZERO));
            result.put("isTargetAchieved", actualAmount.compareTo(MONTHLY_TARGET) >= 0);

            log.info("金刚软件月度目标完成情况 - 目标: {}, 实际: {}, 完成率: {}%, 剩余: {}",
                    MONTHLY_TARGET, actualAmount, completionRate, remainingTarget);
        } else {
            // 非月度查询，按比例计算目标
            BigDecimal proportionalTarget = calculateProportionalTarget(startDate, endDate);
            if (proportionalTarget.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal completionRate = actualAmount.divide(proportionalTarget, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                BigDecimal remainingTarget = proportionalTarget.subtract(actualAmount);

                // 期间目标信息
                result.put("proportionalTarget", proportionalTarget);
                result.put("completionRate", completionRate);
                result.put("remainingTarget", remainingTarget.max(BigDecimal.ZERO));
                result.put("isTargetAchieved", actualAmount.compareTo(proportionalTarget) >= 0);

                // 同时返回总目标信息
                result.put("monthlyTarget", MONTHLY_TARGET);
                BigDecimal monthlyCompletionRate = actualAmount.divide(MONTHLY_TARGET, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                BigDecimal monthlyRemainingTarget = MONTHLY_TARGET.subtract(actualAmount);
                result.put("monthlyCompletionRate", monthlyCompletionRate);
                result.put("monthlyRemainingTarget", monthlyRemainingTarget.max(BigDecimal.ZERO));
                result.put("isMonthlyTargetAchieved", actualAmount.compareTo(MONTHLY_TARGET) >= 0);

                // 计算查询天数和月度天数信息
                long queryDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                long monthDays = startDate.lengthOfMonth();
                result.put("queryDays", queryDays);
                result.put("monthDays", monthDays);
                result.put("targetRatio", BigDecimal.valueOf(queryDays).divide(BigDecimal.valueOf(monthDays), 4, RoundingMode.HALF_UP));

                log.info("金刚软件比例目标完成情况 - 期间目标: {}, 月度目标: {}, 实际: {}, 期间完成率: {}%, 月度完成率: {}%",
                        proportionalTarget, MONTHLY_TARGET, actualAmount, completionRate, monthlyCompletionRate);
            }
        }
    }

    /**
     * 判断是否为月度查询
     */
    private boolean isMonthlyPeriod(LocalDate startDate, LocalDate endDate) {
        return startDate.getDayOfMonth() == 1 &&
               endDate.equals(startDate.withDayOfMonth(startDate.lengthOfMonth()));
    }

    /**
     * 计算比例目标
     */
    private BigDecimal calculateProportionalTarget(LocalDate startDate, LocalDate endDate) {
        long queryDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        long monthDays = startDate.lengthOfMonth();

        return MONTHLY_TARGET.multiply(BigDecimal.valueOf(queryDays))
            .divide(BigDecimal.valueOf(monthDays), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
