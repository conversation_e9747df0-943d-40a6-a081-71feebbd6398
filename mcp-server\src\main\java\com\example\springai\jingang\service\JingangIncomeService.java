package com.example.springai.jingang.service;

import com.example.springai.jingang.repository.CompanyTransactionRepository;
import com.example.springai.util.DateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 金刚到家软件收入查询服务
 * 
 * <AUTHOR>
 */
@Service
@Transactional("jingangTransactionManager")
public class JingangIncomeService {

    private static final Logger log = LoggerFactory.getLogger(JingangIncomeService.class);

    // 每月目标金额：21万元
    private static final BigDecimal MONTHLY_TARGET = new BigDecimal("210000");

    @Autowired
    private CompanyTransactionRepository companyTransactionRepository;

    /**
     * 测试方法 - 查看表结构
     */
    public String testTableStructure() {
        try {
            log.info("开始查看company_transaction表结构");

            // 查看表结构
            List<Object[]> columns = companyTransactionRepository.showTableColumns();
            StringBuilder sb = new StringBuilder();
            sb.append("**company_transaction表结构**\n\n");

            for (Object[] column : columns) {
                if (column != null && column.length >= 6) {
                    sb.append(String.format("字段: %s, 类型: %s, 是否为空: %s, 键: %s, 默认值: %s, 额外: %s\n",
                        column[0], column[1], column[2], column[3], column[4], column[5]));
                    log.info("字段信息: {}", java.util.Arrays.toString(column));
                } else {
                    log.warn("字段信息不完整: 长度={}", column != null ? column.length : 0);
                }
            }

            // 查看样本数据
            List<Object[]> samples = companyTransactionRepository.findSampleRecords();
            sb.append("\n**样本数据**\n\n");

            for (int i = 0; i < Math.min(3, samples.size()); i++) {
                Object[] row = samples.get(i);
                if (row != null && row.length >= 6) {
                    sb.append(String.format("记录%d: ID=%s, 类型=%s, 时间=%s, 金额=%s, 业务类型=%s, 描述=%s\n",
                            i+1, row[0], row[1], row[2], row[3], row[4], row[5]));
                    log.info("样本数据{}: ID={}, 类型={}, 时间={}, 金额={}, 业务类型={}, 描述={}",
                            i+1, row[0], row[1], row[2], row[3], row[4], row[5]);
                } else {
                    sb.append(String.format("记录%d: 数据不完整\n", i+1));
                    log.warn("样本数据{}不完整: 长度={}", i+1, row != null ? row.length : 0);
                }
            }

            return sb.toString();

        } catch (Exception e) {
            log.error("查看表结构时出现错误", e);
            return "查看表结构时出现错误: " + e.getMessage();
        }
    }

    /**
     * 测试方法 - 查看所有收入记录
     */
    public String testAllIncomeRecords() {
        try {
            log.info("开始查看所有收入记录");

            // 查看所有收入汇总
            Object[] summary = companyTransactionRepository.findAllIncomeSummary();
            log.info("所有收入汇总: {}", summary != null ? java.util.Arrays.toString(summary) : "null");

            // 查看所有收入记录
            List<Object[]> records = companyTransactionRepository.findAllIncomeRecords();
            log.info("所有收入记录数量: {}", records.size());

            StringBuilder sb = new StringBuilder();
            sb.append("**所有金刚到家软件收入记录**\n\n");

            if (summary != null && summary.length >= 1) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                Long totalCount = 0L;

                try {
                    // 处理汇总数据的类型转换问题
                    log.info("汇总数据原始值: {}", java.util.Arrays.toString(summary));

                    // 检查summary[0]是否包含聚合结果
                    if (summary[0] != null) {
                        Object firstElement = summary[0];
                        log.info("汇总第一个元素类型: {}, 值: {}",
                                firstElement.getClass().getName(), firstElement);

                        // 如果第一个元素是数组，说明聚合结果在数组中
                        if (firstElement instanceof Object[]) {
                            Object[] innerArray = (Object[]) firstElement;
                            log.info("汇总内部数组长度: {}, 内容: {}", innerArray.length, java.util.Arrays.toString(innerArray));

                            if (innerArray.length >= 1 && innerArray[0] != null) {
                                Object amountObj = innerArray[0];
                                if (amountObj instanceof BigDecimal) {
                                    totalAmount = (BigDecimal) amountObj;
                                } else if (amountObj instanceof Number) {
                                    totalAmount = new BigDecimal(amountObj.toString());
                                }
                            }

                            if (innerArray.length >= 2 && innerArray[1] != null) {
                                Object countObj = innerArray[1];
                                if (countObj instanceof Number) {
                                    totalCount = ((Number) countObj).longValue();
                                }
                            }
                        } else {
                            // 如果第一个元素不是数组，尝试直接解析
                            if (firstElement instanceof BigDecimal) {
                                totalAmount = (BigDecimal) firstElement;
                            } else if (firstElement instanceof Number) {
                                totalAmount = new BigDecimal(firstElement.toString());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("汇总数据转换异常: {}", e.getMessage());
                }

                sb.append(String.format("💰 **总收入**: %,.2f元\n", totalAmount));
                sb.append(String.format("📊 **总笔数**: %d笔\n\n", totalCount));
            }

            sb.append("**详细记录**:\n\n");

            for (int i = 0; i < Math.min(10, records.size()); i++) {
                Object[] row = records.get(i);
                log.info("收入记录{}: {}", i+1, java.util.Arrays.toString(row));

                try {
                    String transactionTime = row.length > 0 && row[0] != null ? row[0].toString() : "";
                    String transactionType = row.length > 1 && row[1] != null ? row[1].toString() : "";
                    String amount = row.length > 2 && row[2] != null ? row[2].toString() : "";
                    String businessType = row.length > 3 && row[3] != null ? row[3].toString() : "";
                    String description = row.length > 4 && row[4] != null ? row[4].toString() : "";

                    sb.append(String.format("**第%d笔**\n", i + 1));
                    sb.append(String.format("    时间: %s\n", transactionTime));
                    sb.append(String.format("    类型: %s\n", transactionType));
                    sb.append(String.format("    金额: %s元\n", amount));
                    sb.append(String.format("    业务: %s\n", businessType));
                    sb.append(String.format("    描述: %s\n\n", description));

                } catch (Exception e) {
                    log.error("处理收入记录异常: {}", e.getMessage());
                }
            }

            return sb.toString();

        } catch (Exception e) {
            log.error("查看所有收入记录时出现错误", e);
            return "查看所有收入记录时出现错误: " + e.getMessage();
        }
    }

    /**
     * 查询金刚到家软件收入总额
     */
    public String getJingangIncomeSummary(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            log.info("查询金刚到家软件收入 - 时间范围: {}", timeRange);
            log.info("查询金刚到家软件收入 - 开始时间: {}", startTime);
            log.info("查询金刚到家软件收入 - 结束时间: {}", endTime);

            Object[] result = companyTransactionRepository.findIncomeSummary(startTime, endTime);

            if (result != null && result.length >= 1) {
                log.info("查询金刚到家软件收入 - 查询结果原始数据: {}", java.util.Arrays.toString(result));

                // 检查result[0]是否是数组
                if (result[0] instanceof Object[]) {
                    Object[] innerArray = (Object[]) result[0];
                    log.info("查询金刚到家软件收入 - 内部数组: 总金额={}, 记录数={}",
                            innerArray.length > 0 ? innerArray[0] : "无",
                            innerArray.length > 1 ? innerArray[1] : "无");
                } else {
                    log.info("查询金刚到家软件收入 - 单个值: {}", result[0]);
                }
            } else {
                log.info("查询金刚到家软件收入 - 查询结果: null 或空数组");
            }

            if (result != null) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                Long recordCount = 0L;

                try {
                    log.info("查询结果数组长度: {}, 内容: {}", result.length, java.util.Arrays.toString(result));

                    // 检查result[0]是否包含聚合结果
                    if (result.length >= 1 && result[0] != null) {
                        Object firstElement = result[0];
                        log.info("第一个元素类型: {}, 值: {}",
                                firstElement.getClass().getName(), firstElement);

                        // 如果第一个元素是数组，说明聚合结果在数组中
                        if (firstElement instanceof Object[]) {
                            Object[] innerArray = (Object[]) firstElement;
                            log.info("内部数组长度: {}, 内容: {}", innerArray.length, java.util.Arrays.toString(innerArray));

                            // 从内部数组获取总金额和记录数
                            if (innerArray.length >= 1 && innerArray[0] != null) {
                                Object amountObj = innerArray[0];
                                if (amountObj instanceof BigDecimal) {
                                    totalAmount = (BigDecimal) amountObj;
                                } else if (amountObj instanceof Number) {
                                    totalAmount = new BigDecimal(amountObj.toString());
                                }
                                log.info("解析出总金额: {}", totalAmount);
                            }

                            if (innerArray.length >= 2 && innerArray[1] != null) {
                                Object countObj = innerArray[1];
                                if (countObj instanceof Number) {
                                    recordCount = ((Number) countObj).longValue();
                                }
                                log.info("解析出记录数: {}", recordCount);
                            }
                        } else {
                            // 如果第一个元素不是数组，尝试直接解析
                            if (firstElement instanceof BigDecimal) {
                                totalAmount = (BigDecimal) firstElement;
                            } else if (firstElement instanceof Number) {
                                totalAmount = new BigDecimal(firstElement.toString());
                            }
                            log.info("直接解析总金额: {}", totalAmount);
                        }
                    }
                } catch (Exception e) {
                    log.error("金刚到家收入数据类型转换异常: {}", e.getMessage(), e);
                    totalAmount = BigDecimal.ZERO;
                    recordCount = 0L;
                }

                if (recordCount > 0) {
                    return String.format("**金刚到家软件收入统计（%s）**\n\n" +
                            "💰 **收入总额**: %,.2f元\n" +
                            "📊 **交易笔数**: %d笔\n" +
                            "💵 **平均交易金额**: %,.2f元\n\n" +
                            "📅 **查询时间**: %s 至 %s\n" +
                            "✅ **数据来源**: 金刚到家MySQL数据库",
                            timeRange,
                            totalAmount,
                            recordCount,
                            totalAmount.divide(BigDecimal.valueOf(recordCount), 2, RoundingMode.HALF_UP),
                            startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                            endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                } else {
                    return String.format("**金刚到家软件收入统计（%s）**\n\n" +
                            "💰 **收入总额**: 0.00元\n" +
                            "📊 **交易笔数**: 0笔\n\n" +
                            "暂无相关收入数据。", timeRange);
                }
            } else {
                return String.format("**金刚到家软件收入统计（%s）**\n\n" +
                        "💰 **收入总额**: 0.00元\n" +
                        "📊 **交易笔数**: 0笔\n\n" +
                        "查询结果异常，请稍后重试。", timeRange);
            }
        } catch (Exception e) {
            log.error("查询金刚到家软件收入时出现错误", e);
            return "查询金刚到家软件收入时出现错误: " + e.getMessage();
        }
    }

    /**
     * 查询金刚到家软件收入明细
     */
    public String getJingangIncomeDetails(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            log.info("查询金刚到家软件收入明细 - 时间范围: {}", timeRange);

            List<Object[]> results = companyTransactionRepository.findIncomeDetails(startTime, endTime);

            log.info("查询金刚到家软件收入明细 - 查询结果数量: {}", results.size());
            if (!results.isEmpty()) {
                log.info("前3条明细记录:");
                for (int i = 0; i < Math.min(3, results.size()); i++) {
                    Object[] record = results.get(i);
                    if (record != null && record.length >= 4) {
                        log.info("  记录{}: 时间={}, 金额={}, 描述={}, 备注={}",
                                i+1, record[0], record[1], record[2], record[3]);
                    } else {
                        log.warn("  记录{}数据不完整: 长度={}, 内容={}", i+1,
                                record != null ? record.length : 0,
                                record != null ? java.util.Arrays.toString(record) : "null");
                    }
                }
            }

            if (results.isEmpty()) {
                return String.format("**金刚到家软件收入明细（%s）**\n\n暂无相关收入数据。", timeRange);
            }

            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**金刚到家软件收入明细（%s）**\n\n", timeRange));

            BigDecimal totalAmount = BigDecimal.ZERO;
            int count = 0;

            for (int i = 0; i < Math.min(20, results.size()); i++) {
                Object[] row = results.get(i);

                // 安全检查数组长度
                if (row == null || row.length < 4) {
                    log.warn("明细记录{}数据不完整，跳过处理: 长度={}", i+1,
                            row != null ? row.length : 0);
                    continue;
                }

                log.info("收入明细第{}条数据: {}", i+1, java.util.Arrays.toString(row));

                try {
                    LocalDateTime transactionTime = null;
                    BigDecimal amount = BigDecimal.ZERO;
                    String description = "";
                    String remark = "";

                    // 安全地获取交易时间
                    if (row[0] instanceof LocalDateTime) {
                        transactionTime = (LocalDateTime) row[0];
                    } else if (row[0] != null) {
                        log.warn("交易时间类型不正确: {}", row[0].getClass().getName());
                        continue;
                    }

                    // 安全地获取金额
                    if (row.length >= 2 && row[1] != null) {
                        if (row[1] instanceof BigDecimal) {
                            amount = (BigDecimal) row[1];
                        } else if (row[1] instanceof Number) {
                            amount = new BigDecimal(row[1].toString());
                        }
                    }

                    // 安全地获取描述和备注
                    if (row.length >= 3) {
                        description = row[2] != null ? row[2].toString() : "";
                    }
                    if (row.length >= 4) {
                        remark = row[3] != null ? row[3].toString() : "";
                    }

                    totalAmount = totalAmount.add(amount);
                    count++;

                    if (transactionTime != null) {
                        sb.append(String.format("**第%d笔** - %s\n", i + 1,
                                transactionTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))));
                    } else {
                        sb.append(String.format("**第%d笔** - 时间未知\n", i + 1));
                    }
                    sb.append(String.format("    💰 金额: %,.2f元\n", amount));
                    if (!description.isEmpty()) {
                        sb.append(String.format("    📝 描述: %s\n", description));
                    }
                    if (!remark.isEmpty()) {
                        sb.append(String.format("    💬 备注: %s\n", remark));
                    }
                    sb.append("\n");

                } catch (Exception e) {
                    log.error("处理收入明细数据异常: {}", e.getMessage());
                }
            }

            sb.append(String.format("📊 **汇总统计**:\n"));
            sb.append(String.format("    总收入: %,.2f元\n", totalAmount));
            sb.append(String.format("    总笔数: %d笔\n", count));
            sb.append(String.format("📅 **查询时间**: %s 至 %s\n",
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

            return sb.toString();
        } catch (Exception e) {
            log.error("查询金刚到家软件收入明细时出现错误", e);
            return "查询金刚到家软件收入明细时出现错误: " + e.getMessage();
        }
    }

    /**
     * 查询金刚到家软件收入分类统计
     */
    public String getJingangIncomeByCategory(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            List<Object[]> results = companyTransactionRepository.findIncomeByCategory(startTime, endTime);

            log.info("查询金刚到家软件收入分类统计 - 查询结果数量: {}", results.size());
            if (!results.isEmpty()) {
                log.info("分类统计结果:");
                for (int i = 0; i < Math.min(5, results.size()); i++) {
                    Object[] record = results.get(i);
                    if (record != null && record.length >= 3) {
                        log.info("  业务类型={}, 总金额={}, 记录数={}", record[0], record[1], record[2]);
                    } else {
                        log.warn("  记录{}数据不完整: 长度={}, 内容={}", i,
                                record != null ? record.length : 0,
                                record != null ? java.util.Arrays.toString(record) : "null");
                    }
                }
            }

            if (results.isEmpty()) {
                return String.format("**金刚到家软件收入分类统计（%s）**\n\n暂无相关收入数据。", timeRange);
            }

            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**金刚到家软件收入分类统计（%s）**\n\n", timeRange));

            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalCount = 0;

            for (int i = 0; i < Math.min(10, results.size()); i++) {
                Object[] row = results.get(i);

                // 安全检查数组长度
                if (row == null || row.length < 3) {
                    log.warn("分类统计记录{}数据不完整，跳过处理", i);
                    continue;
                }

                String businessType = row[0] != null ? row[0].toString() : "未分类";
                BigDecimal categoryAmount = BigDecimal.ZERO;
                Long categoryCount = 0L;

                try {
                    // 安全地获取金额（第二个值）
                    if (row.length >= 2 && row[1] != null) {
                        if (row[1] instanceof BigDecimal) {
                            categoryAmount = (BigDecimal) row[1];
                        } else if (row[1] instanceof Number) {
                            categoryAmount = new BigDecimal(row[1].toString());
                        }
                    }

                    // 安全地获取数量（第三个值）
                    if (row.length >= 3 && row[2] != null) {
                        if (row[2] instanceof Number) {
                            categoryCount = ((Number) row[2]).longValue();
                        }
                    }
                } catch (Exception e) {
                    log.error("处理分类统计数据异常: {}", e.getMessage());
                }

                totalAmount = totalAmount.add(categoryAmount);
                totalCount += categoryCount;

                sb.append(String.format("🏷️ **%s**\n", businessType));
                sb.append(String.format("    收入金额: %,.2f元\n", categoryAmount));
                sb.append(String.format("    交易笔数: %d笔\n", categoryCount));
                sb.append(String.format("    平均金额: %,.2f元\n\n", 
                        categoryCount > 0 ? categoryAmount.divide(BigDecimal.valueOf(categoryCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO));
            }

            sb.append(String.format("📊 **汇总统计**:\n"));
            sb.append(String.format("    总收入: %,.2f元\n", totalAmount));
            sb.append(String.format("    总笔数: %d笔\n", totalCount));

            return sb.toString();
        } catch (Exception e) {
            log.error("查询金刚到家软件收入分类统计时出现错误", e);
            return "查询金刚到家软件收入分类统计时出现错误: " + e.getMessage();
        }
    }
}
