<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务查询测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .query-section {
            margin-bottom: 30px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .quick-queries {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .quick-query {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .quick-query:hover {
            background-color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 业务查询测试工具</h1>
        
        <div class="query-section">
            <label for="queryInput">输入查询内容：</label>
            <input type="text" id="queryInput" placeholder="例如：本月平台收入" value="本月平台收入概览">
            <button onclick="executeQuery()">🚀 执行查询</button>
            
            <div class="quick-queries">
                <div class="quick-query" onclick="setQuery('本月平台收入')">本月平台收入</div>
                <div class="quick-query" onclick="setQuery('平台收入概览')">平台收入概览</div>
                <div class="quick-query" onclick="setQuery('门店业绩排名')">门店业绩排名</div>
                <div class="quick-query" onclick="setQuery('总业绩统计')">总业绩统计</div>
                <div class="quick-query" onclick="setQuery('销售概览')">销售概览</div>
            </div>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function setQuery(query) {
            document.getElementById('queryInput').value = query;
        }

        async function executeQuery() {
            const query = document.getElementById('queryInput').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!query) {
                alert('请输入查询内容');
                return;
            }
            
            // 显示加载状态
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在查询中，请稍候...';
            
            try {
                const response = await fetch(`http://localhost:8081/mcp/tools/business-query?query=${encodeURIComponent(query)}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // 显示结果
                resultDiv.className = 'result success';
                if (data.success) {
                    resultDiv.textContent = data.data || '查询成功，但没有返回数据';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `查询失败: ${data.message || '未知错误'}`;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }
        
        // 支持回车键执行查询
        document.getElementById('queryInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                executeQuery();
            }
        });
    </script>
</body>
</html>
