package com.example.springai.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * DateTimeUtil测试类
 * 验证自定义时间区间解析功能
 */
public class DateTimeUtilTest {

    @Test
    @DisplayName("测试标准日期格式解析")
    public void testStandardDateFormats() {
        // 测试 2025-7-1到2025-7-10
        LocalDateTime[] result1 = DateTimeUtil.parseDateRange("2025-7-1到2025-7-10");
        assertNotNull(result1);
        assertEquals(2025, result1[0].getYear());
        assertEquals(7, result1[0].getMonthValue());
        assertEquals(1, result1[0].getDayOfMonth());
        assertEquals(LocalTime.MIN, result1[0].toLocalTime()); // 00:00:00
        
        assertEquals(2025, result1[1].getYear());
        assertEquals(7, result1[1].getMonthValue());
        assertEquals(10, result1[1].getDayOfMonth());
        assertEquals(LocalTime.MAX, result1[1].toLocalTime()); // 23:59:59
        
        // 测试 2025-07-01到2025-07-10
        LocalDateTime[] result2 = DateTimeUtil.parseDateRange("2025-07-01到2025-07-10");
        assertNotNull(result2);
        assertEquals(result1[0].toLocalDate(), result2[0].toLocalDate());
        assertEquals(result1[1].toLocalDate(), result2[1].toLocalDate());
        
        // 测试 2025.7.1到2025.7.10
        LocalDateTime[] result3 = DateTimeUtil.parseDateRange("2025.7.1到2025.7.10");
        assertNotNull(result3);
        assertEquals(result1[0].toLocalDate(), result3[0].toLocalDate());
        assertEquals(result1[1].toLocalDate(), result3[1].toLocalDate());
    }

    @Test
    @DisplayName("测试中文年份格式解析")
    public void testChineseYearFormats() {
        // 测试 2025年7.1号到7.10号
        LocalDateTime[] result1 = DateTimeUtil.parseDateRange("2025年7.1号到7.10号");
        assertNotNull(result1);
        assertEquals(2025, result1[0].getYear());
        assertEquals(7, result1[0].getMonthValue());
        assertEquals(1, result1[0].getDayOfMonth());
        assertEquals(10, result1[1].getDayOfMonth());
        
        // 测试 2025年7月1日到7月10日
        LocalDateTime[] result2 = DateTimeUtil.parseDateRange("2025年7月1日到7月10日");
        assertNotNull(result2);
        assertEquals(result1[0].toLocalDate(), result2[0].toLocalDate());
        assertEquals(result1[1].toLocalDate(), result2[1].toLocalDate());
        
        // 测试 2025年7.1到2025年7.10
        LocalDateTime[] result3 = DateTimeUtil.parseDateRange("2025年7.1到2025年7.10");
        assertNotNull(result3);
        assertEquals(result1[0].toLocalDate(), result3[0].toLocalDate());
        assertEquals(result1[1].toLocalDate(), result3[1].toLocalDate());
    }

    @Test
    @DisplayName("测试当年简化格式解析")
    public void testCurrentYearFormats() {
        int currentYear = LocalDateTime.now().getYear();
        
        // 测试 7.1到7.10
        LocalDateTime[] result1 = DateTimeUtil.parseDateRange("7.1到7.10");
        assertNotNull(result1);
        assertEquals(currentYear, result1[0].getYear());
        assertEquals(7, result1[0].getMonthValue());
        assertEquals(1, result1[0].getDayOfMonth());
        assertEquals(10, result1[1].getDayOfMonth());
        
        // 测试 7月1日到7月10日
        LocalDateTime[] result2 = DateTimeUtil.parseDateRange("7月1日到7月10日");
        assertNotNull(result2);
        assertEquals(result1[0].toLocalDate(), result2[0].toLocalDate());
        assertEquals(result1[1].toLocalDate(), result2[1].toLocalDate());
    }

    @Test
    @DisplayName("测试分隔符兼容性")
    public void testSeparatorCompatibility() {
        LocalDateTime[] expected = DateTimeUtil.parseDateRange("2025-7-1到2025-7-10");
        
        // 测试不同分隔符
        String[] separators = {"至", "－", "~", "～"};
        for (String sep : separators) {
            String input = "2025-7-1" + sep + "2025-7-10";
            LocalDateTime[] result = DateTimeUtil.parseDateRange(input);
            assertNotNull(result, "分隔符 " + sep + " 解析失败");
            assertEquals(expected[0].toLocalDate(), result[0].toLocalDate());
            assertEquals(expected[1].toLocalDate(), result[1].toLocalDate());
        }
        
        // 测试前缀词
        String[] prefixes = {"从", "自"};
        for (String prefix : prefixes) {
            String input = prefix + "2025-7-1到2025-7-10";
            LocalDateTime[] result = DateTimeUtil.parseDateRange(input);
            assertNotNull(result, "前缀词 " + prefix + " 解析失败");
            assertEquals(expected[0].toLocalDate(), result[0].toLocalDate());
            assertEquals(expected[1].toLocalDate(), result[1].toLocalDate());
        }
    }

    @Test
    @DisplayName("测试日期交换功能")
    public void testDateSwapping() {
        // 测试结束日期早于开始日期的情况
        LocalDateTime[] result = DateTimeUtil.parseDateRange("2025-7-10到2025-7-1");
        assertNotNull(result);
        assertEquals(1, result[0].getDayOfMonth()); // 应该自动交换为开始日期
        assertEquals(10, result[1].getDayOfMonth()); // 应该自动交换为结束日期
    }

    @Test
    @DisplayName("测试时间精度")
    public void testTimePrecision() {
        LocalDateTime[] result = DateTimeUtil.parseDateRange("2025-7-1到2025-7-10");
        assertNotNull(result);
        
        // 验证开始时间为00:00:00
        assertEquals(0, result[0].getHour());
        assertEquals(0, result[0].getMinute());
        assertEquals(0, result[0].getSecond());
        
        // 验证结束时间为23:59:59
        assertEquals(23, result[1].getHour());
        assertEquals(59, result[1].getMinute());
        assertEquals(59, result[1].getSecond());
    }

    @Test
    @DisplayName("测试标准时间范围兼容性")
    public void testStandardTimeRanges() {
        // 确保标准时间范围仍然正常工作
        String[] standardRanges = {"本月", "上个月", "本周", "上周", "今年", "最近7天", "最近30天", "昨天", "今天"};
        
        for (String range : standardRanges) {
            LocalDateTime[] result = DateTimeUtil.parseDateRange(range);
            assertNotNull(result, "标准时间范围 " + range + " 解析失败");
            assertTrue(result[0].isBefore(result[1]) || result[0].isEqual(result[1]), 
                      "开始时间应该不晚于结束时间: " + range);
        }
    }

    @Test
    @DisplayName("测试无效格式处理")
    public void testInvalidFormats() {
        // 测试无效格式应该回退到默认（本月）
        LocalDateTime[] defaultResult = DateTimeUtil.parseDateRange("本月");
        LocalDateTime[] invalidResult = DateTimeUtil.parseDateRange("无效的时间格式");
        
        assertNotNull(invalidResult);
        assertEquals(defaultResult[0].toLocalDate(), invalidResult[0].toLocalDate());
        assertEquals(defaultResult[1].toLocalDate(), invalidResult[1].toLocalDate());
    }

    @Test
    @DisplayName("测试跨月查询")
    public void testCrossMonthQuery() {
        // 测试跨月查询
        LocalDateTime[] result = DateTimeUtil.parseDateRange("2025-6-25到2025-7-5");
        assertNotNull(result);
        assertEquals(6, result[0].getMonthValue());
        assertEquals(25, result[0].getDayOfMonth());
        assertEquals(7, result[1].getMonthValue());
        assertEquals(5, result[1].getDayOfMonth());
    }

    @Test
    @DisplayName("测试跨年查询")
    public void testCrossYearQuery() {
        // 测试跨年查询
        LocalDateTime[] result = DateTimeUtil.parseDateRange("2024-12-25到2025-1-5");
        assertNotNull(result);
        assertEquals(2024, result[0].getYear());
        assertEquals(12, result[0].getMonthValue());
        assertEquals(25, result[0].getDayOfMonth());
        assertEquals(2025, result[1].getYear());
        assertEquals(1, result[1].getMonthValue());
        assertEquals(5, result[1].getDayOfMonth());
    }
}
