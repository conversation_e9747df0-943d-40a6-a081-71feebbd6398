package com.example.springai.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * MCP协议错误信息
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpError {
    
    /**
     * 错误代码
     */
    @JsonProperty("code")
    private int code;
    
    /**
     * 错误消息
     */
    @JsonProperty("message")
    private String message;
    
    /**
     * 错误详细数据
     */
    @JsonProperty("data")
    private Object data;
    
    /**
     * 标准JSON-RPC错误代码
     */
    public static class ErrorCode {
        public static final int PARSE_ERROR = -32700;
        public static final int INVALID_REQUEST = -32600;
        public static final int METHOD_NOT_FOUND = -32601;
        public static final int INVALID_PARAMS = -32602;
        public static final int INTERNAL_ERROR = -32603;
        
        // MCP特定错误代码
        public static final int TOOL_NOT_FOUND = -32000;
        public static final int RESOURCE_NOT_FOUND = -32001;
        public static final int UNAUTHORIZED = -32002;
        public static final int RATE_LIMITED = -32003;
    }
    
    /**
     * 创建解析错误
     */
    public static McpError parseError(String message) {
        return new McpError(ErrorCode.PARSE_ERROR, message, null);
    }
    
    /**
     * 创建无效请求错误
     */
    public static McpError invalidRequest(String message) {
        return new McpError(ErrorCode.INVALID_REQUEST, message, null);
    }
    
    /**
     * 创建方法未找到错误
     */
    public static McpError methodNotFound(String method) {
        return new McpError(ErrorCode.METHOD_NOT_FOUND, "Method not found: " + method, null);
    }
    
    /**
     * 创建无效参数错误
     */
    public static McpError invalidParams(String message) {
        return new McpError(ErrorCode.INVALID_PARAMS, message, null);
    }
    
    /**
     * 创建内部错误
     */
    public static McpError internalError(String message) {
        return new McpError(ErrorCode.INTERNAL_ERROR, message, null);
    }
    
    /**
     * 创建工具未找到错误
     */
    public static McpError toolNotFound(String toolName) {
        return new McpError(ErrorCode.TOOL_NOT_FOUND, "Tool not found: " + toolName, null);
    }
    
    /**
     * 创建资源未找到错误
     */
    public static McpError resourceNotFound(String resourceUri) {
        return new McpError(ErrorCode.RESOURCE_NOT_FOUND, "Resource not found: " + resourceUri, null);
    }
}
