package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 新品成交数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface NewProductSalesRepository extends JpaRepository<Order, Long> {

    /**
     * 查询新品成交统计数据
     * 根据SQL：
     * SELECT
     *   (SELECT name FROM Product WHERE productid = id) AS 产品名称,
     *   COUNT(1) AS 订单数,
     *   SUM(RealTotalAmount) AS 营业额,
     *   (SELECT ProductType FROM Product WHERE productid = id) AS 类别
     * FROM [Order]
     * WHERE CreateTime BETWEEN ? and ?
     *   AND RealTotalAmount = Amount
     *   AND OrderState <> 99
     *   AND productid IN (SELECT id FROM Product WHERE (Lable = 688 OR ProductCategoryId = 47))
     * GROUP BY productid
     * ORDER BY COUNT(1) DESC
     */
    @Query(value = """
        SELECT 
            p.name AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.RealTotalAmount) AS totalRevenue,
            p.ProductType AS productType,
            o.productId AS productId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.CreateTime BETWEEN :startTime AND :endTime
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState <> 99
            AND p.id IN (SELECT id FROM Product WHERE (Lable = 688 OR ProductCategoryId = 47))
        GROUP BY o.productId, p.name, p.ProductType
        ORDER BY COUNT(o.id) DESC
        """, nativeQuery = true)
    List<Object[]> getNewProductSalesStatistics(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询新品成交统计数据（按订单数排序）
     */
    @Query(value = """
        SELECT 
            p.name AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.RealTotalAmount) AS totalRevenue,
            p.ProductType AS productType,
            o.productId AS productId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.CreateTime BETWEEN :startTime AND :endTime
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState <> 99
            AND p.id IN (SELECT id FROM Product WHERE (Lable = 688 OR ProductCategoryId = 47))
        GROUP BY o.productId, p.name, p.ProductType
        ORDER BY COUNT(o.id) DESC
        """, nativeQuery = true)
    List<Object[]> getNewProductSalesRankingByOrderCount(@Param("startTime") LocalDateTime startTime, 
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询新品成交统计数据（按营业额排序）
     */
    @Query(value = """
        SELECT 
            p.name AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.RealTotalAmount) AS totalRevenue,
            p.ProductType AS productType,
            o.productId AS productId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.CreateTime BETWEEN :startTime AND :endTime
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState <> 99
            AND p.id IN (SELECT id FROM Product WHERE (Lable = 688 OR ProductCategoryId = 47))
        GROUP BY o.productId, p.name, p.ProductType
        ORDER BY SUM(o.RealTotalAmount) DESC
        """, nativeQuery = true)
    List<Object[]> getNewProductSalesRankingByRevenue(@Param("startTime") LocalDateTime startTime, 
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询新品成交总体统计
     */
    @Query(value = """
        SELECT 
            COUNT(DISTINCT o.productId) AS productCount,
            COUNT(o.id) AS totalOrderCount,
            SUM(o.RealTotalAmount) AS totalRevenue
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.CreateTime BETWEEN :startTime AND :endTime
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState <> 99
            AND p.id IN (SELECT id FROM Product WHERE (Lable = 688 OR ProductCategoryId = 47))
        """, nativeQuery = true)
    Object[] getNewProductSalesOverview(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
}
