package com.example.springai.service;

// 暂时注释掉Spring AI相关的导入，避免编译错误
// import org.springframework.ai.image.ImageResponse;

/**
 * 通义千问AI服务接口
 * 提供文本对话、图像生成、语音合成等功能
 * 
 * <AUTHOR> AI Alibaba Demo
 * @version 1.0.0
 */
public interface TongYiService {

    /**
     * 基本文本对话
     * 
     * @param message 用户输入的消息
     * @return AI回复的文本内容
     */
    String completion(String message);

    /**
     * 流式文本对话
     * 
     * @param message 用户输入的消息
     * @return 流式响应的文本内容
     */
    String streamCompletion(String message);

    /**
     * 文生图功能
     *
     * @param prompt 图像生成提示词
     * @return 图像响应对象
     */
    // 暂时注释掉，避免编译错误
    // ImageResponse generateImage(String prompt);
    String generateImage(String prompt);

    /**
     * 语音合成功能
     * 
     * @param text 需要转换为语音的文本
     * @return 音频文件的保存路径
     */
    String generateSpeech(String text);
}
