# IDEA调试专用配置
server:
  port: 8080

# 日志配置
logging:
  level:
    com.example.springai: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    com.zaxxer.hikari: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Spring AI Alibaba 配置
spring:
  ai:
    alibaba:
      api-key: ${ALIBABA_API_KEY:sk-your-api-key}
      base-url: https://dashscope.aliyuncs.com/api/v1/
      chat:
        options:
          model: qwen-plus
          temperature: 0.7
          max-tokens: 2000
      image:
        options:
          model: wanx-v1
          size: "1024*1024"
          style: "<auto>"

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  # 主数据源配置 (SQL Server)
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: ********************************************************************************************************************************
    username: xyj2025
    password: Xyj&5170618
    hikari:
      pool-name: PrimaryHikariPool
      maximum-pool-size: 5
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# 应用配置
app:
  name: MCP Server IDEA Debug
  version: 1.0.0
  description: Spring AI Alibaba MCP Server for IDEA Debug Mode
