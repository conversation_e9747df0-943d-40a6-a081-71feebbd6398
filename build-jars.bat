@echo off
echo ==========================================
echo    MCP项目打包脚本
echo ==========================================

echo.
echo 1. 清理项目...
call .\mvnw.cmd clean

echo.
echo 2. 编译项目...
call .\mvnw.cmd compile

echo.
echo 3. 运行测试...
call .\mvnw.cmd test -DskipTests=true

echo.
echo 4. 打包MCP服务器...
call .\mvnw.cmd package -pl mcp-server -DskipTests=true

echo.
echo 5. 打包MCP客户端...
call .\mvnw.cmd package -pl mcp-client -DskipTests=true

echo.
echo 6. 创建部署目录...
if not exist "deploy" mkdir deploy
if not exist "deploy\server" mkdir deploy\server
if not exist "deploy\client" mkdir deploy\client
if not exist "deploy\logs" mkdir deploy\logs
if not exist "deploy\view" mkdir deploy\view

echo.
echo 7. 复制JAR文件...
copy "mcp-server\target\mcp-server-1.0.0.jar" "deploy\server\"
copy "mcp-client\target\mcp-client-1.0.0.jar" "deploy\client\"

echo.
echo 8. 复制HTML页面...
copy "view\*.html" "deploy\view\"

echo.
echo 9. 创建启动脚本...
echo @echo off > deploy\start-server.bat
echo echo 启动MCP服务器... >> deploy\start-server.bat
echo java -jar -Xms512m -Xmx1024m -Dspring.profiles.active=prod server\mcp-server-1.0.0.jar >> deploy\start-server.bat

echo @echo off > deploy\start-client.bat
echo echo 启动MCP客户端... >> deploy\start-client.bat
echo java -jar -Xms256m -Xmx512m -Dspring.profiles.active=prod client\mcp-client-1.0.0.jar >> deploy\start-client.bat

echo @echo off > deploy\start-all.bat
echo echo 启动MCP完整服务... >> deploy\start-all.bat
echo start "MCP Server" cmd /c start-server.bat >> deploy\start-all.bat
echo timeout /t 10 >> deploy\start-all.bat
echo start "MCP Client" cmd /c start-client.bat >> deploy\start-all.bat
echo echo 服务启动完成！ >> deploy\start-all.bat
echo echo MCP服务器: http://localhost:8080 >> deploy\start-all.bat
echo echo MCP客户端: http://localhost:8081 >> deploy\start-all.bat
echo echo AI智能问数页面: 请打开 view\ai-query.html >> deploy\start-all.bat
echo pause >> deploy\start-all.bat

echo.
echo 10. 创建停止脚本...
echo @echo off > deploy\stop-all.bat
echo echo 停止MCP服务... >> deploy\stop-all.bat
echo taskkill /f /im java.exe >> deploy\stop-all.bat
echo echo 服务已停止！ >> deploy\stop-all.bat
echo pause >> deploy\stop-all.bat

echo.
echo ==========================================
echo    打包完成！
echo ==========================================
echo.
echo 部署文件位置: deploy\
echo.
echo 包含文件:
echo   - server\mcp-server-1.0.0.jar     (MCP服务器)
echo   - client\mcp-client-1.0.0.jar     (MCP客户端)
echo   - view\ai-query.html               (AI智能问数页面)
echo   - start-server.bat                 (启动服务器)
echo   - start-client.bat                 (启动客户端)
echo   - start-all.bat                    (启动全部服务)
echo   - stop-all.bat                     (停止全部服务)
echo   - logs\                            (日志目录)
echo.
echo 部署说明:
echo   1. 将 deploy 文件夹复制到服务器
echo   2. 确保服务器安装了 Java 17+
echo   3. 运行 start-all.bat 启动服务
echo   4. 访问 http://服务器IP:8081 使用MCP客户端
echo   5. 打开 view\ai-query.html 使用AI智能问数
echo.
pause
