package com.example.springai.repository;

import com.example.springai.entity.Store;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 门店数据访问接口
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface StoreRepository extends JpaRepository<Store, Long> {
    
    /**
     * 根据门店名称查找门店
     */
    List<Store> findByStoreNameContainingIgnoreCase(String storeName);
    
    /**
     * 查找所有门店（简化版）
     */
    @Query("SELECT s FROM Store s")
    List<Store> findAllStores();
}
