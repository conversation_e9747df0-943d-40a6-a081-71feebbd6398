<!DOCTYPE html>
<html>
<head>
    <title>MCP测试客户端</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>MCP测试客户端</h1>
    <div>
        <button onclick="connectToServer()">连接服务器</button>
        <button onclick="testQuery()">测试查询</button>
        <button onclick="disconnect()">断开连接</button>
    </div>
    <div>
        <h3>连接状态:</h3>
        <div id="status">未连接</div>
    </div>
    <div>
        <h3>查询结果:</h3>
        <div id="result"></div>
    </div>

    <script>
        let ws = null;
        let requestId = 1;

        function connectToServer() {
            try {
                ws = new WebSocket('ws://localhost:8080/mcp');
                
                ws.onopen = function() {
                    document.getElementById('status').innerHTML = '已连接';
                    console.log('WebSocket连接已建立');
                    
                    // 发送初始化请求
                    const initRequest = {
                        jsonrpc: "2.0",
                        id: requestId++,
                        method: "initialize",
                        params: {
                            protocolVersion: "2024-11-05",
                            capabilities: {},
                            clientInfo: {
                                name: "test-client",
                                version: "1.0.0"
                            }
                        }
                    };
                    ws.send(JSON.stringify(initRequest));
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    console.log('收到消息:', message);
                    
                    if (message.id === 1) {
                        // 初始化响应
                        const initNotification = {
                            jsonrpc: "2.0",
                            method: "initialized"
                        };
                        ws.send(JSON.stringify(initNotification));
                        console.log('初始化完成');
                    } else if (message.result && message.result.content) {
                        // 查询结果
                        document.getElementById('result').innerHTML = 
                            '<pre>' + JSON.stringify(message.result.content, null, 2) + '</pre>';
                    }
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    document.getElementById('status').innerHTML = '连接错误';
                };
                
                ws.onclose = function() {
                    console.log('WebSocket连接已关闭');
                    document.getElementById('status').innerHTML = '连接已关闭';
                };
                
            } catch (error) {
                console.error('连接失败:', error);
                document.getElementById('status').innerHTML = '连接失败';
            }
        }

        function testQuery() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('请先连接服务器');
                return;
            }
            
            const queryRequest = {
                jsonrpc: "2.0",
                id: requestId++,
                method: "tools/call",
                params: {
                    name: "business_query",
                    arguments: {
                        query: "上月金刚软件收入"
                    }
                }
            };
            
            ws.send(JSON.stringify(queryRequest));
            console.log('发送查询请求:', queryRequest);
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
    </script>
</body>
</html>
