package com.example.springai.entity;

import com.example.springai.enums.StoreType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 门店实体
 * 映射真实数据库中的Store表
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Entity
@Table(name = "Store")  // 映射真实的Store表
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Store {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 门店名称
     */
    @Column(name = "storeName")
    private String storeName;

    /**
     * 门店类型：0-平台，1-自营，2-加盟，3-定制，4-合伙（共创店），5-承包
     */
    @Column(name = "storeType")
    private Integer storeType;

    /**
     * 获取门店类型枚举
     */
    public StoreType getStoreTypeEnum() {
        return StoreType.fromCode(this.storeType);
    }

    /**
     * 判断是否为共创店（合伙店，storeType = 4）
     */
    public boolean isCoopStore() {
        StoreType type = getStoreTypeEnum();
        return type != null && type.isCoopStore();
    }
    
}
