package com.example.springai.controller;

import com.example.springai.service.TokenService;
import com.example.springai.service.TokenService.TokenResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Token控制器
 * 提供获取阿里云智能语音Token的REST接口
 *
 * <AUTHOR> AI Alibaba MCP
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/aliyun/token")
@CrossOrigin(origins = "*")
public class TokenController {

    private final TokenService tokenService;

    @Autowired
    public TokenController(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    /**
     * 获取阿里云智能语音Token
     * @return Token及过期时间
     */
    @GetMapping("/getToken")
    public ResponseEntity<Map<String, Object>> getToken() {
        log.info("请求获取阿里云Token");
        Map<String, Object> response = new HashMap<>();
        try {
            TokenResult result = tokenService.getToken();
            response.put("success", true);
            response.put("token", result.getToken());
            response.put("expireTime", result.getExpireTime());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.internalServerError().body(response);
        }
    }
} 