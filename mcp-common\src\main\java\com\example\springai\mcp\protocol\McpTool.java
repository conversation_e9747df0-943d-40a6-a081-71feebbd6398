package com.example.springai.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Map;

/**
 * MCP工具定义
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpTool {
    
    /**
     * 工具名称
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 工具描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 输入参数模式（JSON Schema）
     */
    @JsonProperty("inputSchema")
    private Map<String, Object> inputSchema;
    
    /**
     * 创建文本对话工具
     */
    public static McpTool createChatTool() {
        return McpTool.builder()
                .name("ai_chat")
                .description("与AI进行文本对话")
                .inputSchema(Map.of(
                    "type", "object",
                    "properties", Map.of(
                        "message", Map.of(
                            "type", "string",
                            "description", "用户输入的消息"
                        )
                    ),
                    "required", new String[]{"message"}
                ))
                .build();
    }
    
    /**
     * 创建图像生成工具
     */
    public static McpTool createImageTool() {
        return McpTool.builder()
                .name("ai_image")
                .description("根据文本描述生成图像")
                .inputSchema(Map.of(
                    "type", "object",
                    "properties", Map.of(
                        "prompt", Map.of(
                            "type", "string",
                            "description", "图像生成的文本描述"
                        ),
                        "width", Map.of(
                            "type", "integer",
                            "description", "图像宽度",
                            "default", 1024
                        ),
                        "height", Map.of(
                            "type", "integer",
                            "description", "图像高度",
                            "default", 1024
                        )
                    ),
                    "required", new String[]{"prompt"}
                ))
                .build();
    }
    
    /**
     * 创建流式对话工具
     */
    public static McpTool createStreamChatTool() {
        return McpTool.builder()
                .name("ai_stream_chat")
                .description("与AI进行流式文本对话")
                .inputSchema(Map.of(
                    "type", "object",
                    "properties", Map.of(
                        "message", Map.of(
                            "type", "string",
                            "description", "用户输入的消息"
                        )
                    ),
                    "required", new String[]{"message"}
                ))
                .build();
    }

    /**
     * 创建业务查询工具
     */
    public static McpTool createBusinessQueryTool() {
        return McpTool.builder()
                .name("business_query")
                .description("智能业务数据查询，支持门店业绩、销售统计、平台收入、全国三嫂业绩等查询")
                .inputSchema(Map.of(
                    "type", "object",
                    "properties", Map.of(
                        "query", Map.of(
                            "type", "string",
                            "description", "自然语言业务查询，支持以下类型：\n" +
                                         "• 门店业绩：'上周的门店总业绩多少'、'本月销售排名前5的门店'\n" +
                                         "• 平台收入：'本月平台收入总共多少'、'列一个本月平台收入的明细'\n" +
                                         "• 门店平台收入：'王府井旗舰店上个月的平台收入'\n" +
                                         "• 全国三嫂业绩：'本月全国三嫂业绩多少'、'全国三嫂业绩排行前10'、'三嫂产品销售分析'"
                        )
                    ),
                    "required", new String[]{"query"}
                ))
                .build();
    }
}
