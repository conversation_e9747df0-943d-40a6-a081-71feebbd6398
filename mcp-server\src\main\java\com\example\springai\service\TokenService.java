package com.example.springai.service;

/**
 * 阿里云智能语音Token服务接口
 * 提供Token获取功能
 * 
 * <AUTHOR> AI Alibaba MCP
 * @version 1.0.0
 */
public interface TokenService {
    /**
     * 获取阿里云智能语音Token
     * @return Token信息（包含token字符串和过期时间）
     */
    TokenResult getToken();

    /**
     * Token结果对象
     */
    class TokenResult {
        private final String token;
        private final long expireTime;

        public TokenResult(String token, long expireTime) {
            this.token = token;
            this.expireTime = expireTime;
        }

        public String getToken() {
            return token;
        }

        public long getExpireTime() {
            return expireTime;
        }
    }
} 