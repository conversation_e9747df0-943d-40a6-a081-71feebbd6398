@echo off
chcp 65001
echo ==========================================
echo    快速重新编译和启动
echo ==========================================

echo.
echo 1. 停止现有进程...
taskkill /f /im java.exe 2>nul

echo.
echo 2. 编译服务器...
cd mcp-server
javac -cp "target/classes;../mcp-common/target/classes" -d target/classes src/main/java/com/example/springai/service/impl/TongYiServiceImpl.java
javac -cp "target/classes;../mcp-common/target/classes" -d target/classes src/main/java/com/example/springai/service/BusinessAIService.java
cd ..

echo.
echo 3. 编译客户端...
cd mcp-client
javac -cp "target/classes;../mcp-common/target/classes" -d target/classes src/main/java/com/example/springai/mcp/controller/McpController.java
cd ..

echo.
echo 4. 启动服务器...
start "MCP Server" java -jar mcp-server/target/mcp-server-1.0.0.jar

echo.
echo 5. 等待服务器启动...
timeout /t 10

echo.
echo 6. 启动客户端...
start "MCP Client" java -jar mcp-client/target/mcp-client-1.0.0.jar

echo.
echo 7. 等待客户端启动...
timeout /t 5

echo.
echo ==========================================
echo    启动完成！
echo ==========================================
echo.
echo MCP服务器: http://localhost:8080
echo MCP客户端: http://localhost:8081
echo 测试页面: test-business-query.html
echo.
echo 按任意键退出...
pause >nul
