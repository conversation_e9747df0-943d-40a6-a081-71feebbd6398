package com.example.springai.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售记录实体
 * 注意：请根据您的实际数据库表结构调整字段映射
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Entity
@Table(name = "sales_records")  // 请根据实际表名修改
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SalesRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联门店
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private Store store;
    
    /**
     * 销售日期
     */
    @Column(name = "sale_date", nullable = false)
    private LocalDate saleDate;
    
    /**
     * 销售金额
     */
    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;
    
    /**
     * 订单数量
     */
    @Column(name = "order_count", nullable = false)
    private Integer orderCount;
    
    /**
     * 客户数量
     */
    @Column(name = "customer_count")
    private Integer customerCount;
    
    /**
     * 销售类型：ONLINE-线上, OFFLINE-线下, MIXED-混合
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "sale_type")
    private SaleType saleType;
    
    /**
     * 备注
     */
    @Column(name = "remarks")
    private String remarks;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 销售类型枚举
     */
    public enum SaleType {
        ONLINE("线上"),
        OFFLINE("线下"),
        MIXED("混合");
        
        private final String description;
        
        SaleType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
