package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 驻场保洁和开荒集团结算数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface CleaningSettlementRepository extends JpaRepository<Order, Long> {

    /**
     * 查询驻场保洁和开荒集团结算统计数据
     * 基于新的SQL规则：
     * SELECT
     *   t.ProductName AS 产品名称,
     *   t.Amount AS 订单金额,
     *   t.StartTime AS 订单时间
     * FROM [Order] AS t
     * WHERE t.OrderState <> 99
     *   AND t.PaySettlementTime BETWEEN ? AND ?
     *   AND t.ProductId IN (95, 166)
     */
    @Query(value = """
        SELECT
            t.ProductName AS productName,
            COUNT(t.id) AS orderCount,
            SUM(t.Amount) AS totalRevenue,
            '驻场保洁/开荒' AS productType,
            t.ProductId AS productId
        FROM [Order] t
        WHERE t.OrderState <> 99
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
            AND t.ProductId IN (95, 166)
        GROUP BY t.ProductId, t.ProductName
        ORDER BY COUNT(t.id) DESC
        """, nativeQuery = true)
    List<Object[]> getCleaningSettlementStatistics(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询驻场保洁和开荒集团结算排行（按订单数排序）
     */
    @Query(value = """
        SELECT
            t.ProductName AS productName,
            COUNT(t.id) AS orderCount,
            SUM(t.Amount) AS totalRevenue,
            '驻场保洁/开荒' AS productType,
            t.ProductId AS productId
        FROM [Order] t
        WHERE t.OrderState <> 99
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
            AND t.ProductId IN (95, 166)
        GROUP BY t.ProductId, t.ProductName
        ORDER BY COUNT(t.id) DESC
        """, nativeQuery = true)
    List<Object[]> getCleaningSettlementRankingByOrderCount(@Param("startTime") LocalDateTime startTime,
                                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询驻场保洁和开荒集团结算排行（按营业额排序）
     */
    @Query(value = """
        SELECT
            t.ProductName AS productName,
            COUNT(t.id) AS orderCount,
            SUM(t.Amount) AS totalRevenue,
            '驻场保洁/开荒' AS productType,
            t.ProductId AS productId
        FROM [Order] t
        WHERE t.OrderState <> 99
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
            AND t.ProductId IN (95, 166)
        GROUP BY t.ProductId, t.ProductName
        ORDER BY SUM(t.Amount) DESC
        """, nativeQuery = true)
    List<Object[]> getCleaningSettlementRankingByRevenue(@Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询驻场保洁和开荒集团结算总体统计
     */
    @Query(value = """
        SELECT
            COUNT(DISTINCT t.ProductId) AS productCount,
            COUNT(t.id) AS totalOrderCount,
            SUM(t.Amount) AS totalRevenue
        FROM [Order] t
        WHERE t.OrderState <> 99
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
            AND t.ProductId IN (95, 166)
        """, nativeQuery = true)
    List<Object[]> getCleaningSettlementOverview(@Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询驻场保洁和开荒集团结算明细数据
     */
    @Query(value = """
        SELECT
            t.ProductName AS productName,
            t.Amount AS orderAmount,
            t.StartTime AS orderTime
        FROM [Order] t
        WHERE t.OrderState <> 99
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
            AND t.ProductId IN (95, 166)
        ORDER BY t.PaySettlementTime DESC
        """, nativeQuery = true)
    List<Object[]> getCleaningSettlementDetails(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
}
