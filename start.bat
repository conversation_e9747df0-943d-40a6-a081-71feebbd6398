@echo off
chcp 65001 >nul
echo ================================
echo Spring AI Alibaba 项目启动脚本
echo ================================
echo.

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装JDK 17或更高版本
    pause
    exit /b 1
)

echo.
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请确保已安装Maven 3.6或更高版本
    pause
    exit /b 1
)

echo.
echo 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)

echo.
echo 启动应用...
echo 注意：请确保已在application.yml中配置正确的API Key
echo.
mvn spring-boot:run

pause
