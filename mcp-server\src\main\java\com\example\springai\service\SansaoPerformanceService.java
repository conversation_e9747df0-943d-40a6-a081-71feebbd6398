package com.example.springai.service;

import com.example.springai.repository.OrderRepository;
import com.example.springai.util.DateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 全国三嫂业绩查询服务
 */
@Service
public class SansaoPerformanceService {

    private static final Logger log = LoggerFactory.getLogger(SansaoPerformanceService.class);

    @Autowired
    private OrderRepository orderRepository;
    
    /**
     * 查询全国三嫂业绩总额
     */
    public String getSansaoPerformanceSummary(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            // 添加调试日志
            log.info("查询全国三嫂业绩 - 时间范围: {}", timeRange);
            log.info("查询全国三嫂业绩 - 开始时间: {}", startTime);
            log.info("查询全国三嫂业绩 - 结束时间: {}", endTime);

            Object[] resultObjects;
            try {
                resultObjects = orderRepository.findSansaoPerformanceSummary(startTime, endTime);
            } catch (Exception e) {
                log.error("查询全国三嫂业绩数据库连接失败: {}", e.getMessage());
                return String.format("**全国三嫂业绩查询 (%s)**\n\n" +
                        "❌ **数据库连接失败**\n" +
                        "- 错误信息: %s\n" +
                        "- 建议: 请检查数据库连接状态或稍后重试\n\n" +
                        "💡 **提示**: 您可以尝试查询其他数据源的信息",
                        timeRange, e.getMessage().contains("timeout") ? "连接超时" : "连接错误");
            }

            Object[] result = (Object[])resultObjects[0];
            log.info("查询全国三嫂业绩 - 查询结果: {}", result != null ? java.util.Arrays.toString(result) : "null");
            
            if (result != null && result.length >= 2) {
                // 安全的类型转换
                BigDecimal totalAmount;
                Integer orderCount;

                try {
                    // 处理可能的类型转换问题
                    Object amountObj = result[0];
                    Object countObj = result[1];

                    log.info("查询结果类型 - totalAmount: {}, orderCount: {}",
                            amountObj != null ? amountObj.getClass().getName() : "null",
                            countObj != null ? countObj.getClass().getName() : "null");

                    if (amountObj instanceof BigDecimal) {
                        totalAmount = (BigDecimal) amountObj;
                    } else if (amountObj instanceof Number) {
                        totalAmount = new BigDecimal(amountObj.toString());
                    } else {
                        totalAmount = BigDecimal.ZERO;
                    }

                    if (countObj instanceof Number) {
                        orderCount = ((Number) countObj).intValue();
                    } else {
                        orderCount = 0;
                    }
                } catch (Exception e) {
                    log.error("数据类型转换异常: {}", e.getMessage());
                    totalAmount = BigDecimal.ZERO;
                    orderCount = 0;
                }

                // 检查是否有实际的订单数据
                if (orderCount > 0) {
                    return String.format("**全国三嫂业绩统计（%s）**\n\n" +
                            "📊 **业绩总额**: %,.2f元\n" +
                            "📦 **订单数量**: %d笔\n" +
                            "💰 **平均订单金额**: %,.2f元\n\n" +
                            "🏪 **门店类型**: 加盟店(2)、承包店(5)\n" +
                            "📅 **查询时间**: %s 至 %s\n" +
                            "✅ **订单状态**: 已完成(80)、已结算(90)",
                            timeRange,
                            totalAmount,
                            orderCount,
                            totalAmount.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP),
                            startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                            endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                } else {
                    return String.format("**全国三嫂业绩统计（%s）**\n\n" +
                            "📊 **业绩总额**: 0.00元\n" +
                            "📦 **订单数量**: 0笔\n\n" +
                            "暂无相关业绩数据。", timeRange);
                }
            } else {
                return String.format("**全国三嫂业绩统计（%s）**\n\n" +
                        "📊 **业绩总额**: 0.00元\n" +
                        "📦 **订单数量**: 0笔\n\n" +
                        "查询结果异常，请稍后重试。", timeRange);
            }
        } catch (Exception e) {
            return "查询全国三嫂业绩时出现错误: " + e.getMessage();
        }
    }
    
    /**
     * 查询全国三嫂业绩门店排行
     */
    public String getSansaoPerformanceRanking(String timeRange, Integer topN) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];

            log.info("查询全国三嫂业绩门店排行 - 时间范围: {}, 开始时间: {}, 结束时间: {}", timeRange, startTime, endTime);

            List<Object[]> results = orderRepository.findSansaoPerformanceRanking(startTime, endTime);

            log.info("查询全国三嫂业绩门店排行 - 查询结果数量: {}", results.size());

            if (results.isEmpty()) {
                return String.format("**全国三嫂业绩门店排行（%s）**\n\n暂无相关业绩数据。", timeRange);
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**全国三嫂业绩门店排行（%s）**\n\n", timeRange));
            
            int limit = topN != null ? Math.min(topN, results.size()) : Math.min(10, results.size());
            
            for (int i = 0; i < limit; i++) {
                Object[] row = results.get(i);

                log.info("门店排行第{}名数据: {}", i+1, java.util.Arrays.toString(row));

                // 安全的类型转换
                String storeName = row[0] != null ? row[0].toString() : "未知门店";
                Integer storeType = row[1] != null ? ((Number) row[1]).intValue() : 0;
                BigDecimal storeTotal = BigDecimal.ZERO;
                Integer orderCount = 0;

                try {
                    if (row[2] instanceof BigDecimal) {
                        storeTotal = (BigDecimal) row[2];
                    } else if (row[2] instanceof Number) {
                        storeTotal = new BigDecimal(row[2].toString());
                    }

                    if (row[3] instanceof Number) {
                        orderCount = ((Number) row[3]).intValue();
                    }
                } catch (Exception e) {
                    log.error("门店排行数据类型转换异常: {}", e.getMessage());
                }
                
                String storeTypeDesc = getStoreTypeDescription(storeType);
                String medal = getMedal(i + 1);
                
                sb.append(String.format("%s **第%d名**: %s (%s)\n", 
                        medal, i + 1, storeName, storeTypeDesc));
                sb.append(String.format("   💰 业绩金额: %,.2f元\n", storeTotal));
                sb.append(String.format("   📦 订单数量: %d笔\n", orderCount));
                sb.append(String.format("   💵 平均订单: %,.2f元\n\n", 
                        orderCount > 0 ? storeTotal.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO));
            }
            
            sb.append(String.format("📅 **查询时间**: %s 至 %s\n",
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
            
            return sb.toString();
        } catch (Exception e) {
            return "查询全国三嫂业绩排行时出现错误: " + e.getMessage();
        }
    }
    
    /**
     * 查询全国三嫂业绩产品分析
     */
    public String getSansaoPerformanceByProduct(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];
            
            List<Object[]> results = orderRepository.findSansaoPerformanceByProduct(startTime, endTime);
            
            if (results.isEmpty()) {
                return String.format("**全国三嫂业绩产品分析（%s）**\n\n暂无相关业绩数据。", timeRange);
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**全国三嫂业绩产品分析（%s）**\n\n", timeRange));
            
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalOrders = 0;
            
            for (int i = 0; i < Math.min(10, results.size()); i++) {
                Object[] row = results.get(i);
                String productName = (String) row[0];
                BigDecimal productTotal = (BigDecimal) row[1];
                Integer orderCount = ((Number) row[2]).intValue();
                
                totalAmount = totalAmount.add(productTotal);
                totalOrders += orderCount;
                
                sb.append(String.format("🛍️ **%s**\n", productName));
                sb.append(String.format("   💰 销售金额: %,.2f元\n", productTotal));
                sb.append(String.format("   📦 订单数量: %d笔\n", orderCount));
                sb.append(String.format("   💵 平均单价: %,.2f元\n\n", 
                        orderCount > 0 ? productTotal.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO));
            }
            
            sb.append(String.format("📊 **汇总统计**:\n"));
            sb.append(String.format("   💰 总销售额: %,.2f元\n", totalAmount));
            sb.append(String.format("   📦 总订单数: %d笔\n", totalOrders));
            sb.append(String.format("📅 **查询时间**: %s 至 %s\n",
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
            
            return sb.toString();
        } catch (Exception e) {
            return "查询全国三嫂业绩产品分析时出现错误: " + e.getMessage();
        }
    }
    
    /**
     * 查询全国三嫂业绩趋势分析
     */
    public String getSansaoPerformanceTrend(String timeRange) {
        try {
            LocalDateTime[] dateRange = DateTimeUtil.parseDateRange(timeRange);
            LocalDateTime startTime = dateRange[0];
            LocalDateTime endTime = dateRange[1];
            
            List<Object[]> results = orderRepository.findSansaoPerformanceByDate(startTime, endTime);
            
            if (results.isEmpty()) {
                return String.format("**全国三嫂业绩趋势分析（%s）**\n\n暂无相关业绩数据。", timeRange);
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("**全国三嫂业绩趋势分析（%s）**\n\n", timeRange));
            
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalOrders = 0;
            
            for (int i = 0; i < Math.min(15, results.size()); i++) {
                Object[] row = results.get(i);
                String orderDate = row[0].toString();
                BigDecimal dailyTotal = (BigDecimal) row[1];
                Integer orderCount = ((Number) row[2]).intValue();
                
                totalAmount = totalAmount.add(dailyTotal);
                totalOrders += orderCount;
                
                sb.append(String.format("📅 **%s**\n", orderDate));
                sb.append(String.format("   💰 当日业绩: %,.2f元\n", dailyTotal));
                sb.append(String.format("   📦 订单数量: %d笔\n\n", orderCount));
            }
            
            sb.append(String.format("📊 **期间汇总**:\n"));
            sb.append(String.format("   💰 总业绩: %,.2f元\n", totalAmount));
            sb.append(String.format("   📦 总订单: %d笔\n", totalOrders));
            sb.append(String.format("   📈 日均业绩: %,.2f元\n", 
                    results.size() > 0 ? totalAmount.divide(BigDecimal.valueOf(results.size()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO));
            
            return sb.toString();
        } catch (Exception e) {
            return "查询全国三嫂业绩趋势时出现错误: " + e.getMessage();
        }
    }
    
    /**
     * 获取门店类型描述
     */
    private String getStoreTypeDescription(Integer storeType) {
        switch (storeType) {
            case 0: return "平台";
            case 1: return "自营";
            case 2: return "加盟";
            case 3: return "定制";
            case 4: return "合伙";
            case 5: return "承包";
            default: return "未知";
        }
    }
    
    /**
     * 获取排名奖牌
     */
    private String getMedal(int rank) {
        switch (rank) {
            case 1: return "🥇";
            case 2: return "🥈";
            case 3: return "🥉";
            default: return "🏅";
        }
    }
}
