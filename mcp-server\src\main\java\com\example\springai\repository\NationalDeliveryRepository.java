package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 全国交付业绩数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface NationalDeliveryRepository extends JpaRepository<Order, Long> {

    /**
     * 查询全国交付业绩统计数据
     * 基于SQL：
     * SELECT
     *   t.RealTotalAmount AS 订单金额,
     *   t.ProductName AS 产品名称,
     *   t.CreateTime AS 订单时间,
     *   (SELECT ProductType FROM Product WHERE productid = id) AS 类别
     * FROM [Order] AS t
     * WHERE t.StoreId = 1038
     *   AND t.OrderState <> 99
     *   AND t.PaySettlementTime BETWEEN ? AND ?
     */
    @Query(value = """
        SELECT
            p.name AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.RealTotalAmount) AS totalRevenue,
            p.ProductType AS productType,
            o.productId AS productId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.StoreId = 1038
            AND o.OrderState <> 99
            AND o.PaySettlementTime BETWEEN :startTime AND :endTime
        GROUP BY o.productId, p.name, p.ProductType
        ORDER BY COUNT(o.id) DESC
        """, nativeQuery = true)
    List<Object[]> getNationalDeliveryStatistics(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询全国交付业绩排行（按订单数排序）
     */
    @Query(value = """
        SELECT
            p.name AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.RealTotalAmount) AS totalRevenue,
            p.ProductType AS productType,
            o.productId AS productId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.StoreId = 1038
            AND o.OrderState <> 99
            AND o.PaySettlementTime BETWEEN :startTime AND :endTime
        GROUP BY o.productId, p.name, p.ProductType
        ORDER BY COUNT(o.id) DESC
        """, nativeQuery = true)
    List<Object[]> getNationalDeliveryRankingByOrderCount(@Param("startTime") LocalDateTime startTime, 
                                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询全国交付业绩排行（按营业额排序）
     */
    @Query(value = """
        SELECT
            p.name AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.RealTotalAmount) AS totalRevenue,
            p.ProductType AS productType,
            o.productId AS productId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.StoreId = 1038
            AND o.OrderState <> 99
            AND o.PaySettlementTime BETWEEN :startTime AND :endTime
        GROUP BY o.productId, p.name, p.ProductType
        ORDER BY SUM(o.RealTotalAmount) DESC
        """, nativeQuery = true)
    List<Object[]> getNationalDeliveryRankingByRevenue(@Param("startTime") LocalDateTime startTime, 
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询全国交付业绩总体统计
     */
    @Query(value = """
        SELECT 
            COUNT(DISTINCT o.productId) AS productCount,
            COUNT(o.id) AS totalOrderCount,
            SUM(o.RealTotalAmount) AS totalRevenue
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.StoreId = 1038
            AND o.OrderState <> 99
            AND o.PaySettlementTime BETWEEN :startTime AND :endTime
        """, nativeQuery = true)
    Object[] getNationalDeliveryOverview(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询全国交付业绩明细数据
     */
    @Query(value = """
        SELECT
            o.RealTotalAmount AS orderAmount,
            p.name AS productName,
            o.CreateTime AS orderTime,
            p.ProductType AS productType,
            o.id AS orderId
        FROM [Order] o
        INNER JOIN Product p ON o.productId = p.id
        WHERE o.StoreId = 1038
            AND o.OrderState <> 99
            AND o.PaySettlementTime BETWEEN :startTime AND :endTime
        ORDER BY o.PaySettlementTime DESC
        """, nativeQuery = true)
    List<Object[]> getNationalDeliveryDetails(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);
}
