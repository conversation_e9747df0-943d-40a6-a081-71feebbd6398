@echo off
chcp 65001
echo ==========================================
echo    MCP Project Build Script
echo ==========================================

echo.
echo 1. Clean project...
call .\mvnw.cmd clean

echo.
echo 2. Package MCP Server...
call .\mvnw.cmd package -pl mcp-server -DskipTests=true

echo.
echo 3. Package MCP Client...
call .\mvnw.cmd package -pl mcp-client -DskipTests=true

echo.
echo 4. Create deploy directory...
if not exist "deploy" mkdir deploy
if not exist "deploy\server" mkdir deploy\server
if not exist "deploy\client" mkdir deploy\client
if not exist "deploy\logs" mkdir deploy\logs
if not exist "deploy\view" mkdir deploy\view

echo.
echo 5. Copy JAR files...
copy "mcp-server\target\mcp-server-1.0.0.jar" "deploy\server\"
copy "mcp-client\target\mcp-client-1.0.0.jar" "deploy\client\"

echo.
echo 6. Copy HTML files...
copy "view\*.html" "deploy\view\"

echo.
echo 7. Create startup scripts...
echo @echo off > deploy\start-server.bat
echo java -jar -Xms512m -Xmx1024m -Dspring.profiles.active=prod server\mcp-server-1.0.0.jar >> deploy\start-server.bat

echo @echo off > deploy\start-client.bat
echo java -jar -Xms256m -Xmx512m -Dspring.profiles.active=prod client\mcp-client-1.0.0.jar >> deploy\start-client.bat

echo @echo off > deploy\start-all.bat
echo echo Starting MCP Services... >> deploy\start-all.bat
echo start "MCP Server" cmd /c start-server.bat >> deploy\start-all.bat
echo timeout /t 10 >> deploy\start-all.bat
echo start "MCP Client" cmd /c start-client.bat >> deploy\start-all.bat
echo echo Services started! >> deploy\start-all.bat
echo echo MCP Server: http://localhost:8080 >> deploy\start-all.bat
echo echo MCP Client: http://localhost:8081 >> deploy\start-all.bat
echo pause >> deploy\start-all.bat

echo @echo off > deploy\stop-all.bat
echo taskkill /f /im java.exe >> deploy\stop-all.bat
echo echo Services stopped! >> deploy\stop-all.bat
echo pause >> deploy\stop-all.bat

echo.
echo ==========================================
echo    Build Complete!
echo ==========================================
echo.
echo Deploy files location: deploy\
echo.
echo Files included:
echo   - server\mcp-server-1.0.0.jar
echo   - client\mcp-client-1.0.0.jar
echo   - view\ai-query.html
echo   - start-server.bat
echo   - start-client.bat
echo   - start-all.bat
echo   - stop-all.bat
echo.
echo To deploy:
echo   1. Copy deploy folder to server
echo   2. Ensure Java 17+ is installed
echo   3. Run start-all.bat
echo   4. Access http://server-ip:8081
echo.
pause
