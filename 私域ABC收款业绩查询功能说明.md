# 私域ABC收款业绩查询功能说明

## 🎯 功能概述

新增私域ABC收款业绩数据查询服务，基于复杂的Payment和Order表关联查询，支持自然语言查询私域ABC收款业绩统计，并包含目标完成率计算功能。

## 📊 数据源说明

### SQL基础查询（复杂联合查询）
```sql
SELECT
    t1.amount + t2.amounts as '私域ABC收款业绩'
FROM
    (
    SELECT SUM(o.Amount) AS amount 
    FROM
        (SELECT OrderNo, Amount FROM Payment 
         WHERE PaymentStatus = '2' 
           AND PayTime BETWEEN '2025/7/1' AND '2025/7/10 23:59' 
           AND OrderNo IS NOT NULL) t
        LEFT JOIN [Order] o ON t.OrderNo = o.id 
    WHERE
        o.storeid IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5)) 
        AND o.productid NOT IN (SELECT id FROM Product WHERE ProductCategoryid IN (17, 18, 19, 20, 27, 37, 22, 23)) 
        AND o.storeid NOT IN (88, 89) 
    ) t1
    LEFT JOIN (
    SELECT SUM(amount) amounts 
    FROM Payment 
    WHERE
        PaymentStatus = '2' 
        AND PayTime BETWEEN '2025/7/1' AND '2025/7/10 23:59:59' 
        AND OrderNo IS NULL 
        AND memberid NOT IN (
        SELECT memberid 
        FROM
            (
            SELECT *,
                ROW_NUMBER() OVER (PARTITION BY memberid ORDER BY CreateTime DESC) AS rn 
            FROM [Order] 
            WHERE memberid IN (
                SELECT DISTINCT memberid FROM Payment 
                WHERE PaymentStatus = '2' 
                  AND PayTime BETWEEN '2025/7/1' AND '2025/7/10 23:59:59' 
                  AND OrderNo IS NULL
            ) 
            ) a 
        WHERE a.rn = 1 
          AND storeid NOT IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5)) 
        ) 
    ) t2 ON 1 = 1
```

### 查询逻辑说明
**两部分收款统计：**
1. **有订单号的支付** (t1) - 关联Order表，过滤特定门店类型和产品类别
2. **无订单号的支付** (t2) - 直接统计Payment表，排除特定会员

### 查询条件
- **PaymentStatus = '2'** - 支付成功状态
- **PayTime** - 基于支付时间范围
- **StoreType IN (0, 1, 5)** - 特定门店类型
- **排除产品类别** - ProductCategoryid IN (17, 18, 19, 20, 27, 37, 22, 23)
- **排除门店** - storeid NOT IN (88, 89)

## 🎯 目标管理功能

### 月度目标设置
- **固定月度目标：621万元**
- **自动计算完成率**
- **剩余目标金额计算**
- **目标达成状态判断**

### 目标计算逻辑
```java
// 月度目标
MONTHLY_TARGET = 6,210,000元

// 完成率计算
completionRate = (实际收款 / 目标金额) × 100%

// 剩余目标
remainingTarget = 目标金额 - 实际收款

// 比例目标（非整月查询）
proportionalTarget = 月度目标 × (查询天数 / 当月总天数)
```

## 🔍 支持的查询类型

### 1. 基础统计查询
**触发关键词：** "私域"、"ABC"、"收款" + "业绩"、"收入"、"统计"

**查询示例：**
- "本月私域ABC收款业绩"
- "上周私域收款统计"
- "私域ABC收款情况"
- "私域收款目标完成率"

**返回内容：**
- 总收款金额（订单收款 + 直接收款）
- 支付笔数统计
- 目标完成率
- 剩余目标金额

### 2. 趋势分析查询
**触发关键词：** "趋势"、"每日"、"日统计"

**查询示例：**
- "私域收款趋势"
- "私域ABC每日收款统计"
- "私域收款日统计"

**返回内容：**
- 每日收款明细
- 趋势分析
- 总体统计

### 3. 时间范围支持
- **今天、昨天**
- **本周、上周**
- **本月、上月**
- **近7天、近30天**
- **自定义时间范围**

## 📋 返回数据格式

### 统计查询返回示例
```
**私域ABC收款业绩统计**

查询时间：30天

**📊 收款业绩概况**
• **总收款金额：100.00万元** 💰
• 订单收款：85.50万元 📦
• 直接收款：14.50万元 💳
• **总支付笔数：1,256笔** 📊
• 订单支付：1,089笔
• 直接支付：167笔

**🎯 目标完成情况**
• **月度目标：621.00万元** 🎯
• **完成率：16.10%** 📈
• **剩余目标：521.00万元** 🚀

• 平均支付金额：796.18元 📊
```

### 目标达成示例
```
**私域ABC收款业绩统计**

查询时间：30天

**📊 收款业绩概况**
• **总收款金额：650.00万元** 💰
• 订单收款：580.00万元 📦
• 直接收款：70.00万元 💳

**🎯 目标完成情况**
• **月度目标：621.00万元** 🎯
• **完成率：104.67%** ✅ 已达成目标！
```

## 🔧 技术架构

### 1. 服务层结构
- **PrivateDomainRepository** - 数据访问层
- **PrivateDomainService** - 业务逻辑层
- **BusinessAIService** - AI集成层

### 2. 核心方法
```java
// 基础统计查询
getPrivateDomainStatistics(startDate, endDate)

// 趋势分析查询
getPrivateDomainTrend(startDate, endDate)

// 总金额查询
getPrivateDomainTotalAmount(startTime, endTime)

// 详细统计查询
getPrivateDomainDetailedStats(startTime, endTime)
```

### 3. 数据处理特性
- **复杂SQL处理** - 支持多表关联和子查询
- **目标计算** - 自动计算完成率和剩余目标
- **货币格式化** - 大金额自动转换为万元显示
- **时间智能** - 自动识别月度查询和比例计算

## 🧪 测试用例

### 正常查询测试
```
✅ "本月私域ABC收款业绩" → 返回本月统计数据和目标完成率
✅ "私域收款目标完成率" → 返回目标完成情况
✅ "上周私域收款统计" → 返回上周数据
✅ "私域ABC收款趋势" → 返回趋势分析
```

### 目标完成率测试
```
✅ 月度查询 → 使用621万月度目标
✅ 非月度查询 → 使用比例目标
✅ 超额完成 → 显示"已达成目标"
✅ 未完成 → 显示剩余目标金额
```

### 不合理查询测试
```
❌ "下月私域收款业绩" → 幽默拒绝
❌ "明天私域ABC收款" → 未来时间拒绝
❌ "去年私域收款统计" → 久远时间提示
```

## 📈 数据统计特性

### 1. 双重收款统计
- **订单收款** - 通过订单关联的支付记录
- **直接收款** - 无订单号的直接支付记录
- **总收款** - 两部分金额相加

### 2. 智能目标管理
- **月度目标** - 固定621万元月度目标
- **比例目标** - 非整月查询按天数比例计算
- **完成率** - 实时计算完成百分比
- **剩余目标** - 自动计算还需完成的金额

### 3. 数据验证
- **复杂过滤** - 多重条件过滤确保数据准确
- **去重处理** - 避免重复统计
- **状态验证** - 只统计成功支付记录

## 🎨 用户体验优化

### 1. 友好的回复格式
- 使用💰💳📦等emoji图标增强视觉效果
- 清晰的层级结构
- 重要数据加粗显示

### 2. 智能查询识别
- 支持多种表达方式
- 自动识别查询意图
- 容错性强的关键词匹配

### 3. 目标可视化
- 完成率百分比显示
- 目标达成状态标识
- 剩余目标金额提醒

## 🔍 查询识别逻辑

### 关键词匹配
```java
// 私域相关关键词
"私域" || "ABC" || "收款"

// 业绩相关关键词  
"业绩" || "收入" || "营收" || "统计" || "完成率" || "目标"

// 排除其他查询
!("三嫂" || "交付" || "新品" || "金刚" || "保洁" || "驻场" || "大学")
```

### 优先级设置
1. 新品成交查询
2. 全国交付业绩查询
3. 驻场保洁集团结算查询
4. 大学收入查询
5. **私域ABC收款业绩查询** ← 新增
6. 三嫂业绩查询
7. 金刚查询

## 🚀 使用示例

### 基础查询
```
用户：本月私域ABC收款业绩
系统：返回本月私域ABC收款统计数据和目标完成率
```

### 目标查询
```
用户：私域收款目标完成率
系统：返回目标完成情况和剩余目标金额
```

### 趋势查询
```
用户：私域收款趋势
系统：返回每日收款趋势和总体统计
```

通过这个私域ABC收款业绩查询功能，用户可以方便地了解私域业务的收款情况和目标完成进度！
