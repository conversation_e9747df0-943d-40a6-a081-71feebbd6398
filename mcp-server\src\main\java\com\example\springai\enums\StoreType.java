package com.example.springai.enums;

/**
 * 门店类型枚举
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
public enum StoreType {
    
    /**
     * 平台
     */
    PLATFORM(0, "平台"),
    
    /**
     * 自营
     */
    SELF_OPERATED(1, "自营"),
    
    /**
     * 加盟（共创店）
     */
    FRANCHISE(2, "加盟"),

    /**
     * 定制
     */
    CUSTOM(3, "定制"),

    /**
     * 合伙
     */
    PARTNERSHIP(4, "合伙"),
    
    /**
     * 承包
     */
    CONTRACT(5, "承包");
    
    private final Integer code;
    private final String description;
    
    StoreType(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取门店类型
     */
    public static StoreType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StoreType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为共创店（加盟店和承包店）
     * 根据业务需求，全国三嫂业绩包含加盟店(2)和承包店(5)
     */
    public boolean isCoopStore() {
        return this == FRANCHISE || this == CONTRACT;
    }

    /**
     * 获取所有共创店类型的代码
     * 全国三嫂业绩查询需要包含加盟店(2)和承包店(5)
     */
    public static Integer[] getCoopStoreCodes() {
        return new Integer[]{FRANCHISE.getCode(), CONTRACT.getCode()};
    }
}
