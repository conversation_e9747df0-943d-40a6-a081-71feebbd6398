import java.net.*;
import java.io.*;

public class TestConnection {
    public static void main(String[] args) {
        String host = "rm-wz9yqo4g4u00p43c3.sqlserver.rds.aliyuncs.com";
        
        // 测试常见的SQL Server端口
        int[] ports = {1433, 1434, 14330, 14331, 14332, 14333};
        
        System.out.println("测试SQL Server连接...");
        System.out.println("主机: " + host);
        System.out.println();
        
        for (int port : ports) {
            testPort(host, port);
        }
        
        // 测试DNS解析
        System.out.println("\n测试DNS解析:");
        try {
            InetAddress addr = InetAddress.getByName(host);
            System.out.println("DNS解析成功: " + addr.getHostAddress());
        } catch (Exception e) {
            System.out.println("DNS解析失败: " + e.getMessage());
        }
    }
    
    private static void testPort(String host, int port) {
        try {
            Socket socket = new Socket();
            socket.connect(new InetSocketAddress(host, port), 5000); // 5秒超时
            socket.close();
            System.out.println("端口 " + port + ": ✅ 连接成功");
        } catch (Exception e) {
            System.out.println("端口 " + port + ": ❌ 连接失败 - " + e.getMessage());
        }
    }
}
