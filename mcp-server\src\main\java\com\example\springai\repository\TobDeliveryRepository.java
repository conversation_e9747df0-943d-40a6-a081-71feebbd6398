package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * TOB交付业绩数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface TobDeliveryRepository extends JpaRepository<Order, Long> {

    /**
     * 查询TOB交付业绩统计数据
     * 基于新的SQL规则：
     * SELECT o.Amount, o.CreateTime, o.ProductName
     * FROM [Order] o LEFT JOIN OrderChannel oc ON o.billno = oc.billno
     * WHERE o.PaySettlementTime BETWEEN ? AND ?
     *   AND oc.Channel LIKE '%292%'
     *   AND OrderState <> 99
     */
    @Query(value = """
        SELECT
            COUNT(*) as totalOrderCount,
            SUM(o.Amount) as totalAmount,
            COUNT(DISTINCT o.ProductName) as productCount
        FROM [Order] o
        LEFT JOIN OrderChannel oc ON o.billno = oc.billno
        WHERE o.PaySettlementTime >= :startTime
          AND o.PaySettlementTime <= :endTime
          AND oc.Channel LIKE '%292%'
          AND o.OrderState <> 99
        """, nativeQuery = true)
    List<Object[]> getTobDeliveryStatistics(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询TOB交付业绩产品明细
     */
    @Query(value = """
        SELECT
            o.ProductName,
            COUNT(*) as orderCount,
            SUM(o.Amount) as totalAmount,
            AVG(o.Amount) as avgAmount,
            MIN(o.Amount) as minAmount,
            MAX(o.Amount) as maxAmount
        FROM [Order] o
        LEFT JOIN OrderChannel oc ON o.billno = oc.billno
        WHERE o.PaySettlementTime >= :startTime
          AND o.PaySettlementTime <= :endTime
          AND oc.Channel LIKE '%292%'
          AND o.OrderState <> 99
        GROUP BY o.ProductName
        ORDER BY SUM(o.Amount) DESC
        """, nativeQuery = true)
    List<Object[]> getTobDeliveryProductDetails(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询TOB交付业绩排行（按金额）
     */
    @Query(value = """
        SELECT
            o.ProductName,
            COUNT(*) as orderCount,
            SUM(o.Amount) as totalAmount
        FROM [Order] o
        LEFT JOIN OrderChannel oc ON o.billno = oc.billno
        WHERE o.PaySettlementTime >= :startTime
          AND o.PaySettlementTime <= :endTime
          AND oc.Channel LIKE '%292%'
          AND o.OrderState <> 99
        GROUP BY o.ProductName
        ORDER BY SUM(o.Amount) DESC
        """, nativeQuery = true)
    List<Object[]> getTobDeliveryRankingByAmount(@Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询TOB交付业绩排行（按订单数）
     */
    @Query(value = """
        SELECT
            o.ProductName,
            COUNT(*) as orderCount,
            SUM(o.Amount) as totalAmount
        FROM [Order] o
        LEFT JOIN OrderChannel oc ON o.billno = oc.billno
        WHERE o.PaySettlementTime >= :startTime
          AND o.PaySettlementTime <= :endTime
          AND oc.Channel LIKE '%292%'
          AND o.OrderState <> 99
        GROUP BY o.ProductName
        ORDER BY COUNT(*) DESC
        """, nativeQuery = true)
    List<Object[]> getTobDeliveryRankingByOrderCount(@Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询TOB交付业绩每日趋势
     */
    @Query(value = """
        SELECT
            CAST(o.PaySettlementTime AS DATE) as settlementDate,
            COUNT(*) as dailyOrderCount,
            SUM(o.Amount) as dailyAmount,
            COUNT(DISTINCT o.ProductName) as dailyProductCount
        FROM [Order] o
        LEFT JOIN OrderChannel oc ON o.billno = oc.billno
        WHERE o.PaySettlementTime >= :startTime
          AND o.PaySettlementTime <= :endTime
          AND oc.Channel LIKE '%292%'
          AND o.OrderState <> 99
        GROUP BY CAST(o.PaySettlementTime AS DATE)
        ORDER BY CAST(o.PaySettlementTime AS DATE) DESC
        """, nativeQuery = true)
    List<Object[]> getTobDeliveryDailyTrend(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询TOB交付业绩原始明细数据
     */
    @Query(value = """
        SELECT
            o.Amount,
            o.CreateTime,
            o.ProductName,
            o.PaySettlementTime,
            o.billno
        FROM [Order] o
        LEFT JOIN OrderChannel oc ON o.billno = oc.billno
        WHERE o.PaySettlementTime >= :startTime
          AND o.PaySettlementTime <= :endTime
          AND oc.Channel LIKE '%292%'
          AND o.OrderState <> 99
        ORDER BY o.PaySettlementTime DESC
        """, nativeQuery = true)
    List<Object[]> getTobDeliveryRawDetails(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
}
