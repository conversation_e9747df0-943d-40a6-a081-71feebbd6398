package com.example.springai;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * MCP服务器应用程序主启动类
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@SpringBootApplication
public class McpServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpServerApplication.class, args);
        System.out.println("==========================================");
        System.out.println("🚀 MCP服务器启动成功！");
        System.out.println("访问地址：http://localhost:8080");
        System.out.println("==========================================");
        System.out.println("MCP服务器端点：");
        System.out.println("  - WebSocket: ws://localhost:8080/mcp");
        System.out.println("==========================================");
        System.out.println("传统REST API：");
        System.out.println("  - 文本对话：GET /ai/chat?message=你好");
        System.out.println("  - 文生图：GET /ai/image?prompt=画一只可爱的猫");
        System.out.println("  - 语音合成：GET /ai/speech?text=你好世界");
        System.out.println("  - 健康检查：GET /ai/health");
        System.out.println("==========================================");
        System.out.println("💡 提示：启动MCP客户端来连接此服务器");
        System.out.println("==========================================");
    }
}
