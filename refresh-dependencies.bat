@echo off
echo 刷新Maven依赖...
echo.

echo 1. 清理项目...
D:\Maven\apache-maven-3.6.1\bin\mvn.cmd clean

echo.
echo 2. 下载依赖...
D:\Maven\apache-maven-3.6.1\bin\mvn.cmd dependency:resolve

echo.
echo 3. 编译项目...
D:\Maven\apache-maven-3.6.1\bin\mvn.cmd compile

echo.
echo 4. 检查MySQL驱动...
if exist "D:\Maven\MavenHouse\house\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar" (
    echo ✅ MySQL驱动存在
) else (
    echo ❌ MySQL驱动不存在，正在下载...
    D:\Maven\apache-maven-3.6.1\bin\mvn.cmd dependency:get -Dartifact=com.mysql:mysql-connector-j:8.0.33
)

echo.
echo 5. 检查SQL Server驱动...
dir "D:\Maven\MavenHouse\house\com\microsoft\sqlserver\mssql-jdbc\*" /b 2>nul
if %errorlevel% equ 0 (
    echo ✅ SQL Server驱动存在
) else (
    echo ❌ SQL Server驱动不存在
)

echo.
echo 依赖刷新完成！
echo.
echo 现在可以在IDEA中使用 "MCP Server IDEA" 配置启动调试模式
echo.
pause
