@echo off
echo ==========================================
echo    Testing MCP Package
echo ==========================================

echo.
echo Extracting package...
if exist "test-deploy" rmdir /s /q "test-deploy"
powershell -Command "Expand-Archive -Path 'mcp-package.zip' -DestinationPath 'test-deploy' -Force"

echo.
echo Package contents:
dir "test-deploy\deploy-package" /b

echo.
echo JAR file sizes:
dir "test-deploy\deploy-package\server\*.jar" | findstr ".jar"
dir "test-deploy\deploy-package\client\*.jar" | findstr ".jar"

echo.
echo Testing server JAR...
cd "test-deploy\deploy-package"
java -jar server\mcp-server-1.0.0.jar --help >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Server JAR is executable
) else (
    echo [ERROR] Server JAR has issues
)

echo.
echo Testing client JAR...
java -jar client\mcp-client-1.0.0.jar --help >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Client JAR is executable
) else (
    echo [ERROR] Client JAR has issues
)

cd ..\..

echo.
echo ==========================================
echo    Package Test Complete
echo ==========================================
echo.
echo Package is ready for deployment!
echo To deploy: Extract mcp-package.zip and run start-all.bat
echo.
pause
