package com.example.springai.service;

import com.example.springai.repository.NewProductSalesRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 新品成交数据服务
 * 处理新品成交相关的查询
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class NewProductSalesService {
    
    @Autowired
    private NewProductSalesRepository newProductSalesRepository;
    
    /**
     * 获取新品成交统计数据
     */
    public Map<String, Object> getNewProductSalesStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询新品成交统计: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> salesData = newProductSalesRepository.getNewProductSalesStatistics(startTime, endTime);
            Object[] overview = newProductSalesRepository.getNewProductSalesOverview(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "新品成交统计");
            result.put("message", "查询新品成交数据统计");
            
            // 总体统计
            if (overview != null && overview.length >= 3) {
                result.put("totalProductCount", overview[0] != null ? ((Number)overview[0]).intValue() : 0);
                result.put("totalOrderCount", overview[1] != null ? ((Number)overview[1]).intValue() : 0);
                result.put("totalRevenue", overview[2] != null ? (BigDecimal)overview[2] : BigDecimal.ZERO);
            }
            
            // 产品明细
            result.put("productStatistics", formatProductStatistics(salesData));
            
            return result;
            
        } catch (Exception e) {
            log.error("查询新品成交数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取新品成交排行（按订单数）
     */
    public Map<String, Object> getNewProductSalesRankingByOrderCount(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询新品成交排行(按订单数): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> salesData = newProductSalesRepository.getNewProductSalesRankingByOrderCount(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "新品成交排行(订单数)");
            result.put("message", "查询新品成交排行，按订单数排序");
            result.put("rankingType", "订单数");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = salesData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            result.put("productRanking", formatProductStatistics(limitedData));
            
            return result;
            
        } catch (Exception e) {
            log.error("查询新品成交排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取新品成交排行（按营业额）
     */
    public Map<String, Object> getNewProductSalesRankingByRevenue(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询新品成交排行(按营业额): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> salesData = newProductSalesRepository.getNewProductSalesRankingByRevenue(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "新品成交排行(营业额)");
            result.put("message", "查询新品成交排行，按营业额排序");
            result.put("rankingType", "营业额");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = salesData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            result.put("productRanking", formatProductStatistics(limitedData));
            
            return result;
            
        } catch (Exception e) {
            log.error("查询新品成交排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 格式化产品统计数据
     */
    private List<Map<String, Object>> formatProductStatistics(List<Object[]> salesData) {
        return salesData.stream()
                .map(data -> {
                    Map<String, Object> productStat = new HashMap<>();
                    productStat.put("productName", data[0] != null ? data[0].toString() : "未知产品");
                    productStat.put("orderCount", data[1] != null ? ((Number)data[1]).intValue() : 0);
                    productStat.put("totalRevenue", data[2] != null ? (BigDecimal)data[2] : BigDecimal.ZERO);
                    productStat.put("productType", data[3] != null ? data[3].toString() : "未知类别");
                    productStat.put("productId", data[4] != null ? ((Number)data[4]).longValue() : 0L);
                    return productStat;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
