package com.example.springai.service;

import com.example.springai.repository.NewProductSalesRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 新品成交数据服务
 * 处理新品成交相关的查询
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class NewProductSalesService {
    
    @Autowired
    private NewProductSalesRepository newProductSalesRepository;
    
    /**
     * 获取新品成交统计数据
     */
    public Map<String, Object> getNewProductSalesStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询新品成交统计: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> salesData = newProductSalesRepository.getNewProductSalesStatistics(startTime, endTime);
            Object[] overview = newProductSalesRepository.getNewProductSalesOverview(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "新品成交统计");
            result.put("message", "查询新品成交数据统计");
            
            // 总体统计
            if (overview != null && overview.length >= 3) {
                int productCount = overview[0] != null ? ((Number)overview[0]).intValue() : 0;
                int orderCount = overview[1] != null ? ((Number)overview[1]).intValue() : 0;
                BigDecimal totalRevenue = overview[2] != null ? (BigDecimal)overview[2] : BigDecimal.ZERO;

                result.put("totalProductCount", productCount);
                result.put("totalOrderCount", orderCount);
                result.put("totalRevenue", totalRevenue);

                // 添加额外的统计信息
                if (orderCount > 0) {
                    result.put("avgOrderAmount", totalRevenue.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP));
                }
                if (productCount > 0) {
                    result.put("avgProductRevenue", totalRevenue.divide(BigDecimal.valueOf(productCount), 2, RoundingMode.HALF_UP));
                }
            }
            
            // 产品明细
            List<Map<String, Object>> productStats = formatProductStatistics(salesData);
            result.put("productStatistics", productStats);

            // 验证和补充统计数据（从产品明细中累加）
            validateAndSupplementStatistics(result, productStats);

            return result;
            
        } catch (Exception e) {
            log.error("查询新品成交数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取新品成交排行（按订单数）
     */
    public Map<String, Object> getNewProductSalesRankingByOrderCount(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询新品成交排行(按订单数): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> salesData = newProductSalesRepository.getNewProductSalesRankingByOrderCount(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "新品成交排行(订单数)");
            result.put("message", "查询新品成交排行，按订单数排序");
            result.put("rankingType", "订单数");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = salesData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            List<Map<String, Object>> productRanking = formatProductStatistics(limitedData);
            result.put("productRanking", productRanking);

            // 计算累加统计（基于所有数据，不仅仅是限制的数据）
            List<Map<String, Object>> allProductStats = formatProductStatistics(salesData);
            calculateRankingStatistics(result, allProductStats, productRanking);

            return result;
            
        } catch (Exception e) {
            log.error("查询新品成交排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取新品成交排行（按营业额）
     */
    public Map<String, Object> getNewProductSalesRankingByRevenue(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询新品成交排行(按营业额): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> salesData = newProductSalesRepository.getNewProductSalesRankingByRevenue(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "新品成交排行(营业额)");
            result.put("message", "查询新品成交排行，按营业额排序");
            result.put("rankingType", "营业额");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = salesData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            List<Map<String, Object>> productRanking = formatProductStatistics(limitedData);
            result.put("productRanking", productRanking);

            // 计算累加统计（基于所有数据，不仅仅是限制的数据）
            List<Map<String, Object>> allProductStats = formatProductStatistics(salesData);
            calculateRankingStatistics(result, allProductStats, productRanking);

            return result;
            
        } catch (Exception e) {
            log.error("查询新品成交排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 格式化产品统计数据
     */
    private List<Map<String, Object>> formatProductStatistics(List<Object[]> salesData) {
        return salesData.stream()
                .map(data -> {
                    Map<String, Object> productStat = new HashMap<>();
                    productStat.put("productName", data[0] != null ? data[0].toString() : "未知产品");
                    productStat.put("orderCount", data[1] != null ? ((Number)data[1]).intValue() : 0);
                    productStat.put("totalRevenue", data[2] != null ? (BigDecimal)data[2] : BigDecimal.ZERO);
                    productStat.put("productType", data[3] != null ? data[3].toString() : "未知类别");
                    productStat.put("productId", data[4] != null ? ((Number)data[4]).longValue() : 0L);
                    return productStat;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 验证和补充统计数据
     * 从产品明细中累加计算总金额和订单数，确保数据准确性
     */
    private void validateAndSupplementStatistics(Map<String, Object> result, List<Map<String, Object>> productStats) {
        if (productStats == null || productStats.isEmpty()) {
            return;
        }

        // 从产品明细中累加计算
        BigDecimal calculatedTotalRevenue = BigDecimal.ZERO;
        int calculatedTotalOrders = 0;
        int calculatedProductCount = productStats.size();

        for (Map<String, Object> product : productStats) {
            Object revenue = product.get("totalRevenue");
            Object orderCount = product.get("orderCount");

            if (revenue instanceof BigDecimal) {
                calculatedTotalRevenue = calculatedTotalRevenue.add((BigDecimal) revenue);
            }

            if (orderCount instanceof Number) {
                calculatedTotalOrders += ((Number) orderCount).intValue();
            }
        }

        // 更新或补充统计数据
        result.put("calculatedTotalRevenue", calculatedTotalRevenue);
        result.put("calculatedTotalOrders", calculatedTotalOrders);
        result.put("calculatedProductCount", calculatedProductCount);

        // 如果原始统计数据不存在或为0，使用计算出的数据
        Object originalRevenue = result.get("totalRevenue");
        Object originalOrders = result.get("totalOrderCount");
        Object originalProductCount = result.get("totalProductCount");

        if (originalRevenue == null || ((BigDecimal) originalRevenue).compareTo(BigDecimal.ZERO) == 0) {
            result.put("totalRevenue", calculatedTotalRevenue);
        }

        if (originalOrders == null || ((Number) originalOrders).intValue() == 0) {
            result.put("totalOrderCount", calculatedTotalOrders);
        }

        if (originalProductCount == null || ((Number) originalProductCount).intValue() == 0) {
            result.put("totalProductCount", calculatedProductCount);
        }

        // 添加详细的累加统计信息
        result.put("detailedStatistics", createDetailedStatistics(productStats, calculatedTotalRevenue, calculatedTotalOrders));
    }

    /**
     * 创建详细的统计信息
     */
    private Map<String, Object> createDetailedStatistics(List<Map<String, Object>> productStats,
                                                        BigDecimal totalRevenue, int totalOrders) {
        Map<String, Object> detailed = new HashMap<>();

        detailed.put("totalProductCount", productStats.size());
        detailed.put("totalOrderCount", totalOrders);
        detailed.put("totalRevenue", totalRevenue);

        if (totalOrders > 0) {
            detailed.put("avgOrderAmount", totalRevenue.divide(BigDecimal.valueOf(totalOrders), 2, RoundingMode.HALF_UP));
        }

        if (productStats.size() > 0) {
            detailed.put("avgProductRevenue", totalRevenue.divide(BigDecimal.valueOf(productStats.size()), 2, RoundingMode.HALF_UP));
        }

        // 找出最高和最低的产品
        if (!productStats.isEmpty()) {
            Map<String, Object> topProduct = productStats.get(0); // 已经按订单数或营业额排序
            detailed.put("topProduct", topProduct);

            // 计算占比
            Object topRevenue = topProduct.get("totalRevenue");
            if (topRevenue instanceof BigDecimal && totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal percentage = ((BigDecimal) topRevenue)
                    .divide(totalRevenue, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                detailed.put("topProductRevenuePercentage", percentage);
            }
        }

        return detailed;
    }

    /**
     * 计算排行统计数据
     */
    private void calculateRankingStatistics(Map<String, Object> result,
                                          List<Map<String, Object>> allProductStats,
                                          List<Map<String, Object>> limitedProductStats) {

        // 计算所有产品的累加统计
        BigDecimal totalRevenue = BigDecimal.ZERO;
        int totalOrders = 0;

        for (Map<String, Object> product : allProductStats) {
            Object revenue = product.get("totalRevenue");
            Object orderCount = product.get("orderCount");

            if (revenue instanceof BigDecimal) {
                totalRevenue = totalRevenue.add((BigDecimal) revenue);
            }

            if (orderCount instanceof Number) {
                totalOrders += ((Number) orderCount).intValue();
            }
        }

        // 计算显示的产品的累加统计
        BigDecimal displayedRevenue = BigDecimal.ZERO;
        int displayedOrders = 0;

        for (Map<String, Object> product : limitedProductStats) {
            Object revenue = product.get("totalRevenue");
            Object orderCount = product.get("orderCount");

            if (revenue instanceof BigDecimal) {
                displayedRevenue = displayedRevenue.add((BigDecimal) revenue);
            }

            if (orderCount instanceof Number) {
                displayedOrders += ((Number) orderCount).intValue();
            }
        }

        // 添加累加统计信息
        result.put("totalProductCount", allProductStats.size());
        result.put("totalOrderCount", totalOrders);
        result.put("totalRevenue", totalRevenue);
        result.put("displayedProductCount", limitedProductStats.size());
        result.put("displayedOrderCount", displayedOrders);
        result.put("displayedRevenue", displayedRevenue);

        // 计算占比
        if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal revenuePercentage = displayedRevenue
                .divide(totalRevenue, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            result.put("displayedRevenuePercentage", revenuePercentage);
        }

        if (totalOrders > 0) {
            double orderPercentage = (double) displayedOrders / totalOrders * 100;
            result.put("displayedOrderPercentage", BigDecimal.valueOf(orderPercentage).setScale(2, RoundingMode.HALF_UP));
        }

        // 添加平均值
        if (totalOrders > 0) {
            result.put("avgOrderAmount", totalRevenue.divide(BigDecimal.valueOf(totalOrders), 2, RoundingMode.HALF_UP));
        }

        if (allProductStats.size() > 0) {
            result.put("avgProductRevenue", totalRevenue.divide(BigDecimal.valueOf(allProductStats.size()), 2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
