package com.example.springai.service;

import com.example.springai.entity.Store;
import com.example.springai.entity.SalesRecord;
import com.example.springai.entity.StoreRevenue;
import com.example.springai.enums.StoreType;
import com.example.springai.repository.StoreRepository;
import com.example.springai.repository.SalesRecordRepository;
import com.example.springai.repository.StoreRevenueRepository;
import com.example.springai.jingang.service.JingangBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 业务查询服务
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class BusinessQueryService {

    // 每月目标金额：5万元
    private static final BigDecimal MONTHLY_TARGET = new BigDecimal("50000");

    private final StoreRepository storeRepository;
    private final SalesRecordRepository salesRecordRepository;
    private final StoreRevenueRepository storeRevenueRepository;

    @Autowired
    private JingangBusinessService jingangBusinessService;

    @Autowired
    public BusinessQueryService(StoreRepository storeRepository,
                              SalesRecordRepository salesRecordRepository,
                              StoreRevenueRepository storeRevenueRepository) {
        this.storeRepository = storeRepository;
        this.salesRecordRepository = salesRecordRepository;
        this.storeRevenueRepository = storeRevenueRepository;
    }
    
    /**
     * 查询指定时间范围内的总业绩
     */
    public Map<String, Object> getTotalSales(LocalDate startDate, LocalDate endDate) {
        log.info("查询总业绩: {} 到 {}", startDate, endDate);
        
        BigDecimal totalSales = salesRecordRepository.calculateTotalSalesBetween(startDate, endDate);
        List<Object[]> dailyStats = salesRecordRepository.getDailyStatisticsBetween(startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("totalSales", totalSales);
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("dailyBreakdown", formatDailyStats(dailyStats));
        
        return result;
    }
    
    /**
     * 查询指定门店的业绩
     */
    public Map<String, Object> getStoreSales(String storeIdentifier, LocalDate startDate, LocalDate endDate) {
        log.info("查询门店业绩: {} 从 {} 到 {}", storeIdentifier, startDate, endDate);
        
        Store store = findStoreByIdentifier(storeIdentifier);
        if (store == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "未找到门店: " + storeIdentifier);
            return result;
        }
        
        BigDecimal storeSales = salesRecordRepository.calculateStoreSalesBetween(store, startDate, endDate);
        List<SalesRecord> records = salesRecordRepository.findByStoreAndSaleDateBetween(store, startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        result.put("store", formatStoreInfo(store));
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("totalSales", storeSales);
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("recordCount", records.size());
        result.put("salesRecords", formatSalesRecords(records));
        
        return result;
    }
    
    /**
     * 获取门店排名
     */
    public Map<String, Object> getStoreRanking(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询门店排名: {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        List<Object[]> storeStats = salesRecordRepository.getTopStoresBySales(startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("ranking", formatStoreRanking(storeStats, limit));
        
        return result;
    }
    
    /**
     * 获取销售统计概览
     */
    public Map<String, Object> getSalesOverview(LocalDate startDate, LocalDate endDate) {
        log.info("查询销售概览: {} 到 {}", startDate, endDate);
        
        BigDecimal totalSales = salesRecordRepository.calculateTotalSalesBetween(startDate, endDate);
        List<Object[]> storeStats = salesRecordRepository.getStoreStatisticsBetween(startDate, endDate);
        List<Object[]> typeStats = salesRecordRepository.getSalesStatisticsByType(startDate, endDate);
        List<Store> activeStores = storeRepository.findAllStores();
        
        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("totalSales", totalSales);
        result.put("activeStoreCount", activeStores.size());
        result.put("storeStatistics", formatStoreStatistics(storeStats));
        result.put("salesByType", formatSalesByType(typeStats));
        
        return result;
    }
    
    /**
     * 根据标识符查找门店（支持门店名称）
     */
    private Store findStoreByIdentifier(String identifier) {
        // 按名称查找
        List<Store> stores = storeRepository.findByStoreNameContainingIgnoreCase(identifier);
        return stores.isEmpty() ? null : stores.get(0);
    }
    
    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = startDate.until(endDate).getDays() + 1;
        if (days == 1) {
            return "当日";
        } else if (days == 7) {
            return "一周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            return "约" + (days / 30) + "个月";
        }
    }
    
    /**
     * 格式化门店信息
     */
    private Map<String, Object> formatStoreInfo(Store store) {
        Map<String, Object> info = new HashMap<>();
        info.put("storeName", store.getStoreName());
        info.put("storeId", store.getId());
        info.put("storeType", store.getStoreType());

        StoreType storeTypeEnum = store.getStoreTypeEnum();
        if (storeTypeEnum != null) {
            info.put("storeTypeDescription", storeTypeEnum.getDescription());
            info.put("isCoopStore", storeTypeEnum.isCoopStore());
        }

        return info;
    }
    
    /**
     * 格式化销售记录
     */
    private List<Map<String, Object>> formatSalesRecords(List<SalesRecord> records) {
        return records.stream()
                .map(record -> {
                    Map<String, Object> recordMap = new HashMap<>();
                    recordMap.put("date", record.getSaleDate().toString());
                    recordMap.put("amount", record.getAmount());
                    recordMap.put("orderCount", record.getOrderCount());
                    recordMap.put("customerCount", record.getCustomerCount());
                    recordMap.put("saleType", record.getSaleType() != null ? record.getSaleType().getDescription() : "未知");
                    return recordMap;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 格式化日销售统计
     */
    private List<Map<String, Object>> formatDailyStats(List<Object[]> dailyStats) {
        return dailyStats.stream()
                .map(stat -> {
                    Map<String, Object> daily = new HashMap<>();
                    daily.put("date", stat[0].toString());
                    daily.put("amount", stat[1]);
                    daily.put("recordCount", stat[2]);
                    daily.put("orderCount", stat[3]);
                    return daily;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 格式化门店排名
     */
    private List<Map<String, Object>> formatStoreRanking(List<Object[]> storeStats, int limit) {
        return storeStats.stream()
                .limit(limit)
                .map(stat -> {
                    Map<String, Object> ranking = new HashMap<>();
                    ranking.put("storeName", stat[0]);
                    ranking.put("storeCode", stat[1]);
                    ranking.put("totalSales", stat[2]);
                    return ranking;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 格式化门店统计
     */
    private List<Map<String, Object>> formatStoreStatistics(List<Object[]> storeStats) {
        return storeStats.stream()
                .map(stat -> {
                    Map<String, Object> storeStat = new HashMap<>();
                    storeStat.put("storeName", stat[0]);
                    storeStat.put("storeCode", stat[1]);
                    storeStat.put("totalSales", stat[2]);
                    storeStat.put("recordCount", stat[3]);
                    storeStat.put("orderCount", stat[4]);
                    return storeStat;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 格式化按类型销售统计
     */
    private List<Map<String, Object>> formatSalesByType(List<Object[]> typeStats) {
        return typeStats.stream()
                .map(stat -> {
                    Map<String, Object> typeStat = new HashMap<>();
                    typeStat.put("saleType", stat[0] != null ? ((SalesRecord.SaleType) stat[0]).getDescription() : "未知");
                    typeStat.put("totalSales", stat[1]);
                    typeStat.put("recordCount", stat[2]);
                    return typeStat;
                })
                .collect(Collectors.toList());
    }

    // ==================== 平台收入查询方法 ====================

    /**
     * 查询平台收入总额（共创店）
     */
    public Map<String, Object> getTotalPlatformRevenue(LocalDate startDate, LocalDate endDate) {
        log.info("查询共创店平台收入总额: {} 到 {}", startDate, endDate);

        Date startDateTime = convertToDate(startDate);
        Date endDateTime = convertToDate(endDate);

        BigDecimal totalRevenue;
        try {
            totalRevenue = storeRevenueRepository.calculateTotalCoopStoreRevenueBetween(startDateTime, endDateTime);
        } catch (Exception e) {
            log.error("查询平台收入数据库连接失败: {}", e.getMessage());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据库连接失败");
            errorResult.put("message", e.getMessage().contains("timeout") ? "连接超时，请稍后重试" : "连接错误，请检查数据库状态");
            errorResult.put("queryType", "平台收入总额");
            return errorResult;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("totalPlatformRevenue", totalRevenue);
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("queryType", "平台收入总额");
        result.put("message", "查询共创店（加盟店，storeType=2）的平台收入总额");

        return result;
    }

    /**
     * 查询平台收入明细（共创店）
     */
    public Map<String, Object> getPlatformRevenueDetails(LocalDate startDate, LocalDate endDate) {
        log.info("查询共创店平台收入明细: {} 到 {}", startDate, endDate);

        Date startDateTime = convertToDate(startDate);
        Date endDateTime = convertToDate(endDate);

        BigDecimal totalRevenue = storeRevenueRepository.calculateTotalCoopStoreRevenueBetween(startDateTime, endDateTime);
        List<Object[]> revenueDetails = storeRevenueRepository.getCoopStoreRevenueDetailsBetween(startDateTime, endDateTime);
        List<Object[]> storeStats = storeRevenueRepository.getCoopStoreRevenueStatisticsBetween(startDateTime, endDateTime);

        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("totalPlatformRevenue", totalRevenue);
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("revenueDetails", formatPlatformRevenueDetails(revenueDetails));
        result.put("storeStatistics", formatPlatformStoreStats(storeStats));
        result.put("queryType", "平台收入明细");
        result.put("message", "查询共创店（加盟店，storeType=2）的平台收入明细，包含门店和日期信息");

        return result;
    }

    /**
     * 查询指定门店的平台收入
     */
    public Map<String, Object> getStorePlatformRevenue(String storeIdentifier, LocalDate startDate, LocalDate endDate) {
        log.info("查询门店平台收入: {} 从 {} 到 {}", storeIdentifier, startDate, endDate);

        Store store = findStoreByIdentifier(storeIdentifier);
        if (store == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "未找到门店: " + storeIdentifier);
            return result;
        }

        // 检查是否为共创店
        if (!store.isCoopStore()) {
            Map<String, Object> result = new HashMap<>();
            result.put("error", "门店 " + storeIdentifier + " 不是共创店，无平台收入数据");
            return result;
        }

        Date startDateTime = convertToDate(startDate);
        Date endDateTime = convertToDate(endDate);

        BigDecimal storeRevenue = storeRevenueRepository.calculateStoreRevenueBetween(store.getId(), startDateTime, endDateTime);

        Map<String, Object> result = new HashMap<>();
        result.put("storeName", store.getStoreName());
        result.put("storeId", store.getId());
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("totalPlatformRevenue", storeRevenue);
        result.put("queryType", "门店平台收入");
        result.put("message", "查询共创店（加盟店，storeType=2）的平台收入");

        return result;
    }

    /**
     * 获取平台收入排名（共创店）
     */
    public Map<String, Object> getPlatformRevenueRanking(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询共创店平台收入排名: {} 到 {}, 限制: {}", startDate, endDate, limit);

        Date startDateTime = convertToDate(startDate);
        Date endDateTime = convertToDate(endDate);

        List<Object[]> storeStats = storeRevenueRepository.getCoopStoreRevenueStatisticsBetween(startDateTime, endDateTime);

        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("ranking", formatSimpleRanking(storeStats, limit));
        result.put("queryType", "平台收入排名");
        result.put("message", "查询共创店（加盟店，storeType=2）的平台收入排名");

        return result;
    }

    /**
     * 获取平台收入概览（共创店）
     */
    public Map<String, Object> getPlatformRevenueOverview(LocalDate startDate, LocalDate endDate) {
        log.info("查询共创店平台收入概览: {} 到 {}", startDate, endDate);

        Date startDateTime = convertToDate(startDate);
        Date endDateTime = convertToDate(endDate);

        BigDecimal totalRevenue = storeRevenueRepository.calculateTotalCoopStoreRevenueBetween(startDateTime, endDateTime);
        List<Object[]> storeStats = storeRevenueRepository.getCoopStoreRevenueStatisticsBetween(startDateTime, endDateTime);

        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("period", calculatePeriodDescription(startDate, endDate));
        result.put("totalPlatformRevenue", totalRevenue);
        result.put("storeStatistics", formatPlatformStoreStats(storeStats));
        result.put("queryType", "平台收入概览");
        result.put("message", "查询共创店（加盟店，storeType=2）的平台收入概览");

        return result;
    }

    // ==================== 平台收入数据格式化方法 ====================

    /**
     * 格式化简单收入明细
     */
    private List<Map<String, Object>> formatSimpleRevenueDetails(List<Object[]> revenueDetails) {
        return revenueDetails.stream()
                .map(detail -> {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("storeName", detail[0]);
                    detailMap.put("amount", detail[1]);
                    return detailMap;
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化简单门店统计
     */
    private List<Map<String, Object>> formatSimpleStoreStats(List<Object[]> storeStats) {
        return storeStats.stream()
                .map(stat -> {
                    Map<String, Object> storeStat = new HashMap<>();
                    storeStat.put("storeName", stat[0]);
                    storeStat.put("totalRevenue", stat[1]);
                    storeStat.put("recordCount", stat[2]);
                    return storeStat;
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化简单排名
     */
    private List<Map<String, Object>> formatSimpleRanking(List<Object[]> storeStats, int limit) {
        return storeStats.stream()
                .limit(limit)
                .map(stat -> {
                    Map<String, Object> ranking = new HashMap<>();
                    ranking.put("storeName", stat[0]);
                    ranking.put("totalRevenue", stat[1]);
                    return ranking;
                })
                .collect(Collectors.toList());
    }

    // ==================== 时间转换方法 ====================

    /**
     * 将LocalDate转换为Date
     */
    private Date convertToDate(LocalDate localDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(localDate.getYear(), localDate.getMonthValue() - 1, localDate.getDayOfMonth(), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 格式化平台收入明细（包含日期信息）
     */
    private List<Map<String, Object>> formatPlatformRevenueDetails(List<Object[]> revenueDetails) {
        return revenueDetails.stream()
                .map(detail -> {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("storeName", detail[0]);
                    detailMap.put("amount", detail[1]);
                    detailMap.put("date", detail[2] != null ? detail[2].toString() : "");
                    detailMap.put("storeId", detail[3]);
                    return detailMap;
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化平台门店统计
     */
    private List<Map<String, Object>> formatPlatformStoreStats(List<Object[]> storeStats) {
        return storeStats.stream()
                .map(stat -> {
                    Map<String, Object> storeStat = new HashMap<>();
                    storeStat.put("storeName", stat[0]);
                    storeStat.put("totalRevenue", stat[1]);
                    storeStat.put("recordCount", stat[2]);
                    return storeStat;
                })
                .collect(Collectors.toList());
    }
}
