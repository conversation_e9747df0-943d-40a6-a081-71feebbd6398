package com.example.springai.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;

/**
 * 日期时间工具类
 */
public class DateTimeUtil {
    
    /**
     * 解析时间范围字符串，返回开始和结束时间
     */
    public static LocalDateTime[] parseDateRange(String timeRange) {
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;
        
        switch (timeRange.toLowerCase()) {
            case "本月":
                startDate = today.withDayOfMonth(1);
                endDate = today;
                break;
            case "上个月":
                LocalDate lastMonth = today.minusMonths(1);
                startDate = lastMonth.withDayOfMonth(1);
                endDate = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
                break;
            case "本周":
                startDate = today.with(java.time.DayOfWeek.MONDAY);
                endDate = today;
                break;
            case "上周":
                LocalDate lastWeek = today.minusWeeks(1);
                startDate = lastWeek.with(java.time.DayOfWeek.MONDAY);
                endDate = lastWeek.with(java.time.DayOfWeek.SUNDAY);
                break;
            case "今年":
                startDate = today.withDayOfYear(1);
                endDate = today;
                break;
            case "去年":
                LocalDate lastYear = today.minusYears(1);
                startDate = lastYear.withDayOfYear(1);
                endDate = lastYear.with(TemporalAdjusters.lastDayOfYear());
                break;
            case "最近7天":
                startDate = today.minusDays(6);
                endDate = today;
                break;
            case "最近30天":
                startDate = today.minusDays(29);
                endDate = today;
                break;
            case "昨天":
                startDate = today.minusDays(1);
                endDate = today.minusDays(1);
                break;
            case "今天":
                startDate = today;
                endDate = today;
                break;
            default:
                // 默认为本月
                startDate = today.withDayOfMonth(1);
                endDate = today;
                break;
        }
        
        // 转换为LocalDateTime，开始时间为当天00:00:00，结束时间为当天23:59:59
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
        
        return new LocalDateTime[]{startDateTime, endDateTime};
    }
    
    /**
     * 获取时间范围的描述
     */
    public static String getTimeRangeDescription(String timeRange) {
        switch (timeRange.toLowerCase()) {
            case "本月": return "本月";
            case "上个月": return "上个月";
            case "本周": return "本周";
            case "上周": return "上周";
            case "今年": return "今年";
            case "去年": return "去年";
            case "最近7天": return "最近7天";
            case "最近30天": return "最近30天";
            case "昨天": return "昨天";
            case "今天": return "今天";
            default: return timeRange;
        }
    }
    
    /**
     * 检查时间范围是否有效
     */
    public static boolean isValidTimeRange(String timeRange) {
        try {
            parseDateRange(timeRange);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
