package com.example.springai.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;

/**
 * 日期时间工具类
 */
@Slf4j
public class DateTimeUtil {
    
    /**
     * 解析时间范围字符串，返回开始和结束时间
     */
    public static LocalDateTime[] parseDateRange(String timeRange) {
        log.info("=== DateTimeUtil.parseDateRange 开始解析 ===");
        log.info("原始输入: '{}'", timeRange);

        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;

        String lowerTimeRange = timeRange.toLowerCase();
        log.info("转换为小写: '{}'", lowerTimeRange);

        switch (lowerTimeRange) {
            case "本月":
                startDate = today.withDayOfMonth(1);
                endDate = today;
                break;
            case "上个月":
                LocalDate lastMonth = today.minusMonths(1);
                startDate = lastMonth.withDayOfMonth(1);
                endDate = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
                break;
            case "本周":
                startDate = today.with(java.time.DayOfWeek.MONDAY);
                endDate = today;
                break;
            case "上周":
                LocalDate lastWeek = today.minusWeeks(1);
                startDate = lastWeek.with(java.time.DayOfWeek.MONDAY);
                endDate = lastWeek.with(java.time.DayOfWeek.SUNDAY);
                break;
            case "今年":
                startDate = today.withDayOfYear(1);
                endDate = today;
                break;
            case "去年":
                LocalDate lastYear = today.minusYears(1);
                startDate = lastYear.withDayOfYear(1);
                endDate = lastYear.with(TemporalAdjusters.lastDayOfYear());
                break;
            case "最近7天":
                startDate = today.minusDays(6);
                endDate = today;
                break;
            case "最近30天":
                startDate = today.minusDays(29);
                endDate = today;
                break;
            case "昨天":
                startDate = today.minusDays(1);
                endDate = today.minusDays(1);
                break;
            case "今天":
                startDate = today;
                endDate = today;
                break;
            default:
                log.info("进入default分支，尝试解析自定义时间区间");
                // 尝试解析自定义时间区间
                LocalDate[] customRange = parseCustomDateRange(timeRange);
                if (customRange != null) {
                    log.info("自定义时间区间解析成功: {} 到 {}", customRange[0], customRange[1]);
                    startDate = customRange[0];
                    endDate = customRange[1];
                } else {
                    // 如果解析失败，默认为本月
                    log.warn("无法解析时间范围: {}, 使用默认本月", timeRange);
                    startDate = today.withDayOfMonth(1);
                    endDate = today;
                }
                break;
        }

        // 转换为LocalDateTime，开始时间为当天00:00:00，结束时间为当天23:59:59
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

        log.info("=== DateTimeUtil.parseDateRange 解析完成 ===");
        log.info("最终结果 - 开始时间: {}, 结束时间: {}", startDateTime, endDateTime);

        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
        log.info("查询天数: {}天", daysBetween);

        return new LocalDateTime[]{startDateTime, endDateTime};
    }

    /**
     * 解析自定义时间区间
     * 支持多种格式：
     * - 2025-7-1到2025-7-10
     * - 2025.7.1到2025.7.10
     * - 2025-07-01到2025-07-10
     * - 2025年7.1号到7.10号
     * - 2025年7月1日到7月10日
     * - 7.1到7.10 (当年)
     * - 7月1日到7月10日 (当年)
     */
    private static LocalDate[] parseCustomDateRange(String timeRange) {
        try {
            log.info("尝试解析自定义时间区间: {}", timeRange);

            // 清理输入字符串 - 注意不要破坏日期格式中的"-"
            String cleanInput = timeRange.trim()
                .replace("从", "")
                .replace("自", "")
                .replace("至", "到")
                .replace("－", "到")  // 全角横线
                .replace("~", "到")
                .replace("～", "到"); // 全角波浪号

            // 处理可能的空格问题
            cleanInput = cleanInput.replaceAll("\\s+", "");

            log.info("清理后的输入: {}", cleanInput);

            // 定义多种时间格式的正则表达式
            Pattern[] patterns = {
                // 2025-7-1到2025-7-10 或 2025-07-01到2025-07-10 或 2025.7.1到2025.7.10
                Pattern.compile("(\\d{4})[\\-\\.](\\d{1,2})[\\-\\.](\\d{1,2})到(\\d{4})[\\-\\.](\\d{1,2})[\\-\\.](\\d{1,2})"),

                // 2025年7.1号到7.10号 或 2025年7月1日到7月10日
                Pattern.compile("(\\d{4})年(\\d{1,2})[\\.\\/月](\\d{1,2})[号日]?到(\\d{1,2})[\\.\\/月](\\d{1,2})[号日]?"),

                // 2025年7.1到2025年7.10
                Pattern.compile("(\\d{4})年(\\d{1,2})[\\.\\/月](\\d{1,2})[号日]?到(\\d{4})年(\\d{1,2})[\\.\\/月](\\d{1,2})[号日]?"),

                // 7.1到7.10 (当年)
                Pattern.compile("(\\d{1,2})[\\.\\/月](\\d{1,2})[号日]?到(\\d{1,2})[\\.\\/月](\\d{1,2})[号日]?"),

                // 7月1日到7月10日 (当年)
                Pattern.compile("(\\d{1,2})月(\\d{1,2})日?到(\\d{1,2})月(\\d{1,2})日?")
            };

            for (Pattern pattern : patterns) {
                Matcher matcher = pattern.matcher(cleanInput);
                if (matcher.find()) {
                    log.info("匹配到模式: {}", pattern.pattern());
                    return parseMatchedGroups(matcher, pattern.pattern());
                }
            }

            log.warn("无法匹配任何时间格式模式: {}", cleanInput);
            return null;

        } catch (Exception e) {
            log.error("解析自定义时间区间失败: {}", timeRange, e);
            return null;
        }
    }

    /**
     * 解析匹配到的正则表达式组
     */
    private static LocalDate[] parseMatchedGroups(Matcher matcher, String pattern) {
        try {
            int currentYear = LocalDate.now().getYear();
            LocalDate startDate = null;
            LocalDate endDate = null;

            log.info("解析匹配组，模式: {}, 组数: {}", pattern, matcher.groupCount());
            for (int i = 1; i <= matcher.groupCount(); i++) {
                log.info("组{}: {}", i, matcher.group(i));
            }

            int groupCount = matcher.groupCount();

            if (groupCount == 6) {
                // 格式：2025-7-1到2025-7-10 或 2025年7.1到2025年7.10
                int startYear = Integer.parseInt(matcher.group(1));
                int startMonth = Integer.parseInt(matcher.group(2));
                int startDay = Integer.parseInt(matcher.group(3));
                int endYear = Integer.parseInt(matcher.group(4));
                int endMonth = Integer.parseInt(matcher.group(5));
                int endDay = Integer.parseInt(matcher.group(6));

                startDate = LocalDate.of(startYear, startMonth, startDay);
                endDate = LocalDate.of(endYear, endMonth, endDay);

            } else if (groupCount == 5) {
                // 格式：2025年7.1号到7.10号 (同年同月)
                int year = Integer.parseInt(matcher.group(1));
                int startMonth = Integer.parseInt(matcher.group(2));
                int startDay = Integer.parseInt(matcher.group(3));
                int endMonth = Integer.parseInt(matcher.group(4));
                int endDay = Integer.parseInt(matcher.group(5));

                startDate = LocalDate.of(year, startMonth, startDay);
                endDate = LocalDate.of(year, endMonth, endDay);

            } else if (groupCount == 4) {
                // 格式：7.1到7.10 (当年) 或 7月1日到7月10日 (当年)
                int startMonth = Integer.parseInt(matcher.group(1));
                int startDay = Integer.parseInt(matcher.group(2));
                int endMonth = Integer.parseInt(matcher.group(3));
                int endDay = Integer.parseInt(matcher.group(4));

                startDate = LocalDate.of(currentYear, startMonth, startDay);
                endDate = LocalDate.of(currentYear, endMonth, endDay);
            }

            if (startDate != null && endDate != null) {
                // 确保开始日期不晚于结束日期
                if (startDate.isAfter(endDate)) {
                    LocalDate temp = startDate;
                    startDate = endDate;
                    endDate = temp;
                    log.info("交换开始和结束日期: {} <-> {}", startDate, endDate);
                }

                log.info("解析成功 - 开始日期: {}, 结束日期: {}", startDate, endDate);
                return new LocalDate[]{startDate, endDate};
            }

            log.warn("无法解析匹配的组，组数: {}", groupCount);
            return null;

        } catch (Exception e) {
            log.error("解析匹配组失败", e);
            return null;
        }
    }
    
    /**
     * 获取时间范围的描述
     */
    public static String getTimeRangeDescription(String timeRange) {
        switch (timeRange.toLowerCase()) {
            case "本月": return "本月";
            case "上个月": return "上个月";
            case "本周": return "本周";
            case "上周": return "上周";
            case "今年": return "今年";
            case "去年": return "去年";
            case "最近7天": return "最近7天";
            case "最近30天": return "最近30天";
            case "昨天": return "昨天";
            case "今天": return "今天";
            default: return timeRange;
        }
    }
    
    /**
     * 检查时间范围是否有效
     */
    public static boolean isValidTimeRange(String timeRange) {
        try {
            parseDateRange(timeRange);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
