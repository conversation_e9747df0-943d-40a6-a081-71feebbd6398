package com.example.springai.mcp.config;

import com.example.springai.mcp.server.McpServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * MCP WebSocket配置
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Configuration
@EnableWebSocket
public class McpWebSocketConfig implements WebSocketConfigurer {
    
    private final McpServer mcpServer;
    
    @Autowired
    public McpWebSocketConfig(McpServer mcpServer) {
        this.mcpServer = mcpServer;
    }
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册MCP服务器WebSocket处理器
        registry.addHandler(mcpServer, "/mcp")
                .setAllowedOrigins("*"); // 在生产环境中应该限制允许的源
    }
}
