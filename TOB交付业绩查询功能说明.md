# TOB交付业绩查询功能说明

## 🎯 功能概述

新增TOB交付业绩数据查询服务，基于Order和OrderChannel表的关联查询，支持自然语言查询TOB交付业绩统计、排行等信息。与新品成交服务类似的业务处理逻辑。

## 📊 数据源说明

### SQL基础查询
```sql
SELECT
    o.Amount,
    o.CreateTime,
    o.ProductName 
FROM [Order] o
LEFT JOIN OrderChannel oc ON o.billno = oc.billno 
WHERE o.ProductId NOT IN (SELECT id FROM Product WHERE ProductCategoryid IN (22, 23)) 
  AND o.PaySettlementTime BETWEEN '2025/7/1' AND '2025/7/11' 
  AND oc.channel = '292'
```

### 查询条件
- **ProductId过滤** - 排除ProductCategoryid IN (22, 23)的产品
- **PaySettlementTime** - 基于支付结算时间范围
- **channel = '292'** - 特定渠道（TOB渠道）
- **表关联** - Order表LEFT JOIN OrderChannel表

## 🔍 支持的查询类型

### 1. 基础统计查询
**触发关键词：** "TOB"、"TO B"、"B端"、"企业端" + "交付" + "业绩"、"收入"、"统计"

**查询示例：**
- "本月TOB交付业绩"
- "上周TOB交付业绩统计"
- "TOB交付业绩情况"
- "B端交付业绩"

**返回内容：**
- 总金额和总订单数
- 产品数量统计
- 平均订单金额

### 2. 产品明细查询
**触发关键词：** "TOB交付" + "产品"、"明细"

**查询示例：**
- "TOB交付业绩产品明细"
- "TOB交付产品统计"

**返回内容：**
- 产品详细统计
- 各产品订单数和金额
- 平均、最小、最大金额

### 3. 排行榜查询
**触发关键词：** "TOB交付业绩排行"

**查询示例：**
- "TOB交付业绩排行"
- "TOB交付业绩金额排行"
- "TOB交付业绩订单数排行"

**排序方式：**
- 默认按订单数排序
- 包含"营业额"、"金额"、"收入"时按金额排序

### 4. 时间范围支持
- **今天、昨天**
- **本周、上周**
- **本月、上月**
- **近7天、近30天**
- **自定义时间范围**

## 📋 返回数据格式

### 统计查询返回示例
```
**TOB交付业绩统计**

查询时间：11天

**📊 TOB交付业绩概况**
• **总金额：85.60万元** 💰
• **总订单数：156单** 📦
• **产品数量：8个** 📋
• 平均订单金额：5,487.18元 📊
```

### 排行榜查询返回示例
```
**TOB交付业绩排行(金额)**

查询时间：30天

**📊 TOB交付业绩概况**
• **总金额：85.60万元** 💰
• **总订单数：156单** 📦
• **产品数量：8个** 📋

**🏆 产品排行榜（按金额）**
🥇 **企业服务套餐A**：45单，28.90万元
🥈 **企业清洁服务**：38单，22.40万元
🥉 **企业维修服务**：32单，18.70万元
🏅 **企业保洁服务**：25单，10.20万元
🏅 **其他企业服务**：16单，5.40万元
... 还有3个产品未显示
```

### 产品明细查询返回示例
```
**TOB交付业绩产品明细**

查询时间：30天

**📊 TOB交付业绩概况**
• **总金额：85.60万元** 💰
• **总订单数：156单** 📦
• **产品数量：8个** 📋

**📋 产品明细**
🥇 **企业服务套餐A**：45单，28.90万元
🥈 **企业清洁服务**：38单，22.40万元
🥉 **企业维修服务**：32单，18.70万元
🏅 **企业保洁服务**：25单，10.20万元
🏅 **其他企业服务**：16单，5.40万元
```

## 🔧 技术架构

### 1. 服务层结构
- **TobDeliveryRepository** - 数据访问层
- **TobDeliveryService** - 业务逻辑层
- **BusinessAIService** - AI集成层

### 2. 核心方法
```java
// 基础统计查询
getTobDeliveryStatistics(startDate, endDate)

// 产品明细查询
getTobDeliveryProductDetails(startDate, endDate)

// 按金额排行
getTobDeliveryRankingByAmount(startDate, endDate, limit)

// 按订单数排行
getTobDeliveryRankingByOrderCount(startDate, endDate, limit)

// 每日趋势查询
getTobDeliveryDailyTrend(startDate, endDate)

// 原始明细数据
getTobDeliveryRawDetails(startDate, endDate)
```

### 3. 数据处理特性
- **表关联查询** - Order LEFT JOIN OrderChannel
- **产品过滤** - 排除特定产品类别
- **渠道过滤** - 只统计channel='292'的TOB渠道
- **货币格式化** - 大金额自动转换为万元显示

## 🧪 测试用例

### 正常查询测试
```
✅ "本月TOB交付业绩" → 返回本月统计数据
✅ "TOB交付业绩排行" → 返回排行榜数据
✅ "上周TOB交付业绩统计" → 返回上周数据
✅ "TOB交付业绩产品明细" → 返回产品明细
```

### 排行查询测试
```
✅ "TOB交付业绩排行" → 按订单数排序
✅ "TOB交付业绩金额排行" → 按金额排序
✅ "TOB交付业绩订单数排行" → 按订单数排序
```

### 不合理查询测试
```
❌ "下月TOB交付业绩" → 幽默拒绝
❌ "明天TOB交付业绩" → 未来时间拒绝
❌ "去年TOB交付业绩" → 久远时间提示
```

## 📈 数据统计特性

### 1. 多维度统计
- **产品维度** - 按产品分组统计
- **时间维度** - 支持各种时间范围
- **排序维度** - 订单数/金额双重排序

### 2. 智能计算
- **平均订单金额** - 总金额 ÷ 总订单数
- **平均产品金额** - 总金额 ÷ 产品数量
- **产品统计** - 最小、最大、平均金额

### 3. 数据验证
- **渠道过滤** - 只统计TOB渠道(292)数据
- **产品过滤** - 排除特定产品类别
- **时间精确** - 基于PaySettlementTime支付结算时间

## 🎨 用户体验优化

### 1. 友好的回复格式
- 使用💰📦📋等emoji图标增强视觉效果
- 清晰的层级结构
- 重要数据加粗显示

### 2. 智能查询识别
- 支持多种表达方式
- 自动识别查询意图
- 容错性强的关键词匹配

### 3. 合理的数据展示
- 限制显示数量避免信息过载
- 提供剩余数据数量提示
- 突出显示关键统计信息

## 🔍 查询识别逻辑

### 关键词匹配
```java
// TOB相关关键词
"tob" || "to b" || "b端" || "企业端"

// 交付相关关键词
"交付"

// 业绩相关关键词  
"业绩" || "收入" || "营收" || "统计" || "排行" || "明细" || "成交"

// 排除其他查询
!("三嫂" || "新品" || "金刚" || "保洁" || "驻场" || "大学" || "私域" || "全国")
```

### 优先级设置
1. 新品成交查询
2. 全国交付业绩查询
3. 驻场保洁集团结算查询
4. 大学收入查询
5. 私域ABC收款业绩查询
6. 交付业绩查询
7. **TOB交付业绩查询** ← 新增
8. 三嫂业绩查询
9. 金刚查询

## 🚀 使用示例

### 基础查询
```
用户：本月TOB交付业绩
系统：返回本月TOB交付业绩统计数据
```

### 排行查询
```
用户：TOB交付业绩排行
系统：返回按订单数排序的产品排行榜
```

### 金额排行
```
用户：TOB交付业绩金额排行
系统：返回按金额排序的产品排行榜
```

### 产品明细
```
用户：TOB交付业绩产品明细
系统：返回产品详细统计信息
```

## 💡 特殊限制

### 渠道限制
- **channel = '292'** - 只统计TOB渠道的订单
- 这个限制确保统计的是企业端业务数据

### 产品过滤
- **ProductCategoryid NOT IN (22, 23)** - 排除特定产品类别
- 确保统计的是符合TOB业务的产品

### 数据一致性
- **LEFT JOIN OrderChannel** - 确保订单和渠道数据的关联
- **PaySettlementTime** - 基于支付结算时间统计

## 🔄 与其他服务的区别

### vs 交付业绩查询
- **TOB交付业绩** - 基于channel='292'的TOB渠道
- **交付业绩** - 基于IsGroup=0的非团购订单，包含复购分析

### vs 全国交付业绩查询
- **TOB交付业绩** - 基于TOB渠道的产品统计
- **全国交付业绩** - 基于部门维度的业绩统计

### vs 新品成交查询
- **TOB交付业绩** - 基于TOB渠道的所有产品
- **新品成交** - 基于特定标签的新品产品

通过这个TOB交付业绩查询功能，用户可以方便地了解企业端交付业务的整体表现和产品排行！
