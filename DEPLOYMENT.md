# MCP项目部署指南

## 📦 打包说明

### Windows环境打包
```bash
# 运行打包脚本
build-jars.bat
```

### Linux/Mac环境打包
```bash
# 设置执行权限
chmod +x build-jars.sh

# 运行打包脚本
./build-jars.sh
```

## 🚀 部署步骤

### 1. 环境要求
- **Java**: JDK 17 或更高版本
- **内存**: 建议至少 2GB 可用内存
- **端口**: 确保以下端口可用
  - 8080: MCP服务器
  - 8081: MCP客户端
  - 8090: MCP服务器管理端口
  - 8091: MCP客户端管理端口

### 2. 部署文件结构
```
deploy/
├── server/
│   └── mcp-server-1.0.0.jar
├── client/
│   └── mcp-client-1.0.0.jar
├── view/
│   └── ai-query.html
├── logs/
├── start-server.bat/.sh
├── start-client.bat/.sh
├── start-all.bat/.sh
└── stop-all.bat/.sh
```

### 3. Windows服务器部署

#### 启动服务
```bash
# 启动全部服务
start-all.bat

# 或分别启动
start-server.bat  # 启动MCP服务器
start-client.bat  # 启动MCP客户端
```

#### 停止服务
```bash
stop-all.bat
```

### 4. Linux服务器部署

#### 启动服务
```bash
# 启动全部服务（后台运行）
./start-all.sh

# 或分别启动
./start-server.sh  # 启动MCP服务器
./start-client.sh  # 启动MCP客户端
```

#### 停止服务
```bash
./stop-all.sh
```

#### 查看日志
```bash
# 查看服务器日志
tail -f logs/server.log

# 查看客户端日志
tail -f logs/client.log
```

## 🔧 配置说明

### 生产环境配置
项目使用 `application-prod.yml` 作为生产环境配置：

#### MCP服务器配置
- 端口: 8080
- 管理端口: 8090
- 数据库: SQL Server (rm-wz9yqo4g4u00p43c3.sqlserver.rds.aliyuncs.com)
- 日志级别: INFO
- 日志文件: logs/mcp-server.log

#### MCP客户端配置
- 端口: 8081
- 管理端口: 8091
- MCP服务器地址: ws://localhost:8080/mcp
- 日志文件: logs/mcp-client.log

### 自定义配置
如需修改配置，可以：

1. **修改配置文件**：
   - 编辑 `application-prod.yml`
   - 重新打包部署

2. **使用环境变量**：
   ```bash
   # 修改数据库连接
   export SPRING_DATASOURCE_URL="****************************************************"
   export SPRING_DATASOURCE_USERNAME="your-username"
   export SPRING_DATASOURCE_PASSWORD="your-password"
   
   # 修改端口
   export SERVER_PORT=8080
   ```

3. **使用启动参数**：
   ```bash
   java -jar -Dserver.port=8080 -Dspring.datasource.url="..." mcp-server-1.0.0.jar
   ```

## 🌐 访问地址

### 服务端点
- **MCP服务器**: http://服务器IP:8080
- **MCP客户端**: http://服务器IP:8081
- **AI智能问数页面**: 打开 `view/ai-query.html`

### 管理端点
- **服务器健康检查**: http://服务器IP:8090/actuator/health
- **客户端健康检查**: http://服务器IP:8091/actuator/health

### API接口
- **MCP连接**: POST http://服务器IP:8081/mcp/connect
- **业务查询**: POST http://服务器IP:8081/mcp/tools/business-query?query=xxx

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查Java版本
java -version

# 检查端口占用
netstat -an | grep 8080
netstat -an | grep 8081

# 查看详细错误日志
tail -f logs/server.log
tail -f logs/client.log
```

#### 2. 数据库连接失败
- 检查数据库服务器是否可访问
- 验证数据库连接信息
- 确认防火墙设置

#### 3. MCP连接失败
- 确保MCP服务器已启动
- 检查WebSocket连接
- 验证客户端配置中的服务器地址

#### 4. 内存不足
```bash
# 调整JVM内存参数
java -jar -Xms1024m -Xmx2048m mcp-server-1.0.0.jar
```

### 日志级别调整
如需调试，可以临时调整日志级别：
```bash
# 启动时指定调试级别
java -jar -Dlogging.level.com.example.springai=DEBUG mcp-server-1.0.0.jar
```

## 🔒 安全建议

### 生产环境安全
1. **修改默认密码**: 更改数据库密码和API密钥
2. **配置防火墙**: 限制端口访问
3. **使用HTTPS**: 配置SSL证书
4. **定期备份**: 备份数据库和配置文件

### 监控建议
1. **健康检查**: 定期检查 `/actuator/health` 端点
2. **日志监控**: 监控错误日志
3. **性能监控**: 监控内存和CPU使用率

## 📞 技术支持

如遇到部署问题，请检查：
1. Java版本是否正确
2. 端口是否被占用
3. 数据库连接是否正常
4. 日志文件中的错误信息

部署成功后，您可以通过AI智能问数页面体验完整的平台收入查询功能！
