package com.example.springai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.example.springai.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 阿里云智能语音Token服务实现
 *
 * <AUTHOR> AI Alibaba MCP
 * @version 1.0.0
 */
@Slf4j
@Service
public class TokenServiceImpl implements TokenService {

    // 阿里云的AccessKey linjing个人号，若过期需要进行替换
    private final String aliyunAkId = "LTAI5tEQSehski3fWvaQPrxn";
    private final String aliyunAkSecret = "******************************";

    // 地域ID
    private static final String REGIONID = "cn-shanghai";
    // 获取Token服务域名
    private static final String DOMAIN = "nls-meta.cn-shanghai.aliyuncs.com";
    // API版本
    private static final String API_VERSION = "2019-02-28";
    // API名称
    private static final String REQUEST_ACTION = "CreateToken";

    // 响应参数
    private static final String KEY_TOKEN = "Token";
    private static final String KEY_ID = "Id";
    private static final String KEY_EXPIRETIME = "ExpireTime";

    @Override
    public TokenResult getToken() {
        try {
            // 创建DefaultAcsClient实例并初始化
            DefaultProfile profile = DefaultProfile.getProfile(
                    REGIONID,
                    aliyunAkId,
                    aliyunAkSecret);

            IAcsClient client = new DefaultAcsClient(profile);
            CommonRequest request = new CommonRequest();
            request.setDomain(DOMAIN);
            request.setVersion(API_VERSION);
            request.setAction(REQUEST_ACTION);
            request.setMethod(MethodType.POST);
            request.setProtocol(ProtocolType.HTTPS);

            CommonResponse response = client.getCommonResponse(request);
            log.info(response.getData());
            if (response.getHttpStatus() == 200) {
                JSONObject result = JSON.parseObject(response.getData());
                String token = result.getJSONObject(KEY_TOKEN).getString(KEY_ID);
                long expireTime = result.getJSONObject(KEY_TOKEN).getLongValue(KEY_EXPIRETIME);
                log.info("获取到的Token： " + token + "，有效期时间戳(单位：秒): " + expireTime);
                // 将10位数的时间戳转换为北京时间
                String expireDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(expireTime * 1000));
                log.info("Token有效期的北京时间：" + expireDate);
                log.info("获取阿里云Token成功，过期时间：{}", expireTime);
                return new TokenResult(token, expireTime);
            } else {
                log.info("获取Token失败！");
                throw new RuntimeException("获取阿里云Token失败: ");
            }
        } catch (Exception e) {
            log.error("获取阿里云Token失败", e);
            throw new RuntimeException("获取阿里云Token失败: " + e.getMessage(), e);
        }
    }
} 