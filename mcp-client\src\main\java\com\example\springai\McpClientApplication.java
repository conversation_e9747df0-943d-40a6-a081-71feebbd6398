package com.example.springai;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * MCP客户端应用程序主启动类
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@SpringBootApplication
public class McpClientApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpClientApplication.class, args);
        System.out.println("==========================================");
        System.out.println("🚀 MCP客户端启动成功！");
        System.out.println("访问地址：http://localhost:8081");
        System.out.println("==========================================");
        System.out.println("MCP客户端API：");
        System.out.println("  - 连接服务器：POST /mcp/connect");
        System.out.println("  - 获取工具：GET /mcp/tools");
        System.out.println("  - AI对话：POST /mcp/tools/chat?message=你好");
        System.out.println("  - 图像生成：POST /mcp/tools/image?prompt=猫");
        System.out.println("  - 获取资源：GET /mcp/resources");
        System.out.println("  - 健康检查：GET /mcp/health");
        System.out.println("==========================================");
        System.out.println("💡 提示：确保MCP服务器已启动在8080端口");
        System.out.println("默认连接：ws://localhost:8080/mcp");
        System.out.println("==========================================");
    }
}
