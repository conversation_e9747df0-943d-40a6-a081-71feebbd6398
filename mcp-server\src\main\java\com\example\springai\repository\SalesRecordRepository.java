package com.example.springai.repository;

import com.example.springai.entity.SalesRecord;
import com.example.springai.entity.Store;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售记录数据访问接口
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface SalesRecordRepository extends JpaRepository<SalesRecord, Long> {
    
    /**
     * 根据门店查找销售记录
     */
    List<SalesRecord> findByStore(Store store);
    
    /**
     * 根据销售日期范围查找记录
     */
    List<SalesRecord> findBySaleDateBetween(LocalDate startDate, LocalDate endDate);
    
    /**
     * 根据门店和日期范围查找记录
     */
    List<SalesRecord> findByStoreAndSaleDateBetween(Store store, LocalDate startDate, LocalDate endDate);
    
    /**
     * 计算指定日期范围内的总销售额
     */
    @Query("SELECT COALESCE(SUM(sr.amount), 0) FROM SalesRecord sr WHERE sr.saleDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalSalesBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 计算指定门店在日期范围内的总销售额
     */
    @Query("SELECT COALESCE(SUM(sr.amount), 0) FROM SalesRecord sr WHERE sr.store = :store AND sr.saleDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateStoreSalesBetween(@Param("store") Store store, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 获取指定日期范围内各门店的销售统计（简化版）
     */
    @Query("SELECT sr.store.storeName, SUM(sr.amount), COUNT(sr), SUM(sr.orderCount) " +
           "FROM SalesRecord sr WHERE sr.saleDate BETWEEN :startDate AND :endDate " +
           "GROUP BY sr.store.id, sr.store.storeName " +
           "ORDER BY SUM(sr.amount) DESC")
    List<Object[]> getStoreStatisticsBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 获取指定日期范围内的日销售统计
     */
    @Query("SELECT sr.saleDate, SUM(sr.amount), COUNT(sr), SUM(sr.orderCount) " +
           "FROM SalesRecord sr WHERE sr.saleDate BETWEEN :startDate AND :endDate " +
           "GROUP BY sr.saleDate ORDER BY sr.saleDate DESC")
    List<Object[]> getDailyStatisticsBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 获取销售额排名前N的门店（简化版）
     */
    @Query("SELECT sr.store.storeName, SUM(sr.amount) " +
           "FROM SalesRecord sr WHERE sr.saleDate BETWEEN :startDate AND :endDate " +
           "GROUP BY sr.store.id, sr.store.storeName " +
           "ORDER BY SUM(sr.amount) DESC")
    List<Object[]> getTopStoresBySales(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 根据销售类型统计
     */
    @Query("SELECT sr.saleType, SUM(sr.amount), COUNT(sr) " +
           "FROM SalesRecord sr WHERE sr.saleDate BETWEEN :startDate AND :endDate " +
           "GROUP BY sr.saleType")
    List<Object[]> getSalesStatisticsByType(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
