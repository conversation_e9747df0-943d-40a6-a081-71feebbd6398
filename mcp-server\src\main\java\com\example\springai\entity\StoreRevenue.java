package com.example.springai.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 门店平台收入实体
 * 映射真实数据库中的StoreRevenue表
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Entity
@Table(name = "StoreRevenue")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StoreRevenue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联门店ID
     */
    @Column(name = "storeId", nullable = false)
    private Long storeId;

    /**
     * 关联门店实体（通过storeId关联Store表的id）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "storeId", insertable = false, updatable = false)
    private Store store;

    /**
     * 平台收入金额
     */
    @Column(name = "platformUsageFeeMoney", nullable = false, precision = 18, scale = 2)
    private BigDecimal platformUsageFeeMoney;

    /**
     * 创建时间（假设有这个字段，用于时间筛选）
     */
    @Column(name = "createTime")
    private java.util.Date createTime;
}
