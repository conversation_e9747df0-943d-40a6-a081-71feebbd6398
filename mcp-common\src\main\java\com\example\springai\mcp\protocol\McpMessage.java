package com.example.springai.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * MCP协议消息基类
 * 基于JSON-RPC 2.0规范
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpMessage {
    
    /**
     * JSON-RPC版本，固定为"2.0"
     */
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    /**
     * 消息ID，用于请求-响应匹配
     */
    @JsonProperty("id")
    private Object id;
    
    /**
     * 方法名称
     */
    @JsonProperty("method")
    private String method;
    
    /**
     * 请求参数
     */
    @JsonProperty("params")
    private Object params;
    
    /**
     * 响应结果
     */
    @JsonProperty("result")
    private Object result;
    
    /**
     * 错误信息
     */
    @JsonProperty("error")
    private McpError error;
    
    /**
     * 创建请求消息
     */
    public static McpMessage createRequest(Object id, String method, Object params) {
        McpMessage message = new McpMessage();
        message.setId(id);
        message.setMethod(method);
        message.setParams(params);
        return message;
    }
    
    /**
     * 创建响应消息
     */
    public static McpMessage createResponse(Object id, Object result) {
        McpMessage message = new McpMessage();
        message.setId(id);
        message.setResult(result);
        return message;
    }
    
    /**
     * 创建错误响应消息
     */
    public static McpMessage createError(Object id, McpError error) {
        McpMessage message = new McpMessage();
        message.setId(id);
        message.setError(error);
        return message;
    }
    
    /**
     * 创建通知消息（无ID）
     */
    public static McpMessage createNotification(String method, Object params) {
        McpMessage message = new McpMessage();
        message.setMethod(method);
        message.setParams(params);
        return message;
    }
    
    /**
     * 判断是否为请求消息
     */
    public boolean isRequest() {
        return method != null && id != null;
    }
    
    /**
     * 判断是否为响应消息
     */
    public boolean isResponse() {
        return id != null && (result != null || error != null);
    }
    
    /**
     * 判断是否为通知消息
     */
    public boolean isNotification() {
        return method != null && id == null;
    }
    
    /**
     * 判断是否为错误响应
     */
    public boolean isError() {
        return error != null;
    }
}
