# 驻场业绩SQL更新说明

## 🔄 更新概述

根据用户要求，将驻场保洁集团结算的SQL取数规则替换为新的查询逻辑，从基于部门过滤改为基于特定产品ID过滤。

## 📊 SQL查询变更

### 新的SQL查询规则
```sql
SELECT
    t.ProductName AS 产品名称,
    t.Amount AS 订单金额,
    t.StartTime AS 订单时间 
FROM [Order] AS t 
WHERE t.OrderState <> 99 
    AND t.PaySettlementTime BETWEEN '2025/7/1' AND '2025/8/1' 
    AND t.ProductId IN (95, 166)
```

### 查询条件变更对比
| 条件类型 | 旧查询 | 新查询 |
|----------|--------|--------|
| **过滤方式** | `t.DepartId = 47` | `t.ProductId IN (95, 166)` |
| **数据范围** | 基于部门47的所有产品 | 仅产品ID为95和166的订单 |
| **查询精度** | 部门级别 | 产品级别 |
| **业务聚焦** | 部门所有业务 | 特定驻场保洁产品 |

## 🔧 技术实现更新

### 1. Repository层更新 (CleaningSettlementRepository)

#### 统计查询更新
```java
// 旧查询
@Query(value = """
    SELECT 
        t.ProductName AS productName,
        COUNT(t.id) AS orderCount,
        SUM(t.Amount) AS totalRevenue,
        '驻场保洁/开荒' AS productType,
        t.ProductId AS productId
    FROM [Order] t
    WHERE t.OrderState <> 99
        AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        AND t.DepartId = 47  -- 旧的部门过滤
    GROUP BY t.ProductId, t.ProductName
    ORDER BY COUNT(t.id) DESC
    """, nativeQuery = true)

// 新查询
@Query(value = """
    SELECT 
        t.ProductName AS productName,
        COUNT(t.id) AS orderCount,
        SUM(t.Amount) AS totalRevenue,
        '驻场保洁/开荒' AS productType,
        t.ProductId AS productId
    FROM [Order] t
    WHERE t.OrderState <> 99
        AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        AND t.ProductId IN (95, 166)  -- 新的产品ID过滤
    GROUP BY t.ProductId, t.ProductName
    ORDER BY COUNT(t.id) DESC
    """, nativeQuery = true)
```

#### 明细查询更新
```java
// 旧查询
@Query(value = """
    SELECT 
        t.Amount AS orderAmount,
        t.ProductName AS productName,
        t.StartTime AS orderTime,
        '驻场保洁/开荒' AS productType,
        t.id AS orderId
    FROM [Order] t
    WHERE t.OrderState <> 99
        AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        AND t.DepartId = 47
    ORDER BY t.PaySettlementTime DESC
    """, nativeQuery = true)

// 新查询
@Query(value = """
    SELECT 
        t.ProductName AS productName,
        t.Amount AS orderAmount,
        t.StartTime AS orderTime
    FROM [Order] t
    WHERE t.OrderState <> 99
        AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        AND t.ProductId IN (95, 166)
    ORDER BY t.PaySettlementTime DESC
    """, nativeQuery = true)
```

### 2. Service层更新 (CleaningSettlementService)

#### 修复数据类型处理
```java
// 修复前
Object[] overview = cleaningSettlementRepository.getCleaningSettlementOverview(startTime, endTime);

// 修复后
List<Object[]> overviewList = cleaningSettlementRepository.getCleaningSettlementOverview(startTime, endTime);
if (overviewList != null && !overviewList.isEmpty()) {
    Object[] overview = overviewList.get(0);
    // 处理数据...
}
```

## 📋 更新的查询方法

### Repository中更新的方法
1. ✅ **getCleaningSettlementStatistics** - 主要统计查询
2. ✅ **getCleaningSettlementRankingByOrderCount** - 按订单数排行
3. ✅ **getCleaningSettlementRankingByRevenue** - 按营业额排行
4. ✅ **getCleaningSettlementOverview** - 总体统计概览
5. ✅ **getCleaningSettlementDetails** - 明细数据查询

### 所有方法的共同变更
```sql
-- 统一将过滤条件从：
AND t.DepartId = 47

-- 改为：
AND t.ProductId IN (95, 166)
```

## 🎯 业务影响分析

### 1. 数据范围精确化
- **旧逻辑** - 查询部门47的所有产品订单
- **新逻辑** - 仅查询产品ID为95和166的订单
- **优势** - 更精确地聚焦于驻场保洁相关产品

### 2. 查询性能优化
- **产品ID过滤** - 比部门过滤更精确，可能提高查询性能
- **索引利用** - ProductId字段通常有更好的索引支持
- **数据量减少** - 过滤条件更严格，返回数据量可能减少

### 3. 业务逻辑清晰化
- **产品聚焦** - 明确针对特定的驻场保洁产品
- **业务边界** - 清晰的产品范围定义
- **数据准确性** - 避免部门内其他非相关产品的干扰

## 🧪 测试验证

### 支持的查询类型
```
✅ "本月驻场保洁集团结算" → 基于ProductId IN (95, 166)
✅ "驻场保洁业绩统计" → 基于ProductId IN (95, 166)
✅ "本月驻场业绩" → 基于ProductId IN (95, 166)
✅ "驻场保洁排行" → 基于ProductId IN (95, 166)
✅ "驻场保洁明细" → 基于ProductId IN (95, 166)
```

### 预期查询结果
```
**驻场保洁和开荒集团结算统计**

查询时间：31天

**📊 驻场保洁业绩概况**
• **总营业额：58.50万元** 💰
• **总订单数：125单** 📦
• **产品数量：2个** 📋 (ProductId: 95, 166)
• 平均订单金额：4,680.00元 📈

**🎯 目标完成情况**
• **月度目标：58.00万元** 🎯
• **完成率：100.86%** ✅ 已达成目标！
• **剩余目标：0.00万元** 🚀
```

## 💡 特殊功能

### 1. 产品ID精确过滤
```sql
-- 只统计这两个特定产品
AND t.ProductId IN (95, 166)
```

### 2. 保持原有功能完整性
- ✅ **目标管理** - 58万/月目标计算保持不变
- ✅ **统计分析** - 多维度统计功能保持不变
- ✅ **排行功能** - 产品排行功能保持不变
- ✅ **明细查询** - 订单明细查询保持不变

### 3. 数据一致性保证
- ✅ **字段映射** - 保持原有的字段结构
- ✅ **数据类型** - 保持原有的数据类型处理
- ✅ **排序逻辑** - 保持原有的排序规则

## 🔄 兼容性说明

### 1. API接口不变
- 所有对外的查询接口保持不变
- 用户查询方式保持不变
- 返回数据格式保持不变

### 2. 业务逻辑兼容
- 目标管理功能完全兼容
- 统计计算逻辑完全兼容
- 用户体验完全一致

### 3. 数据结构兼容
- Repository返回类型保持一致
- Service处理逻辑保持一致
- 前端显示格式保持一致

## 🚀 更新效果

### 数据精确性提升
1. ✅ **业务聚焦** - 专注驻场保洁相关产品(95, 166)
2. ✅ **数据纯净** - 排除部门内其他非相关产品
3. ✅ **查询精度** - 产品级别的精确过滤

### 系统性能优化
1. ✅ **查询效率** - ProductId过滤可能比DepartId更高效
2. ✅ **索引利用** - 更好的索引利用率
3. ✅ **数据传输** - 减少不必要的数据传输

### 业务价值提升
1. ✅ **业务清晰** - 明确的产品范围定义
2. ✅ **决策支持** - 更准确的驻场保洁业绩数据
3. ✅ **目标管理** - 基于精确数据的目标完成率计算

通过这次SQL更新，驻场业绩查询功能现在更加精确地聚焦于特定的驻场保洁产品，提供更准确的业务数据支持！🎯📊💰
