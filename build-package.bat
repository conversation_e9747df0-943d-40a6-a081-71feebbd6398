@echo off
chcp 65001 >nul
echo ==========================================
echo    MCP Project Build Script
echo ==========================================

echo.
echo Step 1: Clean project...
call .\mvnw.cmd clean

echo.
echo Step 2: Package MCP Server...
call .\mvnw.cmd clean package -pl mcp-server -DskipTests=true

echo.
echo Step 3: Package MCP Client...
call .\mvnw.cmd clean package -pl mcp-client -DskipTests=true

echo.
echo Step 4: Create deploy directory...
if exist "deploy-package" rmdir /s /q "deploy-package"
mkdir "deploy-package"
mkdir "deploy-package\server"
mkdir "deploy-package\client"
mkdir "deploy-package\logs"
mkdir "deploy-package\view"

echo.
echo Step 5: Copy JAR files...
copy "mcp-server\target\mcp-server-1.0.0.jar" "deploy-package\server\"
copy "mcp-client\target\mcp-client-1.0.0.jar" "deploy-package\client\"

echo.
echo Step 6: Copy HTML files...
copy "view\*.html" "deploy-package\view\"

echo.
echo Step 7: Create startup scripts...

echo @echo off > "deploy-package\start-server.bat"
echo echo Starting MCP Server... >> "deploy-package\start-server.bat"
echo java -jar -Xms512m -Xmx1024m -Dspring.profiles.active=prod server/mcp-server-1.0.0.jar >> "deploy-package\start-server.bat"
echo pause >> "deploy-package\start-server.bat"

echo @echo off > "deploy-package\start-client.bat"
echo echo Starting MCP Client... >> "deploy-package\start-client.bat"
echo java -jar -Xms256m -Xmx512m -Dspring.profiles.active=prod client/mcp-client-1.0.0.jar >> "deploy-package\start-client.bat"
echo pause >> "deploy-package\start-client.bat"

echo @echo off > "deploy-package\start-all.bat"
echo echo Starting MCP Services... >> "deploy-package\start-all.bat"
echo start "MCP Server" cmd /c start-server.bat >> "deploy-package\start-all.bat"
echo timeout /t 15 >> "deploy-package\start-all.bat"
echo start "MCP Client" cmd /c start-client.bat >> "deploy-package\start-all.bat"
echo echo Services started! >> "deploy-package\start-all.bat"
echo echo MCP Server: http://localhost:8080 >> "deploy-package\start-all.bat"
echo echo MCP Client: http://localhost:8081 >> "deploy-package\start-all.bat"
echo echo AI Query Page: view/ai-query.html >> "deploy-package\start-all.bat"
echo pause >> "deploy-package\start-all.bat"

echo @echo off > "deploy-package\stop-all.bat"
echo taskkill /f /im java.exe >> "deploy-package\stop-all.bat"
echo echo Services stopped! >> "deploy-package\stop-all.bat"
echo pause >> "deploy-package\stop-all.bat"

echo.
echo Step 8: Create README file...
echo # MCP Deployment Package > "deploy-package\README.txt"
echo. >> "deploy-package\README.txt"
echo Environment Requirements: >> "deploy-package\README.txt"
echo - Java 17 or higher >> "deploy-package\README.txt"
echo - 2GB+ RAM >> "deploy-package\README.txt"
echo - Ports 8080, 8081 available >> "deploy-package\README.txt"
echo. >> "deploy-package\README.txt"
echo Quick Start: >> "deploy-package\README.txt"
echo 1. Double-click start-all.bat >> "deploy-package\README.txt"
echo 2. Wait for services to start >> "deploy-package\README.txt"
echo 3. Open view/ai-query.html in browser >> "deploy-package\README.txt"
echo 4. Access http://localhost:8081 >> "deploy-package\README.txt"

echo.
echo Step 9: Create ZIP package...
powershell -Command "Compress-Archive -Path 'deploy-package' -DestinationPath 'mcp-package.zip' -Force"

echo.
echo ==========================================
echo    Build Complete!
echo ==========================================
echo.
echo Package created: mcp-package.zip
echo.
echo Files included:
echo   - server/mcp-server-1.0.0.jar
echo   - client/mcp-client-1.0.0.jar
echo   - view/ai-query.html
echo   - start-all.bat
echo   - stop-all.bat
echo   - README.txt
echo.
echo To deploy:
echo   1. Extract mcp-package.zip
echo   2. Run start-all.bat
echo   3. Access http://localhost:8081
echo.
pause
