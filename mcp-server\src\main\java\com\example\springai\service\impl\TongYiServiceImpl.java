package com.example.springai.service.impl;

import com.example.springai.service.TongYiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 通义千问AI服务实现类
 * 暂时提供模拟实现，避免Spring AI依赖问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class TongYiServiceImpl implements TongYiService {

    @Override
    public String completion(String message) {
        log.info("收到文本对话请求: {}", message);
        
        // 暂时返回模拟响应
        return "" + message + "";
    }

    @Override
    public String streamCompletion(String message) {
        log.info("收到流式文本对话请求: {}", message);
        
        // 暂时返回模拟响应
        return "这是一个模拟的流式AI回复：" + message + "。当前Spring AI依赖暂时不可用，请稍后再试。";
    }

    @Override
    public String generateImage(String prompt) {
        log.info("收到图像生成请求: {}", prompt);
        
        // 暂时返回模拟响应
        return "模拟图像生成结果：根据提示词'" + prompt + "'生成的图像。当前Spring AI依赖暂时不可用，请稍后再试。";
    }

    @Override
    public String generateSpeech(String text) {
        log.info("收到语音合成请求: {}", text);
        
        // 暂时返回模拟响应
        return "模拟语音合成结果：文本'" + text + "'的语音文件路径。当前Spring AI依赖暂时不可用，请稍后再试。";
    }
}
