package com.example.springai.service.impl;

import com.example.springai.service.TongYiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 通义千问AI服务实现类
 * 暂时提供模拟实现，避免Spring AI依赖问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class TongYiServiceImpl implements TongYiService {

    @Override
    public String completion(String message) {
        log.info("收到文本对话请求: {}", message);

        // 检查是否是业务查询的友好回复生成请求
        if (message.contains("请基于以上数据，用友好、专业的语言回答用户的问题")) {
            return generateFriendlyBusinessResponse(message);
        }

        // 暂时返回模拟响应
        return "这是一个模拟的AI回复：" + message + "。当前Spring AI依赖暂时不可用，请稍后再试。";
    }

    /**
     * 生成友好的业务查询回复
     */
    private String generateFriendlyBusinessResponse(String prompt) {
        try {
            // 提取用户查询和数据
            String userQuery = extractUserQuery(prompt);
            String businessDataStr = extractBusinessData(prompt);

            // 简单的友好回复生成
            if (userQuery.contains("平台收入") || userQuery.contains("收入")) {
                return generatePlatformRevenueResponse(businessDataStr);
            } else if (userQuery.contains("业绩") || userQuery.contains("销售")) {
                return generateSalesResponse(businessDataStr);
            } else {
                return generateGenericResponse(businessDataStr);
            }
        } catch (Exception e) {
            log.warn("生成友好回复失败: {}", e.getMessage());
            return "查询完成，数据已获取。";
        }
    }

    private String extractUserQuery(String prompt) {
        int start = prompt.indexOf("用户查询：\"") + 5;
        int end = prompt.indexOf("\"", start);
        if (start > 4 && end > start) {
            return prompt.substring(start, end);
        }
        return "";
    }

    private String extractBusinessData(String prompt) {
        int start = prompt.indexOf("查询结果数据：");
        int end = prompt.indexOf("请基于以上数据");
        if (start >= 0 && end > start) {
            return prompt.substring(start + 7, end).trim();
        }
        return "";
    }

    private String generatePlatformRevenueResponse(String dataStr) {
        StringBuilder response = new StringBuilder();
        response.append("**平台收入查询结果**\n\n");

        // 提取总收入
        if (dataStr.contains("totalPlatformRevenue")) {
            String revenue = extractValue(dataStr, "totalPlatformRevenue");
            if (revenue != null) {
                response.append("平台总收入：").append(formatCurrency(revenue)).append("\n");
            }
        }

        // 提取时间信息
        if (dataStr.contains("period")) {
            String period = extractValue(dataStr, "period");
            if (period != null) {
                response.append("查询时间：").append(period.replace("\"", "")).append("\n");
            }
        }

        // 提取门店信息
        if (dataStr.contains("storeStatistics")) {
            response.append("\n**门店表现：**\n");
            response.append("详细门店数据已获取，包含各门店收入明细。\n");
        }

        response.append("\n数据查询成功完成。");
        return response.toString();
    }

    private String generateSalesResponse(String dataStr) {
        StringBuilder response = new StringBuilder();
        response.append("**业绩查询结果**\n\n");

        if (dataStr.contains("totalSales")) {
            String sales = extractValue(dataStr, "totalSales");
            if (sales != null) {
                response.append("总业绩：").append(formatCurrency(sales)).append("\n");
            }
        }

        response.append("业绩数据查询完成。");
        return response.toString();
    }

    private String generateGenericResponse(String dataStr) {
        return "**查询结果**\n\n数据查询成功完成，相关信息已获取。";
    }

    private String extractValue(String data, String key) {
        int keyIndex = data.indexOf("\"" + key + "\"");
        if (keyIndex == -1) return null;

        int colonIndex = data.indexOf(":", keyIndex);
        if (colonIndex == -1) return null;

        int start = colonIndex + 1;
        while (start < data.length() && (data.charAt(start) == ' ' || data.charAt(start) == '\t')) {
            start++;
        }

        if (start >= data.length()) return null;

        int end = start;
        if (data.charAt(start) == '"') {
            // String value
            start++;
            end = data.indexOf('"', start);
            if (end == -1) return null;
            return data.substring(start, end);
        } else {
            // Number value
            while (end < data.length() && (Character.isDigit(data.charAt(end)) || data.charAt(end) == '.')) {
                end++;
            }
            return data.substring(start, end);
        }
    }

    private String formatCurrency(String value) {
        try {
            double amount = Double.parseDouble(value);
            return String.format("%,.2f元", amount);
        } catch (NumberFormatException e) {
            return value + "元";
        }
    }

    @Override
    public String streamCompletion(String message) {
        log.info("收到流式文本对话请求: {}", message);
        
        // 暂时返回模拟响应
        return "这是一个模拟的流式AI回复：" + message + "。当前Spring AI依赖暂时不可用，请稍后再试。";
    }

    @Override
    public String generateImage(String prompt) {
        log.info("收到图像生成请求: {}", prompt);
        
        // 暂时返回模拟响应
        return "模拟图像生成结果：根据提示词'" + prompt + "'生成的图像。当前Spring AI依赖暂时不可用，请稍后再试。";
    }

    @Override
    public String generateSpeech(String text) {
        log.info("收到语音合成请求: {}", text);
        
        // 暂时返回模拟响应
        return "模拟语音合成结果：文本'" + text + "'的语音文件路径。当前Spring AI依赖暂时不可用，请稍后再试。";
    }
}
