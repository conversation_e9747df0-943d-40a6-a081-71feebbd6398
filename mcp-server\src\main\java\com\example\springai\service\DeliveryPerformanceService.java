package com.example.springai.service;

import com.example.springai.repository.DeliveryPerformanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 交付业绩数据服务
 * 处理交付业绩相关的查询，包含复购分析和目标管理
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class DeliveryPerformanceService {
    
    @Autowired
    private DeliveryPerformanceRepository deliveryPerformanceRepository;
    
    // 每月目标金额：480万元
    private static final BigDecimal MONTHLY_TARGET = new BigDecimal("4800000");
    
    /**
     * 获取交付业绩统计数据
     */
    public Map<String, Object> getDeliveryPerformanceStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询交付业绩统计: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            Object[] statsResult = deliveryPerformanceRepository.getDeliveryPerformanceStatistics(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "交付业绩统计");
            result.put("message", "查询交付业绩数据统计");
            
            if (statsResult != null && statsResult.length >= 6) {
                int totalOrderCount = statsResult[0] != null ? ((Number) statsResult[0]).intValue() : 0;
                BigDecimal totalPerformance = statsResult[1] != null ? (BigDecimal) statsResult[1] : BigDecimal.ZERO;
                int orderUserCount = statsResult[2] != null ? ((Number) statsResult[2]).intValue() : 0;
                BigDecimal repurchasePerformance = statsResult[3] != null ? (BigDecimal) statsResult[3] : BigDecimal.ZERO;
                int repurchaseUserCount = statsResult[4] != null ? ((Number) statsResult[4]).intValue() : 0;
                BigDecimal repurchaseRate = statsResult[5] != null ? (BigDecimal) statsResult[5] : BigDecimal.ZERO;
                
                result.put("totalOrderCount", totalOrderCount);
                result.put("totalPerformance", totalPerformance);
                result.put("orderUserCount", orderUserCount);
                result.put("repurchasePerformance", repurchasePerformance);
                result.put("repurchaseUserCount", repurchaseUserCount);
                result.put("repurchaseRate", repurchaseRate);
                
                // 计算新客业绩和新客人数
                BigDecimal newCustomerPerformance = totalPerformance.subtract(repurchasePerformance);
                int newCustomerCount = orderUserCount - repurchaseUserCount;
                result.put("newCustomerPerformance", newCustomerPerformance);
                result.put("newCustomerCount", newCustomerCount);
                
                // 计算平均订单金额
                if (totalOrderCount > 0) {
                    result.put("avgOrderAmount", totalPerformance.divide(BigDecimal.valueOf(totalOrderCount), 2, RoundingMode.HALF_UP));
                }
                
                // 计算客单价
                if (orderUserCount > 0) {
                    result.put("avgCustomerValue", totalPerformance.divide(BigDecimal.valueOf(orderUserCount), 2, RoundingMode.HALF_UP));
                }
                
                log.info("交付业绩统计结果 - 总订单: {}, 总业绩: {}, 下单人数: {}, 复购业绩: {}, 复购人数: {}, 复购率: {}%", 
                        totalOrderCount, totalPerformance, orderUserCount, repurchasePerformance, repurchaseUserCount, 
                        repurchaseRate.multiply(BigDecimal.valueOf(100)));
            } else {
                log.warn("交付业绩统计查询返回空结果");
                result.put("totalOrderCount", 0);
                result.put("totalPerformance", BigDecimal.ZERO);
                result.put("orderUserCount", 0);
                result.put("repurchasePerformance", BigDecimal.ZERO);
                result.put("repurchaseUserCount", 0);
                result.put("repurchaseRate", BigDecimal.ZERO);
            }
            
            // 计算目标完成情况
            BigDecimal actualPerformance = (BigDecimal) result.get("totalPerformance");
            calculateTargetCompletion(result, actualPerformance, startDate, endDate);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询交付业绩数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取交付业绩产品明细
     */
    public Map<String, Object> getDeliveryPerformanceProductDetails(LocalDate startDate, LocalDate endDate) {
        log.info("查询交付业绩产品明细: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> productDetails = deliveryPerformanceRepository.getDeliveryPerformanceProductDetails(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "交付业绩产品明细");
            result.put("message", "查询交付业绩产品明细数据");
            
            List<Map<String, Object>> productStats = formatProductDetails(productDetails);
            result.put("productDetails", productStats);
            
            // 计算总体统计
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalOrders = 0;
            int totalUsers = 0;
            
            for (Map<String, Object> product : productStats) {
                Object amount = product.get("totalAmount");
                Object orderCount = product.get("orderCount");
                Object userCount = product.get("userCount");
                
                if (amount instanceof BigDecimal) {
                    totalAmount = totalAmount.add((BigDecimal) amount);
                }
                if (orderCount instanceof Number) {
                    totalOrders += ((Number) orderCount).intValue();
                }
                if (userCount instanceof Number) {
                    totalUsers += ((Number) userCount).intValue();
                }
            }
            
            result.put("totalPerformance", totalAmount);
            result.put("totalOrderCount", totalOrders);
            result.put("totalUserCount", totalUsers);
            result.put("productCount", productStats.size());
            
            // 计算目标完成情况
            calculateTargetCompletion(result, totalAmount, startDate, endDate);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询交付业绩产品明细失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }

    /**
     * 计算目标完成情况
     */
    private void calculateTargetCompletion(Map<String, Object> result, BigDecimal actualAmount,
                                         LocalDate startDate, LocalDate endDate) {

        // 判断查询时间范围类型
        boolean isMonthlyQuery = isMonthlyPeriod(startDate, endDate);

        if (isMonthlyQuery) {
            // 月度目标完成率计算
            BigDecimal completionRate = actualAmount.divide(MONTHLY_TARGET, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            BigDecimal remainingTarget = MONTHLY_TARGET.subtract(actualAmount);

            result.put("monthlyTarget", MONTHLY_TARGET);
            result.put("completionRate", completionRate);
            result.put("remainingTarget", remainingTarget.max(BigDecimal.ZERO));
            result.put("isTargetAchieved", actualAmount.compareTo(MONTHLY_TARGET) >= 0);

            log.info("月度目标完成情况 - 目标: {}, 实际: {}, 完成率: {}%, 剩余: {}",
                    MONTHLY_TARGET, actualAmount, completionRate, remainingTarget);
        } else {
            // 非月度查询，按比例计算目标
            BigDecimal proportionalTarget = calculateProportionalTarget(startDate, endDate);
            if (proportionalTarget.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal completionRate = actualAmount.divide(proportionalTarget, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                BigDecimal remainingTarget = proportionalTarget.subtract(actualAmount);

                // 期间目标信息
                result.put("proportionalTarget", proportionalTarget);
                result.put("completionRate", completionRate);
                result.put("remainingTarget", remainingTarget.max(BigDecimal.ZERO));
                result.put("isTargetAchieved", actualAmount.compareTo(proportionalTarget) >= 0);

                // 同时返回总目标信息
                result.put("monthlyTarget", MONTHLY_TARGET);
                BigDecimal monthlyCompletionRate = actualAmount.divide(MONTHLY_TARGET, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                BigDecimal monthlyRemainingTarget = MONTHLY_TARGET.subtract(actualAmount);
                result.put("monthlyCompletionRate", monthlyCompletionRate);
                result.put("monthlyRemainingTarget", monthlyRemainingTarget.max(BigDecimal.ZERO));
                result.put("isMonthlyTargetAchieved", actualAmount.compareTo(MONTHLY_TARGET) >= 0);

                // 计算查询天数和月度天数信息
                long queryDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                long monthDays = startDate.lengthOfMonth();
                result.put("queryDays", queryDays);
                result.put("monthDays", monthDays);
                result.put("targetRatio", BigDecimal.valueOf(queryDays).divide(BigDecimal.valueOf(monthDays), 4, RoundingMode.HALF_UP));

                log.info("比例目标完成情况 - 期间目标: {}, 月度目标: {}, 实际: {}, 期间完成率: {}%, 月度完成率: {}%",
                        proportionalTarget, MONTHLY_TARGET, actualAmount, completionRate, monthlyCompletionRate);
            }
        }
    }

    /**
     * 判断是否为月度查询
     */
    private boolean isMonthlyPeriod(LocalDate startDate, LocalDate endDate) {
        return startDate.getDayOfMonth() == 1 &&
               endDate.equals(startDate.withDayOfMonth(startDate.lengthOfMonth()));
    }

    /**
     * 计算比例目标
     */
    private BigDecimal calculateProportionalTarget(LocalDate startDate, LocalDate endDate) {
        long queryDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        long monthDays = startDate.lengthOfMonth();

        return MONTHLY_TARGET.multiply(BigDecimal.valueOf(queryDays))
            .divide(BigDecimal.valueOf(monthDays), 2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化产品明细数据
     */
    private List<Map<String, Object>> formatProductDetails(List<Object[]> productDetails) {
        return productDetails.stream()
                .map(data -> {
                    Map<String, Object> productDetail = new HashMap<>();
                    productDetail.put("productName", data[0] != null ? data[0].toString() : "未知产品");
                    productDetail.put("orderCount", data[1] != null ? ((Number) data[1]).intValue() : 0);
                    productDetail.put("totalAmount", data[2] != null ? (BigDecimal) data[2] : BigDecimal.ZERO);
                    productDetail.put("userCount", data[3] != null ? ((Number) data[3]).intValue() : 0);
                    return productDetail;
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
