server:
  port: 8081

spring:
  application:
    name: mcp-client
  mvc:
    async:
      request-timeout: 60000  # 60秒超时

# MCP客户端配置
mcp:
  client:
    # 默认MCP服务器地址
    server-url: ws://localhost:8080/mcp
    # 部署时替换MCP服务器地址
#    server-url: mcp-server:8080/mcp
    # 连接超时时间（秒）
    connect-timeout: 30
    # 请求超时时间（秒）
    request-timeout: 60

# 日志配置
logging:
  level:
    com.example.springai: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
