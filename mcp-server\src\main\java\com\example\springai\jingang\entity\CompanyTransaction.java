package com.example.springai.jingang.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 金刚到家公司交易记录实体类
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "company_transaction")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyTransaction {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 交易类型 (1-收入, 2-支出)
     */
    @Column(name = "transaction_type")
    private Integer transactionType;

    /**
     * 交易金额
     */
    @Column(name = "amount", precision = 10, scale = 2)
    private BigDecimal amount;

    /**
     * 交易描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 业务类型
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 业务类型名称
     */
    @Column(name = "business_type_name")
    private String businessTypeName;

    /**
     * 支付类型
     */
    @Column(name = "pay_type")
    private String payType;

    /**
     * 交易状态 (如果表中存在此字段)
     * 注释掉因为实际表中可能没有此字段
     */
    // @Column(name = "status")
    // private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 交易时间
     */
    @Column(name = "transaction_time")
    private LocalDateTime transactionTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 是否为收入类型
     */
    public boolean isIncome() {
        return transactionType != null && transactionType == 1;
    }

    /**
     * 是否为支出类型
     */
    public boolean isExpense() {
        return transactionType != null && transactionType == 2;
    }

    /**
     * 是否已完成 (由于表中可能没有status字段，暂时返回true)
     */
    public boolean isCompleted() {
        // return status != null && status == 2;
        return true; // 暂时假设所有记录都是已完成的
    }
}
