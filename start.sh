#!/bin/bash

echo "================================"
echo "Spring AI Alibaba 项目启动脚本"
echo "================================"
echo

echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误：未找到Java环境，请确保已安装JDK 17或更高版本"
    exit 1
fi
java -version

echo
echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "错误：未找到Maven环境，请确保已安装Maven 3.6或更高版本"
    exit 1
fi
mvn -version

echo
echo "编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "错误：项目编译失败"
    exit 1
fi

echo
echo "启动应用..."
echo "注意：请确保已在application.yml中配置正确的API Key"
echo
mvn spring-boot:run
