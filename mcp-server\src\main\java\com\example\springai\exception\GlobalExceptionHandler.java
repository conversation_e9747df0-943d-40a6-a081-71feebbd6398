package com.example.springai.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 拦截所有异常并返回友好的提示信息
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 友好的错误提示信息
     */
    private static final String FRIENDLY_ERROR_MESSAGE = "当前问题我还没学会，请给我点时间，我正在学习中哦~";

    /**
     * 处理所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        // 记录详细的异常信息到日志
        log.error("系统异常: {}", e.getMessage(), e);
        
        // 返回友好的提示信息
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理类型转换异常
     */
    @ExceptionHandler(ClassCastException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleClassCastException(ClassCastException e) {
        log.error("类型转换异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * 处理数据库相关异常
     */
    @ExceptionHandler({
        org.springframework.dao.DataAccessException.class,
        java.sql.SQLException.class
    })
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleDataAccessException(Exception e) {
        log.error("数据库异常: {}", e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", FRIENDLY_ERROR_MESSAGE);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}
