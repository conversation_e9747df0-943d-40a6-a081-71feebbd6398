package com.example.springai.mcp.server;

import com.example.springai.mcp.protocol.*;
import com.example.springai.service.TongYiService;
import com.example.springai.service.BusinessAIService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP服务器实现
 * 提供AI工具和资源的MCP服务
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Component
public class McpServer implements WebSocketHandler {
    
    private final TongYiService tongYiService;
    private final BusinessAIService businessAIService;
    private final ObjectMapper objectMapper;
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    // 服务器信息
    private static final String SERVER_NAME = "spring-ai-alibaba-mcp-server";
    private static final String SERVER_VERSION = "1.0.0";

    @Autowired
    public McpServer(TongYiService tongYiService, BusinessAIService businessAIService, ObjectMapper objectMapper) {
        this.tongYiService = tongYiService;
        this.businessAIService = businessAIService;
        this.objectMapper = objectMapper;
    }
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        log.info("MCP客户端连接建立: {}", sessionId);
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            log.debug("收到MCP消息: {}", payload);
            
            try {
                McpMessage mcpMessage = objectMapper.readValue(payload, McpMessage.class);
                McpMessage response = handleMcpMessage(mcpMessage);
                
                if (response != null) {
                    String responseJson = objectMapper.writeValueAsString(response);
                    session.sendMessage(new TextMessage(responseJson));
                    log.debug("发送MCP响应: {}", responseJson);
                }
                
            } catch (Exception e) {
                log.error("处理MCP消息失败", e);
                McpMessage errorResponse = McpMessage.createError(
                    null, 
                    McpError.internalError("消息处理失败: " + e.getMessage())
                );
                String errorJson = objectMapper.writeValueAsString(errorResponse);
                session.sendMessage(new TextMessage(errorJson));
            }
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("MCP传输错误: {}", session.getId(), exception);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        log.info("MCP客户端连接关闭: {}, 状态: {}", sessionId, closeStatus);
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 处理MCP消息
     */
    private McpMessage handleMcpMessage(McpMessage message) {
        if (message.isRequest()) {
            return handleRequest(message);
        } else if (message.isNotification()) {
            handleNotification(message);
            return null;
        } else {
            log.warn("收到未知类型的MCP消息: {}", message);
            return McpMessage.createError(
                message.getId(),
                McpError.invalidRequest("未知的消息类型")
            );
        }
    }
    
    /**
     * 处理请求消息
     */
    private McpMessage handleRequest(McpMessage request) {
        String method = request.getMethod();
        Object id = request.getId();
        
        try {
            switch (method) {
                case "initialize":
                    return handleInitialize(id, request.getParams());
                case "tools/list":
                    return handleListTools(id);
                case "tools/call":
                    return handleCallTool(id, request.getParams());
                case "resources/list":
                    return handleListResources(id);
                case "resources/read":
                    return handleReadResource(id, request.getParams());
                default:
                    return McpMessage.createError(id, McpError.methodNotFound(method));
            }
        } catch (Exception e) {
            log.error("处理请求失败: {}", method, e);
            return McpMessage.createError(id, McpError.internalError(e.getMessage()));
        }
    }
    
    /**
     * 处理通知消息
     */
    private void handleNotification(McpMessage notification) {
        String method = notification.getMethod();
        log.debug("收到通知: {}", method);
        
        switch (method) {
            case "initialized":
                log.info("MCP客户端初始化完成");
                break;
            default:
                log.warn("未知的通知方法: {}", method);
        }
    }
    
    /**
     * 处理初始化请求
     */
    private McpMessage handleInitialize(Object id, Object params) {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("serverInfo", Map.of(
            "name", SERVER_NAME,
            "version", SERVER_VERSION
        ));
        result.put("capabilities", Map.of(
            "tools", Map.of("listChanged", true),
            "resources", Map.of("subscribe", true, "listChanged", true)
        ));
        
        return McpMessage.createResponse(id, result);
    }
    
    /**
     * 处理工具列表请求
     */
    private McpMessage handleListTools(Object id) {
        List<McpTool> tools = Arrays.asList(
            McpTool.createChatTool(),
            McpTool.createStreamChatTool(),
            McpTool.createImageTool(),
            McpTool.createBusinessQueryTool()
        );

        Map<String, Object> result = Map.of("tools", tools);
        return McpMessage.createResponse(id, result);
    }
    
    /**
     * 处理资源列表请求
     */
    private McpMessage handleListResources(Object id) {
        List<McpResource> resources = Arrays.asList(
            McpResource.createModelInfoResource(),
            McpResource.createChatHistoryResource(),
            McpResource.createSystemStatusResource()
        );

        Map<String, Object> result = Map.of("resources", resources);
        return McpMessage.createResponse(id, result);
    }

    /**
     * 处理工具调用请求
     */
    @SuppressWarnings("unchecked")
    private McpMessage handleCallTool(Object id, Object params) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) params;
            String toolName = (String) paramMap.get("name");
            Map<String, Object> arguments = (Map<String, Object>) paramMap.get("arguments");

            log.info("调用工具: {}, 参数: {}", toolName, arguments);

            Object result = callTool(toolName, arguments);

            Map<String, Object> response = Map.of(
                "content", List.of(Map.of(
                    "type", "text",
                    "text", result.toString()
                )),
                "isError", false
            );

            return McpMessage.createResponse(id, response);

        } catch (Exception e) {
            log.error("工具调用失败", e);
            Map<String, Object> errorResponse = Map.of(
                "content", List.of(Map.of(
                    "type", "text",
                    "text", "当前问题我还没学会，请给我点时间，我正在学习中哦~"
                )),
                "isError", false
            );
            return McpMessage.createResponse(id, errorResponse);
        }
    }

    /**
     * 处理资源读取请求
     */
    @SuppressWarnings("unchecked")
    private McpMessage handleReadResource(Object id, Object params) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) params;
            String uri = (String) paramMap.get("uri");

            log.info("读取资源: {}", uri);

            Object content = readResource(uri);

            Map<String, Object> result = Map.of(
                "contents", List.of(Map.of(
                    "uri", uri,
                    "mimeType", "application/json",
                    "text", content.toString()
                ))
            );

            return McpMessage.createResponse(id, result);

        } catch (Exception e) {
            log.error("资源读取失败", e);
            return McpMessage.createError(id, McpError.resourceNotFound(e.getMessage()));
        }
    }

    /**
     * 执行具体的工具调用
     */
    private Object callTool(String toolName, Map<String, Object> arguments) {
        switch (toolName) {
            case "ai_chat":
                String message = (String) arguments.get("message");
                return tongYiService.completion(message);

            case "ai_stream_chat":
                String streamMessage = (String) arguments.get("message");
                return tongYiService.streamCompletion(streamMessage);

            case "ai_image":
                String prompt = (String) arguments.get("prompt");
                try {
                    String imageResponse = tongYiService.generateImage(prompt);
                    return "图像生成成功: " + imageResponse;
                } catch (Exception e) {
                    return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
                }

            case "business_query":
                String query = (String) arguments.get("query");
                return businessAIService.handleBusinessQuery(query);

            default:
                throw new IllegalArgumentException("未知的工具: " + toolName);
        }
    }

    /**
     * 读取具体的资源内容
     */
    private Object readResource(String uri) {
        switch (uri) {
            case "ai://model/info":
                return Map.of(
                    "modelName", "通义千问",
                    "provider", "阿里云",
                    "version", "qwen-turbo",
                    "capabilities", List.of("文本对话", "图像生成", "流式对话")
                );

            case "ai://chat/history":
                return Map.of(
                    "totalSessions", 0,
                    "recentChats", List.of(),
                    "lastActivity", System.currentTimeMillis()
                );

            case "ai://system/status":
                return Map.of(
                    "status", "运行中",
                    "uptime", System.currentTimeMillis(),
                    "activeConnections", sessions.size(),
                    "serverInfo", Map.of(
                        "name", SERVER_NAME,
                        "version", SERVER_VERSION
                    )
                );

            default:
                throw new IllegalArgumentException("未知的资源URI: " + uri);
        }
    }
}
