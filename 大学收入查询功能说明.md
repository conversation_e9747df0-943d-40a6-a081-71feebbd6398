# 大学收入查询功能说明

## 🎯 功能概述

新增大学收入数据查询服务，基于ProductId=361的订单数据，支持自然语言查询大学收入统计、排行等信息。

## 📊 数据源说明

### SQL基础查询
```sql
SELECT
    Amount as 订单金额,
    ProductName as 产品名称,
    CreateTime as 收款时间
FROM [Order]
WHERE ProductId = 361
    AND RealTotalAmount = Amount
    AND OrderState != 99
    AND Amount < 10000
    AND CreateTime BETWEEN '2025-06-01' AND '2025-07-01'
```

### 查询条件
- **ProductId = 361** - 大学收入专用产品ID
- **RealTotalAmount = Amount** - 确保实际金额与订单金额一致
- **OrderState != 99** - 排除无效订单
- **Amount < 10000** - 限制订单金额小于1万元
- **CreateTime** - 基于订单创建时间范围

## 🔍 支持的查询类型

### 1. 基础统计查询
**触发关键词：** "大学"、"高校"、"学校"、"校园" + "收入"、"营收"、"业绩"

**查询示例：**
- "本月大学收入"
- "上周高校收入统计"
- "大学收入情况"
- "校园收入业绩"

**返回内容：**
- 总体概况（产品数量、订单数、总收入）
- 平均订单金额
- 产品明细列表

### 2. 排行榜查询
**触发关键词：** "大学收入排行"、"高校收入排行"、"学校收入排行"

**查询示例：**
- "大学收入排行"
- "高校收入营业额排行"
- "学校收入订单数排行"

**排序方式：**
- 默认按订单数排序
- 包含"营业额"或"收入"时按营业额排序

### 3. 时间范围支持
- **今天、昨天**
- **本周、上周**
- **本月、上月**
- **近7天、近30天**
- **自定义时间范围**

## 📋 返回数据格式

### 统计查询返回示例
```
**大学收入统计**

查询时间：30天

**📊 总体概况**
• **产品数量：1个** 🎓
• **总订单数：156单** 📦
• **总收入：45.67万元** 💰
• 平均订单金额：2,927.56元 📊

**📋 产品明细**
🥇 **大学服务套餐**：156单，45.67万元
```

### 排行榜查询返回示例
```
**大学收入排行(订单数)**

查询时间：30天

**📊 总体概况**
• **产品数量：1个** 🎓
• **总订单数：156单** 📦
• **总收入：45.67万元** 💰
• 平均订单金额：2,927.56元 📊

**🏆 产品排行榜（按订单数）**
🥇 **大学服务套餐**：156单，45.67万元
```

## 🔧 技术架构

### 1. 服务层结构
- **UniversityIncomeRepository** - 数据访问层
- **UniversityIncomeService** - 业务逻辑层
- **BusinessAIService** - AI集成层

### 2. 核心方法
```java
// 基础统计查询
getUniversityIncomeStatistics(startDate, endDate)

// 按订单数排行
getUniversityIncomeRankingByOrderCount(startDate, endDate, limit)

// 按营业额排行
getUniversityIncomeRankingByRevenue(startDate, endDate, limit)

// 总体统计概览
getUniversityIncomeOverview(startDate, endDate)
```

### 3. 数据处理特性
- **累加统计验证** - 从明细数据累加验证总体统计
- **智能数据选择** - 优先使用准确的非零数据
- **货币格式化** - 大金额自动转换为万元显示
- **排名图标** - 前5名使用emoji图标显示

## 🧪 测试用例

### 正常查询测试
```
✅ "本月大学收入" → 返回本月统计数据
✅ "大学收入排行" → 返回排行榜数据
✅ "上周高校收入统计" → 返回上周数据
✅ "学校收入营业额排行" → 按营业额排序
```

### 不合理查询测试
```
❌ "下月大学收入" → 幽默拒绝
❌ "明天高校收入" → 未来时间拒绝
❌ "去年大学收入" → 久远时间提示
```

### 边界情况测试
```
🔍 无数据时 → 显示"暂无统计数据"
🔍 单个产品 → 正常显示统计信息
🔍 大量产品 → 限制显示前5个，提示剩余数量
```

## 📈 数据统计特性

### 1. 多维度统计
- **产品维度** - 按产品分组统计
- **时间维度** - 支持各种时间范围
- **排序维度** - 订单数/营业额双重排序

### 2. 智能计算
- **平均订单金额** - 总收入 ÷ 总订单数
- **平均单品收入** - 总收入 ÷ 产品数量
- **占比分析** - 头部产品占总体的百分比

### 3. 数据验证
- **数据库统计** - SQL聚合查询结果
- **程序计算** - 从明细数据累加计算
- **一致性检查** - 确保数据准确性

## 🎨 用户体验优化

### 1. 友好的回复格式
- 使用🎓emoji图标增强视觉效果
- 清晰的层级结构
- 重要数据加粗显示

### 2. 智能查询识别
- 支持多种表达方式
- 自动识别查询意图
- 容错性强的关键词匹配

### 3. 合理的数据展示
- 限制显示数量避免信息过载
- 提供剩余数据数量提示
- 突出显示关键统计信息

## 🔍 查询识别逻辑

### 关键词匹配
```java
// 大学相关关键词
"大学" || "高校" || "学校" || "校园"

// 收入相关关键词  
"收入" || "营收" || "业绩" || "统计" || "排行" || "情况"

// 排除其他查询
!("三嫂" || "交付" || "新品" || "金刚" || "保洁" || "驻场")
```

### 优先级设置
1. 新品成交查询
2. 全国交付业绩查询
3. 驻场保洁集团结算查询
4. **大学收入查询** ← 新增
5. 三嫂业绩查询
6. 金刚查询

## 🚀 使用示例

### 基础查询
```
用户：本月大学收入
系统：返回本月大学收入统计数据
```

### 排行查询
```
用户：大学收入排行
系统：返回按订单数排序的产品排行榜
```

### 营业额排行
```
用户：高校收入营业额排行
系统：返回按营业额排序的产品排行榜
```

## 💡 特殊限制

### 金额限制
- **Amount < 10000** - 只统计订单金额小于1万元的订单
- 这个限制确保统计的是小额订单，可能是为了过滤大额异常订单

### 数据一致性
- **RealTotalAmount = Amount** - 确保实际金额与订单金额一致
- 这个条件确保数据的准确性和一致性

### 订单状态
- **OrderState != 99** - 排除特定状态的订单
- 确保只统计有效的订单数据

通过这个大学收入查询功能，用户可以方便地了解大学相关业务的收入情况和产品表现！
