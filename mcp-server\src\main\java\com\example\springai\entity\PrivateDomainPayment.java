package com.example.springai.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 私域ABC收款业绩实体类
 * 用于支持私域收款查询的Repository
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Entity
@Table(name = "Payment")
@Data
public class PrivateDomainPayment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "OrderNo")
    private String orderNo;
    
    @Column(name = "Amount")
    private BigDecimal amount;
    
    @Column(name = "PayTime")
    private LocalDateTime payTime;
    
    @Column(name = "PaymentStatus")
    private String paymentStatus;
    
    @Column(name = "memberid")
    private Long memberId;
}
