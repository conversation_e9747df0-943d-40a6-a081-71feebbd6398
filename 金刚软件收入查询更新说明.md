# 金刚软件收入查询更新说明

## 🔄 更新概述

根据用户要求，将金刚到家软件收入的SQL查询替换为新的查询逻辑，并统一修改相关业务功能，添加目标管理功能。

## 📊 新的SQL查询

### 核心查询逻辑
```sql
SELECT
    transaction_time as 交易时间,
    amount as 交易金额,
    description as 交易描述,
    remark as 交易备注
FROM
    company_transaction 
WHERE
    transaction_time BETWEEN '2025-07-01' AND '2025-07-30'
    AND transaction_status = 'SUCCESS'
    AND business_type = 'SOFTWARE_RENEWAL'
ORDER BY
    transaction_time DESC,
    amount DESC
```

### 查询条件变更
| 条件类型 | 旧查询 | 新查询 |
|----------|--------|--------|
| **状态过滤** | `transaction_type = 1` | `transaction_status = 'SUCCESS'` |
| **业务类型** | 无特定限制 | `business_type = 'SOFTWARE_RENEWAL'` |
| **数据范围** | 所有收入类型 | 仅软件续费收入 |
| **字段别名** | 无别名 | 中文别名（交易时间、交易金额等） |

## 🔧 技术实现更新

### 1. Repository层更新 (CompanyTransactionRepository)

#### 统计查询更新
```java
// 旧查询
@Query(value = "SELECT COALESCE(SUM(amount), 0) " +
       "FROM company_transaction " +
       "WHERE transaction_type = 1 " +
       "AND transaction_time BETWEEN :startTime AND :endTime", nativeQuery = true)

// 新查询
@Query(value = "SELECT COALESCE(SUM(amount), 0) " +
       "FROM company_transaction " +
       "WHERE transaction_status = 'SUCCESS' " +
       "AND business_type = 'SOFTWARE_RENEWAL' " +
       "AND transaction_time BETWEEN :startTime AND :endTime", nativeQuery = true)
```

#### 明细查询更新
```java
// 旧查询
@Query(value = "SELECT transaction_time, amount, description, remark " +
       "FROM company_transaction " +
       "WHERE transaction_type = 1 " +
       "AND transaction_time BETWEEN :startTime AND :endTime " +
       "ORDER BY transaction_time DESC, amount DESC", nativeQuery = true)

// 新查询
@Query(value = "SELECT transaction_time as 交易时间, amount as 交易金额, description as 交易描述, remark as 交易备注 " +
       "FROM company_transaction " +
       "WHERE transaction_status = 'SUCCESS' " +
       "AND business_type = 'SOFTWARE_RENEWAL' " +
       "AND transaction_time BETWEEN :startTime AND :endTime " +
       "ORDER BY transaction_time DESC, amount DESC", nativeQuery = true)
```

### 2. Service层更新 (JingangIncomeService)

#### 新增统计查询方法
```java
/**
 * 获取金刚软件收入统计数据
 */
public Map<String, Object> getJingangIncomeStatistics(LocalDate startDate, LocalDate endDate) {
    // 获取汇总数据
    Object[] summary = companyTransactionRepository.findIncomeSummary(startTime, endTime);
    
    // 获取明细数据
    List<Object[]> details = companyTransactionRepository.findIncomeDetails(startTime, endTime);
    
    // 计算目标完成情况
    calculateTargetCompletion(result, totalAmount, startDate, endDate);
    
    return result;
}
```

#### 目标管理功能
```java
// 每月目标金额：21万元
private static final BigDecimal MONTHLY_TARGET = new BigDecimal("210000");

/**
 * 计算目标完成情况
 */
private void calculateTargetCompletion(Map<String, Object> result, BigDecimal actualAmount, 
                                     LocalDate startDate, LocalDate endDate) {
    // 月度目标完成率计算
    // 期间目标比例计算
    // 剩余目标金额计算
}
```

### 3. Business层更新 (JingangBusinessService)

#### 查询意图识别更新
```java
// 新增统计查询类型识别
} else if (query.contains("统计") && !query.contains("分类")) {
    intent.setQueryType("统计");
} else {
    intent.setQueryType("统计"); // 默认使用新的统计查询
}
```

#### 新增统计查询处理
```java
case "统计":
    // 新增：使用新的统计查询方法
    return handleJingangStatisticsQuery(intent.getTimeRange());
```

## 📋 返回数据格式

### 统计查询返回数据
```java
{
    "startDate": "2025-07-01",
    "endDate": "2025-07-11", 
    "period": "11天",
    "queryType": "金刚软件收入统计",
    "totalAmount": 45000.00,        // 总收入金额
    "totalCount": 15,               // 交易笔数
    "avgAmount": 3000.00,           // 平均交易金额
    "transactionDetails": [...],    // 交易明细列表
    
    // 目标管理数据
    "monthlyTarget": 210000,        // 月度目标
    "completionRate": 21.43,        // 完成率
    "remainingTarget": 165000,      // 剩余目标
    "isTargetAchieved": false       // 是否达成目标
}
```

### 交易明细数据格式
```java
{
    "交易时间": "2025-07-10 14:30:00",
    "交易金额": 5000.00,
    "交易描述": "软件续费服务",
    "交易备注": "年度续费"
}
```

## 🎨 用户界面显示

### 统计查询显示示例
```
**金刚软件收入统计**

查询时间：11天

**💰 金刚软件收入概况**
• **总收入：4.50万元** 💰
• **交易笔数：15笔** 📊
• 平均交易金额：3,000.00元 📈

**🎯 目标完成情况**
• **月度目标：21.00万元** 🎯
• **完成率：21.43%** 📈
• **剩余目标：16.50万元** 🚀
```

### 期间查询显示示例
```
**🎯 目标完成情况**
• **月度总目标：21.00万元** 🎯
• **期间目标：7.45万元** 📅
• 查询时间：11天 / 31天
• **期间完成率：60.40%** 📈
• **期间剩余：2.95万元** 🚀
• **月度完成率：21.43%** 📊
• **月度剩余：16.50万元** 🎯
```

## 🧪 测试用例

### 支持的查询类型
```
✅ "本月金刚软件收入" → 统计查询
✅ "金刚软件收入统计" → 统计查询
✅ "本月金刚到家收入" → 统计查询
✅ "金刚软件收入明细" → 明细查询
✅ "金刚软件收入分类统计" → 分类统计查询
```

### 时间范围支持
```
✅ "本月金刚软件收入" → 当月1日到当前日期
✅ "上月金刚软件收入" → 上月1日到上月最后一天
✅ "本周金刚软件收入" → 本周一到当前日期
✅ "上周金刚软件收入" → 上周一到上周日
```

## 🔄 业务逻辑变更

### 1. 数据范围精确化
- **旧逻辑** - 查询所有收入类型的交易记录
- **新逻辑** - 仅查询软件续费相关的成功交易记录

### 2. 查询条件优化
- **状态过滤** - 只统计成功的交易
- **业务类型** - 专注于软件续费业务
- **数据质量** - 提高查询结果的准确性

### 3. 目标管理集成
- **月度目标** - 21万元/月
- **完成率计算** - 实际收入 ÷ 目标金额 × 100%
- **剩余目标** - 目标金额 - 实际收入
- **期间目标** - 按天数比例计算的期间目标

## 💡 特殊功能

### 1. 智能查询识别
```java
// 优先级调整
if (query.contains("统计") && !query.contains("分类")) {
    intent.setQueryType("统计");  // 新的统计查询
} else {
    intent.setQueryType("统计");  // 默认使用统计查询
}
```

### 2. 货币格式化
```java
private String formatCurrency(BigDecimal amount) {
    if (amount.compareTo(BigDecimal.valueOf(10000)) >= 0) {
        BigDecimal wan = amount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP);
        return wan.toString() + "万元";
    } else {
        return amount.setScale(2, RoundingMode.HALF_UP).toString() + "元";
    }
}
```

### 3. 时间范围解析
```java
private LocalDate[] parseTimeRangeToDate(String timeRange) {
    // 支持本月、上月、本周、上周等时间范围
    // 返回精确的开始和结束日期
}
```

## 🚀 更新效果

### 数据准确性提升
1. ✅ **业务聚焦** - 专注软件续费收入，排除其他业务干扰
2. ✅ **状态过滤** - 只统计成功交易，避免失败交易影响
3. ✅ **字段清晰** - 使用中文别名，提高可读性

### 功能完整性增强
1. ✅ **目标管理** - 完整的目标设置和完成率计算
2. ✅ **统计分析** - 多维度的收入统计和分析
3. ✅ **用户体验** - 友好的查询结果展示

### 系统一致性保证
1. ✅ **查询模式** - 与其他业务查询保持一致的模式
2. ✅ **错误处理** - 统一的异常处理和友好提示
3. ✅ **数据安全** - 完善的空值检查和类型转换

## 📈 业务价值

### 1. 精准的业务监控
- 专注软件续费业务的收入统计
- 排除其他业务类型的干扰
- 提供准确的业务决策数据

### 2. 完整的目标管理
- 明确的月度目标设置（21万元）
- 实时的完成率监控
- 清晰的剩余目标提醒

### 3. 友好的用户体验
- 自然语言查询支持
- 直观的数据展示格式
- 智能的查询意图识别

通过这次更新，金刚软件收入查询功能现在更加精准、完整和用户友好！🎯💰📊
