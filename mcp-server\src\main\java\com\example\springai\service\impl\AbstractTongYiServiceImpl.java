package com.example.springai.service.impl;

import com.example.springai.service.TongYiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 通义千问AI服务抽象实现类
 * 提供通用的工具方法
 * 
 * <AUTHOR> AI Alibaba Demo
 * @version 1.0.0
 */
@Slf4j
public abstract class AbstractTongYiServiceImpl implements TongYiService {

    /**
     * 音频文件保存目录
     */
    private static final String AUDIO_SAVE_DIR = "audio";

    /**
     * 保存音频文件到本地
     *
     * @param audioData 音频数据
     * @param format 音频格式
     * @return 保存的文件路径
     */
    protected String saveAudioFile(byte[] audioData, String format) {
        try {
            // 创建保存目录
            Path audioDir = Paths.get(AUDIO_SAVE_DIR);
            if (!Files.exists(audioDir)) {
                Files.createDirectories(audioDir);
            }

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("speech_%s.%s", timestamp, format);
            Path filePath = audioDir.resolve(fileName);

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
                fos.write(audioData);
            }

            String absolutePath = filePath.toAbsolutePath().toString();
            log.info("音频文件保存成功：{}", absolutePath);
            return absolutePath;

        } catch (IOException e) {
            log.error("保存音频文件失败", e);
            throw new RuntimeException("保存音频文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证输入参数
     * 
     * @param input 输入参数
     * @param paramName 参数名称
     */
    protected void validateInput(String input, String paramName) {
        if (!StringUtils.hasText(input)) {
            throw new IllegalArgumentException(paramName + " 不能为空");
        }
    }
}
