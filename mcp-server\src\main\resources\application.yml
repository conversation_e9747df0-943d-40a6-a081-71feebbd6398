server:
  port: 8080

spring:
  application:
    name: spring-ai-alibaba-mcp-server

  # 数据库配置
  datasource:
    # 主数据源 - SQL Server (小语家)
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: ********************************************************************************************************************************
    username: xyj2025
    password: Xyj&5170618
    hikari:
      pool-name: HikariPool-1
      minimum-idle: 2
      maximum-pool-size: 10
      connection-timeout: 60000
      idle-timeout: 300000
      max-lifetime: 900000
      leak-detection-threshold: 60000
      initialization-fail-timeout: -1
      connection-test-query: SELECT 1

    # 金刚到家数据源 - MySQL
    jingang:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************************************************************
      username: jingang
      password: jg$5170701
      hikari:
        pool-name: JingangHikariPool
        minimum-idle: 2
        maximum-pool-size: 8
        connection-timeout: 60000
        idle-timeout: 300000
        max-lifetime: 900000
        leak-detection-threshold: 60000
        initialization-fail-timeout: -1
        connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none  # 不自动创建表，使用现有数据库结构
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.SQLServerDialect

  cloud:
    ai:
      tongyi:
        # 阿里云通义千问API密钥，请替换为您的实际API密钥
        # 获取地址：https://dashscope.console.aliyun.com/model
        api-key: sk-1119519ea22c42a4a9ed9e1cf5f8561c
        
        # 聊天模型配置
        chat:
          options:
            # 模型名称，可选值：qwen-turbo, qwen-plus, qwen-max 等
            model: qwen-turbo
            # 生成文本的随机性，0.0-2.0，值越高越随机
            temperature: 0.7
            # 生成文本的最大长度
            max-tokens: 1000
            # 核采样参数，0.0-1.0
            top-p: 0.8
        
        # 图像生成模型配置
        image:
          options:
            # 图像模型名称
            model: wanx-v1
            # 图像尺寸
            width: 1024
            height: 1024
            # 生成图像数量
            n: 1
        
        # 语音合成模型配置
        audio:
          speech:
            options:
              # 语音模型名称
              model: sambert-zhichu-v1
              # 语音格式
              response-format: mp3
              # 语音速度
              speed: 1.0

# 日志配置
logging:
  level:
    com.example.springai: DEBUG
    com.alibaba.cloud.ai: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
