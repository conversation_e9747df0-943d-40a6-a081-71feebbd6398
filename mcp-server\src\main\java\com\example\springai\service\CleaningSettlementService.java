package com.example.springai.service;

import com.example.springai.repository.CleaningSettlementRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 驻场保洁和开荒集团结算数据服务
 * 处理驻场保洁和开荒集团结算相关的查询
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class CleaningSettlementService {
    
    @Autowired
    private CleaningSettlementRepository cleaningSettlementRepository;
    
    /**
     * 获取驻场保洁和开荒集团结算统计数据
     */
    public Map<String, Object> getCleaningSettlementStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查询驻场保洁和开荒集团结算统计: {} 到 {}", startDate, endDate);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> settlementData = cleaningSettlementRepository.getCleaningSettlementStatistics(startTime, endTime);
            Object[] overview = cleaningSettlementRepository.getCleaningSettlementOverview(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "驻场保洁和开荒集团结算统计");
            result.put("message", "查询驻场保洁和开荒集团结算数据统计");
            
            // 总体统计
            if (overview != null && overview.length >= 3) {
                int productCount = overview[0] != null ? ((Number)overview[0]).intValue() : 0;
                int orderCount = overview[1] != null ? ((Number)overview[1]).intValue() : 0;
                BigDecimal totalRevenue = overview[2] != null ? (BigDecimal)overview[2] : BigDecimal.ZERO;

                log.info("驻场保洁集团结算统计结果 - 产品数量: {}, 订单数: {}, 总营业额: {}", productCount, orderCount, totalRevenue);

                result.put("totalProductCount", productCount);
                result.put("totalOrderCount", orderCount);
                result.put("totalRevenue", totalRevenue);

                // 添加额外的统计信息
                if (orderCount > 0) {
                    result.put("avgOrderAmount", totalRevenue.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP));
                }
                if (productCount > 0) {
                    result.put("avgProductRevenue", totalRevenue.divide(BigDecimal.valueOf(productCount), 2, RoundingMode.HALF_UP));
                }
            } else {
                log.warn("驻场保洁集团结算统计查询返回空结果");
                result.put("totalProductCount", 0);
                result.put("totalOrderCount", 0);
                result.put("totalRevenue", BigDecimal.ZERO);
            }
            
            // 产品明细
            List<Map<String, Object>> productStats = formatProductStatistics(settlementData);
            result.put("productStatistics", productStats);
            
            // 验证和补充统计数据（从产品明细中累加）
            validateAndSupplementStatistics(result, productStats);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询驻场保洁和开荒集团结算数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取驻场保洁和开荒集团结算排行（按订单数）
     */
    public Map<String, Object> getCleaningSettlementRankingByOrderCount(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询驻场保洁和开荒集团结算排行(按订单数): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> settlementData = cleaningSettlementRepository.getCleaningSettlementRankingByOrderCount(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "驻场保洁和开荒集团结算排行(订单数)");
            result.put("message", "查询驻场保洁和开荒集团结算排行，按订单数排序");
            result.put("rankingType", "订单数");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = settlementData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            List<Map<String, Object>> productRanking = formatProductStatistics(limitedData);
            result.put("productRanking", productRanking);
            
            // 计算累加统计（基于所有数据，不仅仅是限制的数据）
            List<Map<String, Object>> allProductStats = formatProductStatistics(settlementData);
            calculateRankingStatistics(result, allProductStats, productRanking);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询驻场保洁和开荒集团结算排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }
    
    /**
     * 获取驻场保洁和开荒集团结算排行（按营业额）
     */
    public Map<String, Object> getCleaningSettlementRankingByRevenue(LocalDate startDate, LocalDate endDate, int limit) {
        log.info("查询驻场保洁和开荒集团结算排行(按营业额): {} 到 {}, 限制: {}", startDate, endDate, limit);
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        try {
            List<Object[]> settlementData = cleaningSettlementRepository.getCleaningSettlementRankingByRevenue(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());
            result.put("period", calculatePeriodDescription(startDate, endDate));
            result.put("queryType", "驻场保洁和开荒集团结算排行(营业额)");
            result.put("message", "查询驻场保洁和开荒集团结算排行，按营业额排序");
            result.put("rankingType", "营业额");
            result.put("limit", limit);
            
            // 限制返回数量
            List<Object[]> limitedData = settlementData.stream()
                .limit(limit)
                .collect(Collectors.toList());
            
            List<Map<String, Object>> productRanking = formatProductStatistics(limitedData);
            result.put("productRanking", productRanking);
            
            // 计算累加统计（基于所有数据，不仅仅是限制的数据）
            List<Map<String, Object>> allProductStats = formatProductStatistics(settlementData);
            calculateRankingStatistics(result, allProductStats, productRanking);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询驻场保洁和开荒集团结算排行失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "数据查询失败");
            errorResult.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
            return errorResult;
        }
    }

    /**
     * 格式化产品统计数据
     */
    private List<Map<String, Object>> formatProductStatistics(List<Object[]> settlementData) {
        return settlementData.stream()
                .map(data -> {
                    Map<String, Object> productStat = new HashMap<>();
                    productStat.put("productName", data[0] != null ? data[0].toString() : "未知产品");
                    productStat.put("orderCount", data[1] != null ? ((Number)data[1]).intValue() : 0);
                    productStat.put("totalRevenue", data[2] != null ? (BigDecimal)data[2] : BigDecimal.ZERO);
                    productStat.put("productType", data[3] != null ? data[3].toString() : "驻场保洁/开荒");
                    productStat.put("productId", data[4] != null ? ((Number)data[4]).longValue() : 0L);
                    return productStat;
                })
                .collect(Collectors.toList());
    }

    /**
     * 验证和补充统计数据
     */
    private void validateAndSupplementStatistics(Map<String, Object> result, List<Map<String, Object>> productStats) {
        if (productStats == null || productStats.isEmpty()) {
            return;
        }

        BigDecimal calculatedTotalRevenue = BigDecimal.ZERO;
        int calculatedTotalOrders = 0;

        for (Map<String, Object> product : productStats) {
            Object revenue = product.get("totalRevenue");
            Object orderCount = product.get("orderCount");

            if (revenue instanceof BigDecimal) {
                calculatedTotalRevenue = calculatedTotalRevenue.add((BigDecimal) revenue);
            }

            if (orderCount instanceof Number) {
                calculatedTotalOrders += ((Number) orderCount).intValue();
            }
        }

        result.put("calculatedTotalRevenue", calculatedTotalRevenue);
        result.put("calculatedTotalOrders", calculatedTotalOrders);
        result.put("calculatedProductCount", productStats.size());
    }

    /**
     * 计算排行统计数据
     */
    private void calculateRankingStatistics(Map<String, Object> result,
                                          List<Map<String, Object>> allProductStats,
                                          List<Map<String, Object>> limitedProductStats) {

        BigDecimal totalRevenue = BigDecimal.ZERO;
        int totalOrders = 0;

        for (Map<String, Object> product : allProductStats) {
            Object revenue = product.get("totalRevenue");
            Object orderCount = product.get("orderCount");

            if (revenue instanceof BigDecimal) {
                totalRevenue = totalRevenue.add((BigDecimal) revenue);
            }

            if (orderCount instanceof Number) {
                totalOrders += ((Number) orderCount).intValue();
            }
        }

        result.put("totalProductCount", allProductStats.size());
        result.put("totalOrderCount", totalOrders);
        result.put("totalRevenue", totalRevenue);
    }

    /**
     * 计算时间段描述
     */
    private String calculatePeriodDescription(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days == 1) {
            return "1天";
        } else if (days == 7) {
            return "1周";
        } else if (days <= 31) {
            return days + "天";
        } else {
            long months = ChronoUnit.MONTHS.between(startDate, endDate);
            return months > 0 ? months + "个月" : days + "天";
        }
    }
}
