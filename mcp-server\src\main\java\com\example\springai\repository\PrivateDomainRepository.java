package com.example.springai.repository;

import com.example.springai.entity.PrivateDomainPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 私域ABC收款业绩数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface PrivateDomainRepository extends JpaRepository<PrivateDomainPayment, Long> {

    /**
     * 查询私域ABC收款业绩总额
     * 基于新的SQL规则：
     * SELECT SUM(RealTotalAmount) as 私域ABC收款业绩, COUNT(1) as 订单数
     * FROM [dbo].[Order]
     * WHERE PaySettlementTime BETWEEN ? AND ?
     *   AND storeid IN (2, 3, 12, 1054)
     *   AND ProductId NOT IN (95, 166)
     */
    @Query(value = """
        SELECT
            SUM(RealTotalAmount) as totalAmount,
            COUNT(1) as orderCount
        FROM [dbo].[Order]
        WHERE PaySettlementTime BETWEEN :startTime AND :endTime
          AND storeid IN (2, 3, 12, 1054)
         AND orderstate<>99
          AND ProductId NOT IN (95, 166)
        """, nativeQuery = true)
    List<Object[]> getPrivateDomainTotalAmount(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询私域ABC收款业绩详细统计
     * 基于新的SQL规则
     */
    @Query(value = """
        SELECT
            SUM(RealTotalAmount) as totalAmount,
            COUNT(1) as orderCount,
            COUNT(DISTINCT ProductId) as productCount,
            AVG(RealTotalAmount) as avgAmount
        FROM [dbo].[Order]
        WHERE PaySettlementTime BETWEEN :startTime AND :endTime
          AND storeid IN (2, 3, 12, 1054)
           AND orderstate<>99
          AND ProductId NOT IN (95, 166)
        """, nativeQuery = true)
    List<Object[]> getPrivateDomainDetailedStats(@Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询私域ABC收款业绩按天统计
     * 基于新的SQL规则
     */
    @Query(value = """
        SELECT
            CAST(PaySettlementTime AS DATE) as settlementDate,
            SUM(RealTotalAmount) as dailyAmount,
            COUNT(1) as dailyOrderCount
        FROM [dbo].[Order]
        WHERE PaySettlementTime BETWEEN :startTime AND :endTime
          AND storeid IN (2, 3, 12, 1054)
           AND orderstate<>99
          AND ProductId NOT IN (95, 166)
        GROUP BY CAST(PaySettlementTime AS DATE)
        ORDER BY settlementDate DESC
        """, nativeQuery = true)
    List<Object[]> getPrivateDomainDailyStats(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询私域ABC收款业绩产品明细
     * 基于新的SQL规则
     */
    @Query(value = """
        SELECT
            ProductName,
            SUM(RealTotalAmount) as productAmount,
            COUNT(1) as productOrderCount,
            AVG(RealTotalAmount) as avgProductAmount
        FROM [dbo].[Order]
        WHERE PaySettlementTime BETWEEN :startTime AND :endTime
          AND storeid IN (2, 3, 12, 1054)
          AND ProductId NOT IN (95, 166)
            AND orderstate<>99
        GROUP BY ProductName, ProductId
        ORDER BY SUM(RealTotalAmount) DESC
        """, nativeQuery = true)
    List<Object[]> getPrivateDomainProductDetails(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);
}
