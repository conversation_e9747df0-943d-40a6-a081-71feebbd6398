package com.example.springai.repository;

import com.example.springai.entity.PrivateDomainPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 私域ABC收款业绩数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface PrivateDomainRepository extends JpaRepository<PrivateDomainPayment, Long> {

    /**
     * 查询私域ABC收款业绩总额
     * 基于复杂的SQL查询，包含两部分：
     * 1. 有订单号的支付记录（关联订单表）
     * 2. 无订单号的支付记录（直接统计）
     */
    @Query(value = """
        SELECT
            COALESCE(t1.amount, 0) + COALESCE(t2.amounts, 0) as totalAmount
        FROM
            (
            SELECT SUM(o.Amount) AS amount
            FROM
                (SELECT OrderNo, Amount FROM Payment
                 WHERE PaymentStatus = '2'
                   AND PayTime BETWEEN :startTime AND :endTime
                   AND OrderNo IS NOT NULL) t
                LEFT JOIN [Order] o ON t.OrderNo = o.id
            WHERE
                o.storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
                AND o.productid NOT IN (SELECT id FROM Product WHERE ProductCategoryid IN (17, 18, 19, 20, 27, 37, 22, 23))
                AND o.storeid NOT IN (88, 89)
            ) t1
            LEFT JOIN (
            SELECT SUM(amount) amounts
            FROM Payment
            WHERE
                PaymentStatus = '2'
                AND PayTime BETWEEN :startTime AND :endTime
                AND OrderNo IS NULL
                AND memberid NOT IN (
                SELECT memberid
                FROM
                    (
                    SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY memberid ORDER BY CreateTime DESC) AS rn
                    FROM [Order]
                    WHERE memberid IN (
                        SELECT DISTINCT memberid FROM Payment
                        WHERE PaymentStatus = '2'
                          AND PayTime BETWEEN :startTime AND :endTime
                          AND OrderNo IS NULL
                    )
                    ) a
                WHERE a.rn = 1
                  AND storeid NOT IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
                )
            ) t2 ON 1 = 1
        """, nativeQuery = true)
    Object[] getPrivateDomainTotalAmount(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询私域ABC收款业绩详细统计
     * 分别获取有订单号和无订单号的金额
     */
    @Query(value = """
        SELECT
            COALESCE(t1.amount, 0) as orderAmount,
            COALESCE(t1.orderCount, 0) as orderCount,
            COALESCE(t2.amounts, 0) as directAmount,
            COALESCE(t2.paymentCount, 0) as directPaymentCount
        FROM
            (
            SELECT
                SUM(o.Amount) AS amount,
                COUNT(DISTINCT o.id) AS orderCount
            FROM
                (SELECT OrderNo, Amount FROM Payment
                 WHERE PaymentStatus = '2'
                   AND PayTime BETWEEN :startTime AND :endTime
                   AND OrderNo IS NOT NULL) t
                LEFT JOIN [Order] o ON t.OrderNo = o.id
            WHERE
                o.storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
                AND o.productid NOT IN (SELECT id FROM Product WHERE ProductCategoryid IN (17, 18, 19, 20, 27, 37, 22, 23))
                AND o.storeid NOT IN (88, 89)
            ) t1
            LEFT JOIN (
            SELECT
                SUM(amount) amounts,
                COUNT(*) paymentCount
            FROM Payment
            WHERE
                PaymentStatus = '2'
                AND PayTime BETWEEN :startTime AND :endTime
                AND OrderNo IS NULL
                AND memberid NOT IN (
                SELECT memberid
                FROM
                    (
                    SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY memberid ORDER BY CreateTime DESC) AS rn
                    FROM [Order]
                    WHERE memberid IN (
                        SELECT DISTINCT memberid FROM Payment
                        WHERE PaymentStatus = '2'
                          AND PayTime BETWEEN :startTime AND :endTime
                          AND OrderNo IS NULL
                    )
                    ) a
                WHERE a.rn = 1
                  AND storeid NOT IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
                )
            ) t2 ON 1 = 1
        """, nativeQuery = true)
    Object[] getPrivateDomainDetailedStats(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询私域ABC收款业绩按天统计
     * 用于趋势分析
     */
    @Query(value = """
        SELECT
            CAST(PayTime AS DATE) as payDate,
            SUM(CASE WHEN OrderNo IS NOT NULL THEN
                (SELECT Amount FROM [Order] WHERE id = Payment.OrderNo AND
                 storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5)) AND
                 productid NOT IN (SELECT id FROM Product WHERE ProductCategoryid IN (17, 18, 19, 20, 27, 37, 22, 23)) AND
                 storeid NOT IN (88, 89))
                ELSE 0 END) as orderAmount,
            SUM(CASE WHEN OrderNo IS NULL THEN amount ELSE 0 END) as directAmount,
            COUNT(*) as totalPayments
        FROM Payment
        WHERE PaymentStatus = '2'
          AND PayTime BETWEEN :startTime AND :endTime
        GROUP BY CAST(PayTime AS DATE)
        ORDER BY payDate DESC
        """, nativeQuery = true)
    List<Object[]> getPrivateDomainDailyStats(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);
}
