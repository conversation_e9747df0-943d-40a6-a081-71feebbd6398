package com.example.springai.repository;

import com.example.springai.entity.StoreRevenue;
import com.example.springai.entity.Store;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 门店平台收入数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface StoreRevenueRepository extends JpaRepository<StoreRevenue, Long> {

    /**
     * 根据门店ID查找平台收入记录
     */
    List<StoreRevenue> findByStoreId(Long storeId);

    /**
     * 计算共创店的总平台收入（storeType = 2，加盟店）
     */
    @Query("SELECT COALESCE(SUM(sr.platformUsageFeeMoney), 0) " +
           "FROM StoreRevenue sr " +
           "JOIN sr.store s " +
           "WHERE s.storeType = 2")
    BigDecimal calculateTotalCoopStoreRevenue();

    /**
     * 计算指定时间范围内共创店的总平台收入（storeType = 2，加盟店）
     */
    @Query("SELECT COALESCE(SUM(sr.platformUsageFeeMoney), 0) " +
           "FROM StoreRevenue sr " +
           "JOIN sr.store s " +
           "WHERE s.storeType = 2 " +
           "AND sr.createTime BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalCoopStoreRevenueBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取共创店平台收入明细（包含门店和日期信息）（storeType = 2，加盟店）
     */
    @Query("SELECT s.storeName, sr.platformUsageFeeMoney, sr.createTime, sr.storeId " +
           "FROM StoreRevenue sr " +
           "JOIN sr.store s " +
           "WHERE s.storeType = 2 " +
           "ORDER BY sr.createTime DESC, sr.platformUsageFeeMoney DESC")
    List<Object[]> getCoopStoreRevenueDetails();

    /**
     * 获取指定时间范围内共创店平台收入明细（storeType = 2，加盟店）
     */
    @Query("SELECT s.storeName, sr.platformUsageFeeMoney, sr.createTime, sr.storeId " +
           "FROM StoreRevenue sr " +
           "JOIN sr.store s " +
           "WHERE s.storeType = 2 " +
           "AND sr.createTime BETWEEN :startDate AND :endDate " +
           "ORDER BY sr.createTime DESC, sr.platformUsageFeeMoney DESC")
    List<Object[]> getCoopStoreRevenueDetailsBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取共创店平台收入统计（按门店分组）（storeType = 2，加盟店）
     */
    @Query("SELECT s.storeName, SUM(sr.platformUsageFeeMoney), COUNT(sr) " +
           "FROM StoreRevenue sr " +
           "JOIN sr.store s " +
           "WHERE s.storeType = 2 " +
           "GROUP BY s.id, s.storeName " +
           "ORDER BY SUM(sr.platformUsageFeeMoney) DESC")
    List<Object[]> getCoopStoreRevenueStatistics();

    /**
     * 获取指定时间范围内共创店平台收入统计（storeType = 2，加盟店）
     */
    @Query("SELECT s.storeName, SUM(sr.platformUsageFeeMoney), COUNT(sr) " +
           "FROM StoreRevenue sr " +
           "JOIN sr.store s " +
           "WHERE s.storeType = 2 " +
           "AND sr.createTime BETWEEN :startDate AND :endDate " +
           "GROUP BY s.id, s.storeName " +
           "ORDER BY SUM(sr.platformUsageFeeMoney) DESC")
    List<Object[]> getCoopStoreRevenueStatisticsBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 计算指定门店的平台收入
     */
    @Query("SELECT COALESCE(SUM(sr.platformUsageFeeMoney), 0) " +
           "FROM StoreRevenue sr " +
           "WHERE sr.storeId = :storeId")
    BigDecimal calculateStoreRevenue(@Param("storeId") Long storeId);

    /**
     * 计算指定门店在时间范围内的平台收入
     */
    @Query("SELECT COALESCE(SUM(sr.platformUsageFeeMoney), 0) " +
           "FROM StoreRevenue sr " +
           "WHERE sr.storeId = :storeId " +
           "AND sr.createTime BETWEEN :startDate AND :endDate")
    BigDecimal calculateStoreRevenueBetween(@Param("storeId") Long storeId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
