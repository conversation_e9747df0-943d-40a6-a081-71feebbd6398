package com.example.springai.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Entity
@Table(name = "[Order]")
public class Order {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "RealTotalAmount")
    private BigDecimal realTotalAmount;
    
    @Column(name = "ProductName")
    private String productName;
    
    @Column(name = "PaySettlementTime")
    private LocalDateTime paySettlementTime;
    
    @Column(name = "StoreId")
    private Long storeId;
    
    @Column(name = "ProductId")
    private Long productId;
    
    @Column(name = "OrderState")
    private Integer orderState;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "StoreId", insertable = false, updatable = false)
    private Store store;
    
    // 构造函数
    public Order() {}
    
    public Order(Long id, BigDecimal realTotalAmount, String productName, 
                 LocalDateTime paySettlementTime, Long storeId, Long productId, Integer orderState) {
        this.id = id;
        this.realTotalAmount = realTotalAmount;
        this.productName = productName;
        this.paySettlementTime = paySettlementTime;
        this.storeId = storeId;
        this.productId = productId;
        this.orderState = orderState;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public BigDecimal getRealTotalAmount() {
        return realTotalAmount;
    }
    
    public void setRealTotalAmount(BigDecimal realTotalAmount) {
        this.realTotalAmount = realTotalAmount;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public LocalDateTime getPaySettlementTime() {
        return paySettlementTime;
    }
    
    public void setPaySettlementTime(LocalDateTime paySettlementTime) {
        this.paySettlementTime = paySettlementTime;
    }
    
    public Long getStoreId() {
        return storeId;
    }
    
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public Integer getOrderState() {
        return orderState;
    }
    
    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }
    
    public Store getStore() {
        return store;
    }
    
    public void setStore(Store store) {
        this.store = store;
    }
    
    @Override
    public String toString() {
        return "Order{" +
                "id=" + id +
                ", realTotalAmount=" + realTotalAmount +
                ", productName='" + productName + '\'' +
                ", paySettlementTime=" + paySettlementTime +
                ", storeId=" + storeId +
                ", productId=" + productId +
                ", orderState=" + orderState +
                '}';
    }
}
