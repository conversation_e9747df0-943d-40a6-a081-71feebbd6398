package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单数据访问层
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    /**
     * 查询全国三嫂业绩 - 原始SQL查询
     */
    @Query(value = """
        SELECT
            t.RealTotalAmount,
            t.ProductName,
            t.PaySettlementTime,
            t2.StoreName,
            t2.StoreType 
        FROM
            [Order] AS t
            INNER JOIN Store AS t2 ON t2.id = t.StoreId 
        WHERE
            t.StoreID IN ( SELECT id FROM Store WHERE StoreType IN ( 2, 5 ) ) 
            AND t.ProductId IN ( SELECT id FROM Product WHERE ProductCategoryId IN ( 17, 18, 19, 20, 27 ) ) 
            AND ( t.OrderState = 80 OR t.OrderState = 90 )
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        ORDER BY t.PaySettlementTime DESC
        """, nativeQuery = true)
    List<Object[]> findSansaoPerformanceByDateRange(@Param("startTime") LocalDateTime startTime, 
                                                    @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询全国三嫂业绩总额
     */
    @Query(value = """
        SELECT
            COALESCE(SUM(t.RealTotalAmount), 0) as totalAmount,
            COUNT(*) as orderCount
        FROM
            [Order] AS t
            INNER JOIN Store AS t2 ON t2.id = t.StoreId 
        WHERE
            t.StoreId IN ( SELECT id FROM Store WHERE StoreType IN ( 2, 5 ) )
            AND t.ProductId IN ( SELECT id FROM Product WHERE ProductCategoryId IN ( 17, 18, 19, 20, 27 ) )
            AND ( t.OrderState = 80 OR t.OrderState = 90 )
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        """, nativeQuery = true)
    Object[] findSansaoPerformanceSummary(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询全国三嫂业绩门店排行
     */
    @Query(value = """
        SELECT
            t2.StoreName,
            t2.StoreType,
            COALESCE(SUM(t.RealTotalAmount), 0) as storeTotal,
            COUNT(*) as orderCount
        FROM
            [Order] AS t
            INNER JOIN Store AS t2 ON t2.id = t.StoreId 
        WHERE
            t.StoreId IN ( SELECT id FROM Store WHERE StoreType IN ( 2, 5 ) )
            AND t.ProductId IN ( SELECT id FROM Product WHERE ProductCategoryId IN ( 17, 18, 19, 20, 27 ) )
            AND ( t.OrderState = 80 OR t.OrderState = 90 )
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        GROUP BY t2.StoreName, t2.StoreType
        ORDER BY storeTotal DESC
        """, nativeQuery = true)
    List<Object[]> findSansaoPerformanceRanking(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询全国三嫂业绩按产品分类统计
     */
    @Query(value = """
        SELECT
            t.ProductName,
            COALESCE(SUM(t.RealTotalAmount), 0) as productTotal,
            COUNT(*) as orderCount
        FROM
            [Order] AS t
            INNER JOIN Store AS t2 ON t2.id = t.StoreId 
        WHERE
            t.StoreId IN ( SELECT id FROM Store WHERE StoreType IN ( 2, 5 ) )
            AND t.ProductId IN ( SELECT id FROM Product WHERE ProductCategoryId IN ( 17, 18, 19, 20, 27 ) )
            AND ( t.OrderState = 80 OR t.OrderState = 90 )
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        GROUP BY t.ProductName
        ORDER BY productTotal DESC
        """, nativeQuery = true)
    List<Object[]> findSansaoPerformanceByProduct(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询全国三嫂业绩按日期统计（用于趋势分析）
     */
    @Query(value = """
        SELECT
            CAST(t.PaySettlementTime AS DATE) as orderDate,
            COALESCE(SUM(t.RealTotalAmount), 0) as dailyTotal,
            COUNT(*) as orderCount
        FROM
            [Order] AS t
            INNER JOIN Store AS t2 ON t2.id = t.StoreId 
        WHERE
            t.StoreId IN ( SELECT id FROM Store WHERE StoreType IN ( 2, 5 ) )
            AND t.ProductId IN ( SELECT id FROM Product WHERE ProductCategoryId IN ( 17, 18, 19, 20, 27 ) )
            AND ( t.OrderState = 80 OR t.OrderState = 90 )
            AND t.PaySettlementTime BETWEEN :startTime AND :endTime
        GROUP BY CAST(t.PaySettlementTime AS DATE)
        ORDER BY orderDate DESC
        """, nativeQuery = true)
    List<Object[]> findSansaoPerformanceByDate(@Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime);
}
