package com.example.springai.mcp.client;

import com.example.springai.mcp.protocol.McpMessage;
import com.example.springai.mcp.protocol.McpError;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MCP客户端实现
 * 连接到MCP服务器并调用AI工具
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Component
public class McpClient implements WebSocketHandler {
    
    private final ObjectMapper objectMapper;
    private final AtomicLong requestIdGenerator = new AtomicLong(1);
    private final Map<Object, CompletableFuture<McpMessage>> pendingRequests = new ConcurrentHashMap<>();
    
    private WebSocketSession session;
    private boolean initialized = false;
    private CompletableFuture<Void> connectionFuture;

    // 客户端信息
    private static final String CLIENT_NAME = "spring-ai-alibaba-mcp-client";
    private static final String CLIENT_VERSION = "1.0.0";

    public McpClient(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 检查客户端是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }

    /**
     * 检查WebSocket连接是否建立
     */
    public boolean isConnected() {
        return session != null && session.isOpen();
    }
    
    /**
     * 连接到MCP服务器
     */
    public CompletableFuture<Void> connect(String serverUrl) {
        connectionFuture = new CompletableFuture<>();

        try {
            org.springframework.web.socket.client.WebSocketConnectionManager manager =
                new org.springframework.web.socket.client.WebSocketConnectionManager(
                    new org.springframework.web.socket.client.standard.StandardWebSocketClient(),
                    this,
                    serverUrl);
            manager.start();

            // 返回连接Future，在afterConnectionEstablished中完成
            return connectionFuture;

        } catch (Exception e) {
            log.error("连接MCP服务器失败", e);
            connectionFuture.completeExceptionally(e);
            return connectionFuture;
        }
    }
    
    /**
     * 初始化MCP连接
     */
    public CompletableFuture<Map<String, Object>> initialize() {
        Map<String, Object> params = Map.of(
            "protocolVersion", "2024-11-05",
            "clientInfo", Map.of(
                "name", CLIENT_NAME,
                "version", CLIENT_VERSION
            ),
            "capabilities", Map.of()
        );
        
        return sendRequest("initialize", params)
                .thenApply(response -> {
                    if (response.isError()) {
                        throw new RuntimeException("初始化失败: " + response.getError().getMessage());
                    }
                    
                    // 发送initialized通知
                    sendNotification("initialized", null);
                    initialized = true;
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    return result;
                });
    }
    
    /**
     * 获取可用工具列表
     */
    public CompletableFuture<Map<String, Object>> listTools() {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("客户端未初始化"));
        }
        
        return sendRequest("tools/list", null)
                .thenApply(response -> {
                    if (response.isError()) {
                        throw new RuntimeException("获取工具列表失败: " + response.getError().getMessage());
                    }
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    return result;
                });
    }
    
    /**
     * 调用工具
     */
    public CompletableFuture<Map<String, Object>> callTool(String toolName, Map<String, Object> arguments) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("客户端未初始化"));
        }
        
        Map<String, Object> params = Map.of(
            "name", toolName,
            "arguments", arguments
        );
        
        return sendRequest("tools/call", params)
                .thenApply(response -> {
                    if (response.isError()) {
                        throw new RuntimeException("工具调用失败: " + response.getError().getMessage());
                    }
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    return result;
                });
    }
    
    /**
     * 获取资源列表
     */
    public CompletableFuture<Map<String, Object>> listResources() {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("客户端未初始化"));
        }
        
        return sendRequest("resources/list", null)
                .thenApply(response -> {
                    if (response.isError()) {
                        throw new RuntimeException("获取资源列表失败: " + response.getError().getMessage());
                    }
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    return result;
                });
    }
    
    /**
     * 读取资源
     */
    public CompletableFuture<Map<String, Object>> readResource(String uri) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("客户端未初始化"));
        }
        
        Map<String, Object> params = Map.of("uri", uri);
        
        return sendRequest("resources/read", params)
                .thenApply(response -> {
                    if (response.isError()) {
                        throw new RuntimeException("读取资源失败: " + response.getError().getMessage());
                    }
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) response.getResult();
                    return result;
                });
    }
    
    /**
     * 发送请求并等待响应
     */
    private CompletableFuture<McpMessage> sendRequest(String method, Object params) {
        Object requestId = requestIdGenerator.getAndIncrement();
        McpMessage request = McpMessage.createRequest(requestId, method, params);
        
        CompletableFuture<McpMessage> future = new CompletableFuture<>();
        pendingRequests.put(requestId, future);
        
        try {
            String json = objectMapper.writeValueAsString(request);
            session.sendMessage(new TextMessage(json));
            log.debug("发送MCP请求: {}", json);
        } catch (Exception e) {
            pendingRequests.remove(requestId);
            future.completeExceptionally(e);
        }
        
        return future;
    }
    
    /**
     * 发送通知（不等待响应）
     */
    private void sendNotification(String method, Object params) {
        McpMessage notification = McpMessage.createNotification(method, params);
        
        try {
            String json = objectMapper.writeValueAsString(notification);
            session.sendMessage(new TextMessage(json));
            log.debug("发送MCP通知: {}", json);
        } catch (Exception e) {
            log.error("发送通知失败", e);
        }
    }
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        this.session = session;
        log.info("MCP客户端连接建立: {}", session.getId());

        // 完成连接Future
        if (connectionFuture != null && !connectionFuture.isDone()) {
            connectionFuture.complete(null);
        }
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            log.debug("收到MCP响应: {}", payload);
            
            try {
                McpMessage mcpMessage = objectMapper.readValue(payload, McpMessage.class);
                
                if (mcpMessage.isResponse()) {
                    Object id = mcpMessage.getId();
                    log.debug("收到响应消息，ID: {}, 类型: {}", id, id.getClass().getSimpleName());

                    // 尝试不同类型的ID匹配
                    CompletableFuture<McpMessage> future = null;
                    Object matchedKey = null;

                    // 直接匹配
                    future = pendingRequests.remove(id);
                    if (future != null) {
                        matchedKey = id;
                    } else {
                        // 尝试Long类型匹配
                        Long longId = null;
                        if (id instanceof Number) {
                            longId = ((Number) id).longValue();
                            future = pendingRequests.remove(longId);
                            if (future != null) {
                                matchedKey = longId;
                            }
                        }
                    }

                    if (future != null) {
                        future.complete(mcpMessage);

                        // 如果是初始化响应，设置初始化标志
                        boolean isInitResponse = (matchedKey != null &&
                            (matchedKey.equals(1) || matchedKey.equals(1L)));

                        log.debug("检查是否为初始化响应: matchedKey={}, isInitResponse={}, isError={}",
                                matchedKey, isInitResponse, mcpMessage.isError());

                        if (isInitResponse && !mcpMessage.isError()) {
                            initialized = true;
                            log.info("MCP客户端初始化完成");

                            // 发送initialized通知
                            sendNotification("initialized", null);
                        }
                    } else {
                        log.warn("收到响应但没有找到对应的Future，ID: {}, 类型: {}", id, id.getClass().getSimpleName());
                        log.debug("当前待处理请求: {}", pendingRequests.keySet());
                    }
                }
                
            } catch (Exception e) {
                log.error("处理MCP响应失败", e);
            }
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("MCP客户端传输错误", exception);

        // 如果连接过程中出错，完成连接Future
        if (connectionFuture != null && !connectionFuture.isDone()) {
            connectionFuture.completeExceptionally(exception);
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("MCP客户端连接关闭: {}", closeStatus);
        this.session = null;
        this.initialized = false;
        
        // 完成所有待处理的请求
        pendingRequests.values().forEach(future -> 
            future.completeExceptionally(new RuntimeException("连接已关闭")));
        pendingRequests.clear();
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
