package com.example.springai.repository;

import com.example.springai.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 大学收入数据访问接口
 *
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Repository
public interface UniversityIncomeRepository extends JpaRepository<Order, Long> {

    /**
     * 查询大学收入统计数据
     * 基于SQL：
     * SELECT
     *   Amount as 订单金额,
     *   ProductName as 产品名称,
     *   CreateTime as 收款时间
     * FROM [Order]
     * WHERE ProductId = 361
     *   AND RealTotalAmount = Amount
     *   AND OrderState != 99
     *   AND Amount < 10000
     *   AND CreateTime BETWEEN ? AND ?
     */
    @Query(value = """
        SELECT 
            o.ProductName AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.Amount) AS totalRevenue,
            '大学收入' AS productType,
            o.ProductId AS productId
        FROM [Order] o
        WHERE o.ProductId = 361
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState != 99
            AND o.Amount < 10000
            AND o.CreateTime BETWEEN :startTime AND :endTime
        GROUP BY o.ProductId, o.ProductName
        ORDER BY COUNT(o.id) DESC
        """, nativeQuery = true)
    List<Object[]> getUniversityIncomeStatistics(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大学收入排行（按订单数排序）
     */
    @Query(value = """
        SELECT 
            o.ProductName AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.Amount) AS totalRevenue,
            '大学收入' AS productType,
            o.ProductId AS productId
        FROM [Order] o
        WHERE o.ProductId = 361
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState != 99
            AND o.Amount < 10000
            AND o.CreateTime BETWEEN :startTime AND :endTime
        GROUP BY o.ProductId, o.ProductName
        ORDER BY COUNT(o.id) DESC
        """, nativeQuery = true)
    List<Object[]> getUniversityIncomeRankingByOrderCount(@Param("startTime") LocalDateTime startTime, 
                                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大学收入排行（按营业额排序）
     */
    @Query(value = """
        SELECT 
            o.ProductName AS productName,
            COUNT(o.id) AS orderCount,
            SUM(o.Amount) AS totalRevenue,
            '大学收入' AS productType,
            o.ProductId AS productId
        FROM [Order] o
        WHERE o.ProductId = 361
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState != 99
            AND o.Amount < 10000
            AND o.CreateTime BETWEEN :startTime AND :endTime
        GROUP BY o.ProductId, o.ProductName
        ORDER BY SUM(o.Amount) DESC
        """, nativeQuery = true)
    List<Object[]> getUniversityIncomeRankingByRevenue(@Param("startTime") LocalDateTime startTime, 
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大学收入总体统计
     */
    @Query(value = """
        SELECT 
            COUNT(DISTINCT o.ProductId) AS productCount,
            COUNT(o.id) AS totalOrderCount,
            SUM(o.Amount) AS totalRevenue
        FROM [Order] o
        WHERE o.ProductId = 361
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState != 99
            AND o.Amount < 10000
            AND o.CreateTime BETWEEN :startTime AND :endTime
        """, nativeQuery = true)
    Object[] getUniversityIncomeOverview(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大学收入明细数据
     */
    @Query(value = """
        SELECT 
            o.Amount AS orderAmount,
            o.ProductName AS productName,
            o.CreateTime AS orderTime,
            '大学收入' AS productType,
            o.id AS orderId
        FROM [Order] o
        WHERE o.ProductId = 361
            AND o.RealTotalAmount = o.Amount
            AND o.OrderState != 99
            AND o.Amount < 10000
            AND o.CreateTime BETWEEN :startTime AND :endTime
        ORDER BY o.CreateTime DESC
        """, nativeQuery = true)
    List<Object[]> getUniversityIncomeDetails(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);
}
