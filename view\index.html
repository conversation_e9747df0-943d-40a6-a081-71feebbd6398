<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能问数</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .title {
            text-align: center;
            font-size: 32px;
            font-weight: 600;
            color: #4A90E2;
            margin-bottom: 40px;
            letter-spacing: 2px;
        }

        .search-container {
            position: relative;
            margin-bottom: 40px;
        }

        .search-input {
            width: 100%;
            padding: 16px 60px 16px 20px;
            font-size: 16px;
            border: 2px solid #e1e8ed;
            border-radius: 50px;
            outline: none;
            transition: all 0.3s ease;
            background: #fff;
        }

        .search-input:focus {
            border-color: #4A90E2;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #4A90E2;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #357abd;
            transform: translateY(-50%) scale(1.05);
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tag:hover {
            background: #4A90E2;
            color: white;
            border-color: #4A90E2;
            transform: translateY(-2px);
        }

        .history-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .history-item:hover {
            background: #e3f2fd;
            border-color: #4A90E2;
        }

        .history-item::before {
            content: "🕐";
            margin-right: 8px;
        }

        .result-container {
            margin: 20px 0;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border-left: 4px solid #4A90E2;
            display: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .result-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4A90E2, #667eea, #764ba2);
        }

        .result-container.show {
            animation: slideInUp 0.5s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading.show {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .result-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .result-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            margin: 20px 0;
            color: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .loading-content {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 15px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 16px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .loading-dots {
            display: inline-block;
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .connection-status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .connection-status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .tags-container {
                gap: 8px;
            }
            
            .tag {
                font-size: 12px;
                padding: 6px 12px;
            }
        }
    </style>
</head>
<body>
    <!-- 连接状态指示器 -->
    <div class="connection-status disconnected" id="connectionStatus">
        🔴 未连接
    </div>

    <div class="container">
        <h1 class="title">AI智能问数</h1>
        
        <div class="search-container">
            <input type="text" class="search-input" placeholder="请输入您的业务查询，如：本月销售业绩、本月平台收入、本周交付业绩等" id="searchInput">
            <button class="search-btn" onclick="performSearch()">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
            </button>
        </div>

        <!-- 查询等待提示 -->
        <div class="loading" id="loading">
            <div class="loading-content">
                <div class="spinner"></div>
                <div class="loading-text">
                    正在智能分析您的问题<span class="loading-dots"></span>
                </div>
                <div style="font-size: 12px; opacity: 0.8;">
                    AI正在连接数据库查询相关信息
                </div>
            </div>
        </div>

        <!-- 查询结果 -->
        <div class="result-container" id="resultContainer">
            <div class="result-title">📊 查询结果</div>
            <div class="result-content" id="resultContent"></div>
        </div>

        <div class="section">
            <div class="section-title">热门搜索</div>
            <div class="tags-container">
                <div class="tag" onclick="searchTag('本月销售业绩')">本月销售业绩</div>
                <div class="tag" onclick="searchTag('上周交付业绩')">上周交付业绩</div>
                <div class="tag" onclick="searchTag('本月异地交付业绩')">本月异地交付业绩</div>
                <div class="tag" onclick="searchTag('本周驻场业绩')">本周驻场业绩</div>
                <div class="tag" onclick="searchTag('本周政府收入')">本周政府收入</div>
                <div class="tag" onclick="searchTag('全国门店收入')">全国门店收入</div>
                <div class="tag" onclick="searchTag('大学收入')">大学收入</div>
                <div class="tag" onclick="searchTag('本月全国三嫂业绩')">本月全国三嫂业绩</div>
                <div class="tag" onclick="searchTag('本月平台收入')">本月平台收入</div>
                <div class="tag" onclick="searchTag('上月软件销售')">上月软件销售</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">历史搜索</div>
            <div id="historyContainer">
                <div class="history-item" onclick="searchTag('销售业绩')">销售业绩</div>
                <div class="history-item" onclick="searchTag('交付业绩')">交付业绩</div>
                <div class="history-item" onclick="searchTag('异地交付业绩')">异地交付业绩</div>
                <div class="history-item" onclick="searchTag('政府收入')">政府收入</div>
                <div class="history-item" onclick="searchTag('全国门店收入')">全国门店收入</div>
                <div class="history-item" onclick="searchTag('平台收入')">平台收入</div>
                <div class="history-item" onclick="searchTag('软件销售')">软件销售</div>
                <div class="history-item" onclick="searchTag('大学收入')">大学收入</div>
            </div>
        </div>
    </div>

    <script>
        // MCP客户端API基础URL - 自动适配部署环境
        const MCP_API_BASE = (() => {
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;

            console.log('当前访问域名:', hostname);
            console.log('当前协议:', protocol);

            // 如果是文件协议（本地直接打开HTML文件）
            if (protocol === 'file:') {
                console.log('检测到文件协议，使用本地环境配置');
                return 'http://localhost:8081';
            }

            // 如果是本地环境
            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '') {
                return 'http://localhost:8081';
            }

            // 如果是服务器环境，使用指定的线上IP地址
            console.log('检测到服务器环境，使用线上IP配置');
            return 'https://dataseekapi.xiaoyujia.com';
        })();

        // MCP连接状态
        let mcpConnected = false;

        // 页面加载时建立MCP连接
        async function connectToMCP() {
            const statusElement = document.getElementById('connectionStatus');

            try {
                // 更新状态为连接中
                updateConnectionStatus('connecting', '🟡 连接中...');

                console.log('正在连接MCP服务器...');

                // 1. 创建新的URL对象（用于参数编码）
                const url = new URL(`${MCP_API_BASE}/mcp/connect`);

                // 2. 添加查询参数 - 自动适配服务器URL
                const serverUrl = (() => {
                    const hostname = window.location.hostname;
                    const protocol = window.location.protocol;

                    // 如果是文件协议（本地直接打开HTML文件）
                    if (protocol === 'file:') {
                        console.log('检测到文件协议，使用本地WebSocket配置');
                        return 'ws://localhost:8080/mcp';
                    }

                    // 如果是本地环境
                    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '') {
                        return 'ws://localhost:8080/mcp';
                    }

                    // 如果是服务器环境，使用指定的线上IP地址
                    console.log('检测到服务器环境，使用线上WebSocket配置');
                    return 'ws://mcp-server:8080/mcp';
                })();

                console.log('MCP服务器URL:', serverUrl);
                url.searchParams.append('serverUrl', serverUrl);

                // 3. 调用MCP连接API（使用GET方法）
                const response = await fetch(url.toString(), {
                    method: 'POST'  // 改为GET请求
                });

                // 检查响应状态
                if (!response.ok) {
                    throw new Error(`连接失败，状态码: ${response.status}`);
                }

                // 获取响应数据
                const result = await response.json();
                console.log('MCP连接结果:', result);

                if (result.success) {
                    mcpConnected = true;
                    updateConnectionStatus('connected', '🟢 已连接');
                    console.log('MCP服务器连接成功');
                } else {
                    throw new Error(result.message || '连接失败');
                }

            } catch (error) {
                console.error('MCP连接错误:', error);
                mcpConnected = false;
                updateConnectionStatus('disconnected', '🔴 连接失败');

                // 显示错误提示
                setTimeout(() => {
                    alert(`MCP服务器连接失败: ${error.message}\n\n请确保:\n1. MCP服务器正在运行 (端口8080)\n2. MCP客户端正在运行 (端口8081)`);
                }, 1000);
            }
        }

        // 更新连接状态显示
        function updateConnectionStatus(status, text) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = text;
        }

        // 显示动态加载提示
        function showLoadingWithMessages(query) {
            const loading = document.getElementById('loading');
            const loadingText = loading.querySelector('.loading-text');
            const loadingSubText = loading.querySelector('div[style*="font-size: 12px"]');

            loading.style.display = 'block';
            loading.classList.add('show');

            // 根据查询内容显示不同的提示信息
            const messages = [
                { main: '正在智能分析您的问题', sub: 'AI正在理解查询意图' },
                { main: '正在连接数据库', sub: '建立与SQL Server的安全连接' },
                { main: '正在查询数据', sub: '从表中检索相关数据' },
                { main: '正在处理结果', sub: '对查询结果进行智能分析和格式化' }
            ];

            let currentIndex = 0;

            // 立即显示第一条消息
            updateLoadingMessage(loadingText, loadingSubText, messages[currentIndex]);

            // 每1.5秒切换一次消息
            const messageInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % messages.length;
                updateLoadingMessage(loadingText, loadingSubText, messages[currentIndex]);
            }, 1500);

            // 保存interval ID以便后续清除
            loading.messageInterval = messageInterval;
        }

        // 更新加载消息
        function updateLoadingMessage(textElement, subTextElement, message) {
            textElement.innerHTML = `${message.main}<span class="loading-dots"></span>`;
            subTextElement.textContent = message.sub;
        }

        // 隐藏加载提示
        function hideLoading() {
            const loading = document.getElementById('loading');
            loading.classList.remove('show');
            setTimeout(() => {
                loading.style.display = 'none';
            }, 300);

            // 清除消息切换定时器
            if (loading.messageInterval) {
                clearInterval(loading.messageInterval);
                loading.messageInterval = null;
            }
        }

        // 显示结果容器
        function showResult() {
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.style.display = 'block';
            resultContainer.classList.add('show');
        }

        // 搜索功能
        async function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                alert('请输入查询内容');
                return;
            }

            // 检查MCP连接状态
            if (!mcpConnected) {
                alert('MCP服务器未连接，请稍后重试或刷新页面重新连接');
                return;
            }

            await executeQuery(query);
        }

        // 标签搜索
        async function searchTag(query) {
            document.getElementById('searchInput').value = query;
            await executeQuery(query);
        }
        
        // 执行查询
        async function executeQuery(query) {
            const loading = document.getElementById('loading');
            const resultContainer = document.getElementById('resultContainer');
            const resultContent = document.getElementById('resultContent');

            // 显示加载状态
            showLoadingWithMessages(query);
            resultContainer.style.display = 'none';

            console.log('开始执行查询:', query);

            try {
                // 调用MCP业务查询API
                console.log('调用API:', `${MCP_API_BASE}/mcp/tools/business-query?query=${encodeURIComponent(query)}`);

                const response = await fetch(`${MCP_API_BASE}/mcp/tools/business-query?query=${encodeURIComponent(query)}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                console.log('API响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('API响应结果:', result);

                // 显示结果
                if (result.success && result.data && result.data.content) {
                    const content = result.data.content[0];
                    if (content && content.text) {
                        resultContent.innerHTML = formatResult(content.text);
                        showResult();

                        // 添加到历史搜索
                        addToHistory(query);

                        console.log('查询成功，结果已显示');
                    } else {
                        resultContent.innerHTML = '查询结果为空，请尝试其他问题';
                        showResult();
                    }
                } else {
                    const errorMsg = result.message || result.error || '查询失败，请稍后重试';
                    resultContent.innerHTML = `<div style="color: #e74c3c;">❌ ${errorMsg}</div>`;
                    showResult();
                }

            } catch (error) {
                console.error('查询错误:', error);

                let errorMessage = '';
                if (error.message.includes('Failed to fetch')) {
                    errorMessage = '❌ 无法连接到MCP服务，请检查服务是否正在运行';
                } else if (error.message.includes('HTTP 404')) {
                    errorMessage = '❌ API接口不存在，请检查MCP客户端是否正确启动';
                } else if (error.message.includes('HTTP 500')) {
                    errorMessage = '❌ 服务器内部错误，请检查MCP服务器日志';
                } else {
                    errorMessage = `❌ 查询出错: ${error.message}`;
                }

                resultContent.innerHTML = `<div style="color: #e74c3c;">${errorMessage}</div>
                    <div style="margin-top: 10px; font-size: 12px; color: #666;">
                        <strong>故障排除建议：</strong><br>
                        1. 确保MCP服务器正在运行 (端口8080)<br>
                        2. 确保MCP客户端正在运行 (端口8081)<br>
                        3. 检查网络连接<br>
                        4. 刷新页面重新连接
                    </div>`;
                showResult();
            } finally {
                hideLoading();
            }
        }
        
        // 格式化结果显示
        function formatResult(text) {
            // 更丰富的文本格式化
            let formatted = text
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px;">$1</code>');

            // 如果包含数字，添加一些样式
            formatted = formatted.replace(/(\d+\.?\d*)(元|万元|亿元)/g, '<span style="color: #4A90E2; font-weight: bold;">$1$2</span>');

            return `<div style="line-height: 1.8;">${formatted}</div>`;
        }
        
        // 添加到历史搜索
        function addToHistory(query) {
            let history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            
            // 避免重复
            history = history.filter(item => item !== query);
            
            // 添加到开头
            history.unshift(query);
            
            // 限制历史记录数量
            if (history.length > 10) {
                history = history.slice(0, 10);
            }
            
            localStorage.setItem('searchHistory', JSON.stringify(history));
        }
        
        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // 重新连接MCP
        async function reconnectMCP() {
            console.log('尝试重新连接MCP...');
            await connectToMCP();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('AI智能问数页面已加载');
            console.log('MCP API地址:', MCP_API_BASE);

            // 自动建立MCP连接
            await connectToMCP();

            // 加载历史搜索
            loadSearchHistory();
        });

        // 加载搜索历史
        function loadSearchHistory() {
            const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            console.log('加载搜索历史:', history);
        }

        // 添加点击连接状态重新连接的功能
        document.addEventListener('DOMContentLoaded', function() {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.addEventListener('click', function() {
                if (!mcpConnected) {
                    reconnectMCP();
                }
            });
            statusElement.style.cursor = 'pointer';
            statusElement.title = '点击重新连接';
        });
    </script>
</body>
</html>
