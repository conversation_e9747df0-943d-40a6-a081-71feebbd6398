package com.example.springai.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import jakarta.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;

/**
 * 金刚到家数据源配置
 * 
 * <AUTHOR>
 */
@Configuration
@Profile("!idea")
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef = "jingangEntityManagerFactory",
    transactionManagerRef = "jingangTransactionManager",
    basePackages = {"com.example.springai.jingang.repository"}
)
public class JingangDataSourceConfig {

    private static final Logger log = LoggerFactory.getLogger(JingangDataSourceConfig.class);

    /**
     * 金刚到家数据源
     */
    @Bean(name = "jingangDataSource")
    public DataSource jingangDataSource() {
        try {
            // 显式加载MySQL驱动类
            Class.forName("com.mysql.cj.jdbc.Driver");
            log.info("MySQL驱动加载成功");

            HikariDataSource dataSource = new HikariDataSource();
            dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
            //部署时改内网
            dataSource.setJdbcUrl("**********************************************************************************************************************************************************");
            //本地运行外网
//             dataSource.setJdbcUrl("************************************************************************************************************************************************************");
            dataSource.setUsername("jingang");
            dataSource.setPassword("jg$5170701");
            dataSource.setPoolName("JingangHikariPool");
            dataSource.setMaximumPoolSize(10);
            dataSource.setMinimumIdle(5);
            dataSource.setConnectionTimeout(30000);
            dataSource.setIdleTimeout(600000);
            dataSource.setMaxLifetime(1800000);

            return dataSource;
        } catch (ClassNotFoundException e) {
            log.error("MySQL驱动加载失败，将使用备用数据源", e);
            return createFallbackDataSource();
        }
    }

    /**
     * 备用数据源 - 当MySQL驱动不可用时使用H2内存数据库
     */
    private DataSource createFallbackDataSource() {
        log.warn("创建备用数据源 - 使用H2内存数据库");
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("org.h2.Driver");
        dataSource.setJdbcUrl("jdbc:h2:mem:jingang;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE");
        dataSource.setUsername("sa");
        dataSource.setPassword("");
        dataSource.setPoolName("JingangFallbackPool");
        dataSource.setMaximumPoolSize(5);
        dataSource.setMinimumIdle(1);

        return dataSource;
    }

    /**
     * 金刚到家EntityManagerFactory
     */
    @Bean(name = "jingangEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean jingangEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("jingangDataSource") DataSource dataSource) {

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.show_sql", true);
        properties.put("hibernate.format_sql", true);

        return builder
                .dataSource(dataSource)
                .packages("com.example.springai.jingang.entity")
                .persistenceUnit("jingang")
                .properties(properties)
                .build();
    }

    /**
     * 金刚到家事务管理器
     */
    @Bean(name = "jingangTransactionManager")
    public PlatformTransactionManager jingangTransactionManager(
            @Qualifier("jingangEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
