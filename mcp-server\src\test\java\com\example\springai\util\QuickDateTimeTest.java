package com.example.springai.util;

import java.time.LocalDateTime;

/**
 * 快速测试DateTimeUtil解析功能
 */
public class QuickDateTimeTest {
    
    public static void main(String[] args) {
        System.out.println("=== 快速测试DateTimeUtil解析功能 ===");
        
        String[] testInputs = {
            "2025-07-01到2025-07-10交付业绩",
            "2025-07-01到2025-07-10",
            "2025-7-1到2025-7-10",
            "本月"
        };
        
        for (String input : testInputs) {
            System.out.println("\n--- 测试输入: " + input + " ---");
            
            try {
                LocalDateTime[] result = DateTimeUtil.parseDateRange(input);
                if (result != null) {
                    System.out.println("解析成功:");
                    System.out.println("  开始时间: " + result[0]);
                    System.out.println("  结束时间: " + result[1]);
                    
                    long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(
                        result[0].toLocalDate(), result[1].toLocalDate()) + 1;
                    System.out.println("  查询天数: " + daysBetween + "天");
                } else {
                    System.out.println("解析失败: 返回null");
                }
            } catch (Exception e) {
                System.out.println("解析异常: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
}
