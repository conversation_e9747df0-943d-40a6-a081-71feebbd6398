package com.example.springai.config;

import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import jakarta.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;

/**
 * IDEA调试模式数据源配置
 * 使用H2内存数据库避免MySQL驱动问题
 * 
 * <AUTHOR>
 */
@Configuration
@Profile("idea")
@EnableTransactionManagement
public class IdeaDataSourceConfig {
    
    private static final Logger log = LoggerFactory.getLogger(IdeaDataSourceConfig.class);

    /**
     * 主数据源 (H2内存数据库) - IDEA调试模式
     */
    @Primary
    @Bean(name = "primaryDataSource")
    public DataSource primaryDataSource() {
        log.info("创建IDEA调试模式主数据源 - H2内存数据库");
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("org.h2.Driver");
        dataSource.setJdbcUrl("jdbc:h2:mem:primary;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MSSQLServer");
        dataSource.setUsername("sa");
        dataSource.setPassword("");
        dataSource.setPoolName("PrimaryH2Pool");
        dataSource.setMaximumPoolSize(5);
        dataSource.setMinimumIdle(2);
        
        return dataSource;
    }

    /**
     * 金刚到家数据源 (H2内存数据库) - IDEA调试模式
     */
    @Bean(name = "jingangDataSource")
    public DataSource jingangDataSource() {
        log.info("创建IDEA调试模式金刚到家数据源 - H2内存数据库");
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("org.h2.Driver");
        dataSource.setJdbcUrl("jdbc:h2:mem:jingang;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL");
        dataSource.setUsername("sa");
        dataSource.setPassword("");
        dataSource.setPoolName("JingangH2Pool");
        dataSource.setMaximumPoolSize(5);
        dataSource.setMinimumIdle(2);
        
        return dataSource;
    }

    /**
     * 主数据源EntityManagerFactory - IDEA调试模式
     */
    @Primary
    @Bean(name = "primaryEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean primaryEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            DataSource primaryDataSource) {
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.H2Dialect");
        properties.put("hibernate.hbm2ddl.auto", "create-drop");
        properties.put("hibernate.show_sql", true);
        properties.put("hibernate.format_sql", true);
        
        return builder
                .dataSource(primaryDataSource)
                .packages("com.example.springai.entity")
                .persistenceUnit("primary")
                .properties(properties)
                .build();
    }

    /**
     * 金刚到家EntityManagerFactory - IDEA调试模式
     */
    @Bean(name = "jingangEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean jingangEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            DataSource jingangDataSource) {
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.H2Dialect");
        properties.put("hibernate.hbm2ddl.auto", "create-drop");
        properties.put("hibernate.show_sql", true);
        properties.put("hibernate.format_sql", true);
        
        return builder
                .dataSource(jingangDataSource)
                .packages("com.example.springai.jingang.entity")
                .persistenceUnit("jingang")
                .properties(properties)
                .build();
    }

    /**
     * 主数据源事务管理器 - IDEA调试模式
     */
    @Primary
    @Bean(name = "primaryTransactionManager")
    public PlatformTransactionManager primaryTransactionManager(
            EntityManagerFactory primaryEntityManagerFactory) {
        return new JpaTransactionManager(primaryEntityManagerFactory);
    }

    /**
     * 金刚到家事务管理器 - IDEA调试模式
     */
    @Bean(name = "jingangTransactionManager")
    public PlatformTransactionManager jingangTransactionManager(
            EntityManagerFactory jingangEntityManagerFactory) {
        return new JpaTransactionManager(jingangEntityManagerFactory);
    }

    /**
     * 主数据源Repository配置
     */
    @Configuration
    @Profile("idea")
    @EnableJpaRepositories(
        entityManagerFactoryRef = "primaryEntityManagerFactory",
        transactionManagerRef = "primaryTransactionManager",
        basePackages = {"com.example.springai.repository"}
    )
    static class PrimaryRepositoryConfig {
    }

    /**
     * 金刚到家Repository配置
     */
    @Configuration
    @Profile("idea")
    @EnableJpaRepositories(
        entityManagerFactoryRef = "jingangEntityManagerFactory",
        transactionManagerRef = "jingangTransactionManager",
        basePackages = {"com.example.springai.jingang.repository"}
    )
    static class JingangRepositoryConfig {
    }
}
