package com.example.springai.service;

import com.example.springai.service.impl.TongYiServiceImpl;
import com.example.springai.jingang.service.JingangBusinessService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务AI查询服务
 * 智能解析用户的自然语言查询并返回业务数据
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class BusinessAIService {
    
    private final TongYiServiceImpl tongYiService;
    private final BusinessQueryService businessQueryService;
    private final SansaoBusinessService sansaoBusinessService;
    private final JingangBusinessService jingangBusinessService;
    private final ObjectMapper objectMapper;

    @Autowired
    public BusinessAIService(TongYiServiceImpl tongYiService,
                           BusinessQueryService businessQueryService,
                           SansaoBusinessService sansaoBusinessService,
                           JingangBusinessService jingangBusinessService,
                           ObjectMapper objectMapper) {
        this.tongYiService = tongYiService;
        this.businessQueryService = businessQueryService;
        this.sansaoBusinessService = sansaoBusinessService;
        this.jingangBusinessService = jingangBusinessService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 处理业务查询请求
     */
    public String handleBusinessQuery(String userQuery) {
        log.info("处理业务查询: {}", userQuery);

        try {
            // 优先检查是否为全国三嫂业绩查询
            if (sansaoBusinessService.isSansaoQuery(userQuery)) {
                log.info("识别为全国三嫂业绩查询");
                return sansaoBusinessService.handleSansaoQuery(userQuery);
            }

            // 检查是否为金刚到家软件收入查询
            if (jingangBusinessService.isJingangQuery(userQuery)) {
                log.info("识别为金刚到家软件收入查询");
                return jingangBusinessService.handleJingangQuery(userQuery);
            }

            // 1. 使用AI解析用户查询意图
            QueryIntent intent = parseQueryIntent(userQuery);
            log.info("解析的查询意图: {}", intent);

            // 2. 根据意图执行相应的业务查询
            Map<String, Object> businessData = executeBusinessQuery(intent);

            // 3. 直接生成友好的回复（不依赖AI服务）
            String response = generateFriendlyResponse(userQuery, intent, businessData);

            return response;

        } catch (Exception e) {
            log.error("处理业务查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }
    
    /**
     * 解析用户查询意图
     */
    private QueryIntent parseQueryIntent(String userQuery) {
        String prompt = buildIntentParsePrompt(userQuery);
        String aiResponse = tongYiService.completion(prompt);
        
        try {
            return parseIntentFromAIResponse(aiResponse);
        } catch (Exception e) {
            log.warn("AI意图解析失败，使用默认解析: {}", e.getMessage());
            return parseIntentFallback(userQuery);
        }
    }
    
    /**
     * 构建意图解析提示词
     */
    private String buildIntentParsePrompt(String userQuery) {
        return String.format("""
            请分析以下用户查询，并以JSON格式返回查询意图：
            
            用户查询："%s"
            
            请返回JSON格式，包含以下字段：
            {
                "queryType": "总业绩|门店业绩|门店排名|销售概览|平台收入总额|平台收入明细|平台收入排名|门店平台收入|平台收入概览|全国三嫂业绩|三嫂业绩排行|三嫂产品分析|三嫂趋势分析",
                "timeRange": {
                    "startDate": "YYYY-MM-DD",
                    "endDate": "YYYY-MM-DD"
                },
                "storeIdentifier": "门店名称或编码（如果涉及特定门店）",
                "limit": 数字（如果涉及排名限制）
            }
            
            时间解析规则：
            - "上周"：上周一到周日
            - "本周"：本周一到今天
            - "上个月"：上个月1号到最后一天
            - "本月"：本月1号到今天
            - "昨天"：昨天
            - "今天"：今天
            - "最近7天"：7天前到今天
            - "最近30天"：30天前到今天
            
            今天是：%s
            
            只返回JSON，不要其他解释。
            """, userQuery, LocalDate.now().toString());
    }
    
    /**
     * 从AI响应中解析意图
     */
    private QueryIntent parseIntentFromAIResponse(String aiResponse) throws JsonProcessingException {
        // 提取JSON部分
        String jsonStr = extractJsonFromResponse(aiResponse);
        JsonNode jsonNode = objectMapper.readTree(jsonStr);
        
        QueryIntent intent = new QueryIntent();
        intent.setQueryType(jsonNode.get("queryType").asText());
        
        // 解析时间范围
        JsonNode timeRange = jsonNode.get("timeRange");
        if (timeRange != null) {
            intent.setStartDate(LocalDate.parse(timeRange.get("startDate").asText()));
            intent.setEndDate(LocalDate.parse(timeRange.get("endDate").asText()));
        }
        
        // 解析门店标识符
        JsonNode storeNode = jsonNode.get("storeIdentifier");
        if (storeNode != null && !storeNode.isNull()) {
            intent.setStoreIdentifier(storeNode.asText());
        }
        
        // 解析限制数量
        JsonNode limitNode = jsonNode.get("limit");
        if (limitNode != null && !limitNode.isNull()) {
            intent.setLimit(limitNode.asInt());
        }
        
        return intent;
    }
    
    /**
     * 提取响应中的JSON部分
     */
    private String extractJsonFromResponse(String response) {
        int start = response.indexOf('{');
        int end = response.lastIndexOf('}');
        if (start >= 0 && end > start) {
            return response.substring(start, end + 1);
        }
        return response;
    }
    
    /**
     * 备用意图解析（基于关键词）
     */
    private QueryIntent parseIntentFallback(String userQuery) {
        QueryIntent intent = new QueryIntent();
        String query = userQuery.toLowerCase();
        
        // 解析查询类型
        if (query.contains("三嫂") || query.contains("三姐妹") ||
            (query.contains("全国") && (query.contains("业绩") || query.contains("销售")))) {
            if (query.contains("排行") || query.contains("排名")) {
                intent.setQueryType("三嫂业绩排行");
                intent.setLimit(10); // 默认前10名
            } else if (query.contains("产品") || query.contains("商品")) {
                intent.setQueryType("三嫂产品分析");
            } else if (query.contains("趋势") || query.contains("走势")) {
                intent.setQueryType("三嫂趋势分析");
            } else {
                intent.setQueryType("全国三嫂业绩");
            }
        } else if (query.contains("平台收入") || query.contains("平台费用") || query.contains("平台佣金")) {
            if (query.contains("明细") || query.contains("详细") || query.contains("列表")) {
                intent.setQueryType("平台收入明细");
            } else if (query.contains("排名") || query.contains("排行")) {
                intent.setQueryType("平台收入排名");
                intent.setLimit(10); // 默认前10名
            } else if (query.contains("总") || query.contains("合计") || query.contains("多少")) {
                intent.setQueryType("平台收入总额");
            } else {
                intent.setQueryType("平台收入概览");
            }
        } else if (query.contains("总业绩") || query.contains("总销售") || query.contains("总收入")) {
            intent.setQueryType("总业绩");
        } else if (query.contains("门店") && (query.contains("排名") || query.contains("排行"))) {
            intent.setQueryType("门店排名");
            intent.setLimit(10); // 默认前10名
        } else if (query.contains("门店")) {
            // 检查是否询问门店的平台收入
            if (query.contains("平台收入") || query.contains("平台费用")) {
                intent.setQueryType("门店平台收入");
            } else {
                intent.setQueryType("门店业绩");
            }
        } else {
            intent.setQueryType("销售概览");
        }
        
        // 解析时间范围
        LocalDate today = LocalDate.now();
        if (query.contains("上周")) {
            LocalDate lastMonday = today.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
            intent.setStartDate(lastMonday);
            intent.setEndDate(lastMonday.plusDays(6));
        } else if (query.contains("本周")) {
            LocalDate thisMonday = today.with(java.time.DayOfWeek.MONDAY);
            intent.setStartDate(thisMonday);
            intent.setEndDate(today);
        } else if (query.contains("上个月")) {
            LocalDate lastMonth = today.minusMonths(1);
            intent.setStartDate(lastMonth.withDayOfMonth(1));
            intent.setEndDate(lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()));
        } else if (query.contains("本月")) {
            intent.setStartDate(today.withDayOfMonth(1));
            intent.setEndDate(today);
        } else {
            // 默认最近7天
            intent.setStartDate(today.minusDays(6));
            intent.setEndDate(today);
        }
        
        return intent;
    }
    
    /**
     * 执行业务查询
     */
    private Map<String, Object> executeBusinessQuery(QueryIntent intent) {
        switch (intent.getQueryType()) {
            case "总业绩":
                return businessQueryService.getTotalSales(intent.getStartDate(), intent.getEndDate());
            case "门店业绩":
                if (intent.getStoreIdentifier() != null) {
                    return businessQueryService.getStoreSales(intent.getStoreIdentifier(),
                            intent.getStartDate(), intent.getEndDate());
                } else {
                    return businessQueryService.getSalesOverview(intent.getStartDate(), intent.getEndDate());
                }
            case "门店排名":
                return businessQueryService.getStoreRanking(intent.getStartDate(), intent.getEndDate(),
                        intent.getLimit() != null ? intent.getLimit() : 10);
            case "平台收入总额":
                return businessQueryService.getTotalPlatformRevenue(intent.getStartDate(), intent.getEndDate());
            case "平台收入明细":
                return businessQueryService.getPlatformRevenueDetails(intent.getStartDate(), intent.getEndDate());
            case "平台收入排名":
                return businessQueryService.getPlatformRevenueRanking(intent.getStartDate(), intent.getEndDate(),
                        intent.getLimit() != null ? intent.getLimit() : 10);
            case "门店平台收入":
                if (intent.getStoreIdentifier() != null) {
                    return businessQueryService.getStorePlatformRevenue(intent.getStoreIdentifier(),
                            intent.getStartDate(), intent.getEndDate());
                } else {
                    return businessQueryService.getPlatformRevenueOverview(intent.getStartDate(), intent.getEndDate());
                }
            case "平台收入概览":
                return businessQueryService.getPlatformRevenueOverview(intent.getStartDate(), intent.getEndDate());
            // 全国三嫂业绩查询会在handleBusinessQuery中被提前处理，这里作为备用
            case "全国三嫂业绩":
            case "三嫂业绩排行":
            case "三嫂产品分析":
            case "三嫂趋势分析":
                // 这些查询类型会被SansaoBusinessService处理，这里返回提示信息
                Map<String, Object> sansaoResult = new HashMap<>();
                sansaoResult.put("message", "全国三嫂业绩查询已转交专门服务处理");
                sansaoResult.put("queryType", intent.getQueryType());
                return sansaoResult;
            case "销售概览":
            default:
                return businessQueryService.getSalesOverview(intent.getStartDate(), intent.getEndDate());
        }
    }
    
    /**
     * 生成友好的回复
     */
    private String generateFriendlyResponse(String userQuery, QueryIntent intent, Map<String, Object> businessData) {
        // 检查是否有错误信息
        if (businessData.containsKey("error")) {
            return "抱歉，" + businessData.get("error");
        }

        // 生成简化的友好回复，避免复杂的格式化
        StringBuilder response = new StringBuilder();
        String queryType = intent.getQueryType();

        if ("平台收入概览".equals(queryType) || "平台收入总额".equals(queryType) || "门店平台收入".equals(queryType)) {
            String period = (String) businessData.get("period");
            Object totalRevenue = businessData.get("totalPlatformRevenue");

            response.append("**平台收入查询结果**\n\n");
            if (period != null) {
                response.append("查询时间：").append(period).append("\n");
            }
            if (totalRevenue != null) {
                response.append("平台总收入：").append(String.format("%,.2f元", ((BigDecimal)totalRevenue).doubleValue())).append("\n");
            }

            // 添加门店统计信息
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> storeStats = (List<Map<String, Object>>) businessData.get("storeStatistics");
            if (storeStats != null && !storeStats.isEmpty()) {
                response.append("\n**门店表现排名：**\n");
                for (int i = 0; i < Math.min(5, storeStats.size()); i++) {
                    Map<String, Object> store = storeStats.get(i);
                    String storeName = (String) store.get("storeName");
                    Object revenue = store.get("totalRevenue");
                    Object recordCount = store.get("recordCount");

                    response.append(String.format("%d. %s：%,.2f元",
                        i + 1, storeName, ((BigDecimal)revenue).doubleValue()));
                    if (recordCount != null) {
                        response.append(String.format("（%d单）", ((Number)recordCount).intValue()));
                    }
                    response.append("\n");
                }
            }
        } else {
            // 对于其他查询类型，使用通用格式
            response.append("**查询结果**\n\n");
            String period = (String) businessData.get("period");
            if (period != null) {
                response.append("查询时间：").append(period).append("\n");
            }

            // 显示主要数据
            Object totalSales = businessData.get("totalSales");
            if (totalSales != null) {
                response.append("总业绩：").append(String.format("%,.2f元", ((BigDecimal)totalSales).doubleValue())).append("\n");
            }
        }

        return response.toString();
    }
    



    /**
     * 为AI格式化业务数据（保留此方法以备将来使用）
     */
    private String formatBusinessDataForAI(Map<String, Object> businessData) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(businessData);
        } catch (JsonProcessingException e) {
            return businessData.toString();
        }
    }
    
    /**
     * 查询意图数据类
     */
    public static class QueryIntent {
        private String queryType;
        private LocalDate startDate;
        private LocalDate endDate;
        private String storeIdentifier;
        private Integer limit;
        
        // Getters and Setters
        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }
        
        public LocalDate getStartDate() { return startDate; }
        public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
        
        public LocalDate getEndDate() { return endDate; }
        public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
        
        public String getStoreIdentifier() { return storeIdentifier; }
        public void setStoreIdentifier(String storeIdentifier) { this.storeIdentifier = storeIdentifier; }
        
        public Integer getLimit() { return limit; }
        public void setLimit(Integer limit) { this.limit = limit; }
        
        @Override
        public String toString() {
            return String.format("QueryIntent{queryType='%s', startDate=%s, endDate=%s, storeIdentifier='%s', limit=%d}",
                    queryType, startDate, endDate, storeIdentifier, limit);
        }
    }
}
