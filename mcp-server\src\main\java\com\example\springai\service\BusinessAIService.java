package com.example.springai.service;

import com.example.springai.service.impl.TongYiServiceImpl;
import com.example.springai.jingang.service.JingangBusinessService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务AI查询服务
 * 智能解析用户的自然语言查询并返回业务数据
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@Service
public class BusinessAIService {
    
    private final TongYiServiceImpl tongYiService;
    private final BusinessQueryService businessQueryService;
    private final SansaoBusinessService sansaoBusinessService;
    private final JingangBusinessService jingangBusinessService;
    private final NewProductSalesService newProductSalesService;
    private final NationalDeliveryService nationalDeliveryService;
    private final CleaningSettlementService cleaningSettlementService;
    private final UniversityIncomeService universityIncomeService;
    private final PrivateDomainService privateDomainService;
    private final ObjectMapper objectMapper;

    @Autowired
    public BusinessAIService(TongYiServiceImpl tongYiService,
                           BusinessQueryService businessQueryService,
                           SansaoBusinessService sansaoBusinessService,
                           JingangBusinessService jingangBusinessService,
                           NewProductSalesService newProductSalesService,
                           NationalDeliveryService nationalDeliveryService,
                           CleaningSettlementService cleaningSettlementService,
                           UniversityIncomeService universityIncomeService,
                           PrivateDomainService privateDomainService,
                           ObjectMapper objectMapper) {
        this.tongYiService = tongYiService;
        this.businessQueryService = businessQueryService;
        this.sansaoBusinessService = sansaoBusinessService;
        this.jingangBusinessService = jingangBusinessService;
        this.newProductSalesService = newProductSalesService;
        this.nationalDeliveryService = nationalDeliveryService;
        this.cleaningSettlementService = cleaningSettlementService;
        this.universityIncomeService = universityIncomeService;
        this.privateDomainService = privateDomainService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 处理业务查询请求
     */
    public String handleBusinessQuery(String userQuery) {
        log.info("处理业务查询: {}", userQuery);

        try {
            // 首先检查是否是不合理的查询
            String unreasonableCheck = checkUnreasonableQuery(userQuery);
            if (unreasonableCheck != null) {
                return unreasonableCheck;
            }
            // 检查是否为新品成交查询
            if (isNewProductSalesQuery(userQuery)) {
                log.info("识别为新品成交查询");
                return handleNewProductSalesQuery(userQuery);
            }

            // 优先检查是否为全国交付业绩查询（在三嫂查询之前）
            if (isNationalDeliveryQuery(userQuery)) {
                log.info("识别为全国交付业绩查询");
                return handleNationalDeliveryQuery(userQuery);
            }

            // 检查是否为驻场保洁和开荒集团结算查询
            if (isCleaningSettlementQuery(userQuery)) {
                log.info("识别为驻场保洁和开荒集团结算查询");
                return handleCleaningSettlementQuery(userQuery);
            }

            // 检查是否为大学收入查询
            if (isUniversityIncomeQuery(userQuery)) {
                log.info("识别为大学收入查询");
                return handleUniversityIncomeQuery(userQuery);
            }

            // 检查是否为私域ABC收款业绩查询
            if (isPrivateDomainQuery(userQuery)) {
                log.info("识别为私域ABC收款业绩查询");
                return handlePrivateDomainQuery(userQuery);
            }

            // 检查是否为全国三嫂业绩查询
            if (sansaoBusinessService.isSansaoQuery(userQuery)) {
                log.info("识别为全国三嫂业绩查询");
                return sansaoBusinessService.handleSansaoQuery(userQuery);
            }

            // 检查是否为金刚到家软件收入查询
            if (jingangBusinessService.isJingangQuery(userQuery)) {
                log.info("识别为金刚到家软件收入查询");
                return jingangBusinessService.handleJingangQuery(userQuery);
            }

            // 1. 使用AI解析用户查询意图
            QueryIntent intent = parseQueryIntent(userQuery);
            log.info("解析的查询意图: {}", intent);

            // 2. 根据意图执行相应的业务查询
            Map<String, Object> businessData = executeBusinessQuery(intent);

            // 3. 直接生成友好的回复（不依赖AI服务）
            String response = generateFriendlyResponse(userQuery, intent, businessData);

            return response;

        } catch (Exception e) {
            log.error("处理业务查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }
    
    /**
     * 解析用户查询意图
     */
    private QueryIntent parseQueryIntent(String userQuery) {
        String prompt = buildIntentParsePrompt(userQuery);
        String aiResponse = tongYiService.completion(prompt);
        
        try {
            return parseIntentFromAIResponse(aiResponse);
        } catch (Exception e) {
            log.warn("AI意图解析失败，使用默认解析: {}", e.getMessage());
            return parseIntentFallback(userQuery);
        }
    }
    
    /**
     * 构建意图解析提示词
     */
    private String buildIntentParsePrompt(String userQuery) {
        return String.format("""
            请分析以下用户查询，并以JSON格式返回查询意图：
            
            用户查询："%s"
            
            请返回JSON格式，包含以下字段：
            {
                "queryType": "总业绩|门店业绩|门店排名|销售概览|平台收入总额|平台收入明细|平台收入排名|门店平台收入|平台收入概览|全国三嫂业绩|三嫂业绩排行|三嫂产品分析|三嫂趋势分析",
                "timeRange": {
                    "startDate": "YYYY-MM-DD",
                    "endDate": "YYYY-MM-DD"
                },
                "storeIdentifier": "门店名称或编码（如果涉及特定门店）",
                "limit": 数字（如果涉及排名限制）
            }
            
            时间解析规则：
            - "上周"：上周一到周日
            - "本周"：本周一到今天
            - "上个月"：上个月1号到最后一天
            - "本月"：本月1号到今天
            - "昨天"：昨天
            - "今天"：今天
            - "最近7天"：7天前到今天
            - "最近30天"：30天前到今天
            
            今天是：%s
            
            只返回JSON，不要其他解释。
            """, userQuery, LocalDate.now().toString());
    }
    
    /**
     * 从AI响应中解析意图
     */
    private QueryIntent parseIntentFromAIResponse(String aiResponse) throws JsonProcessingException {
        // 提取JSON部分
        String jsonStr = extractJsonFromResponse(aiResponse);
        JsonNode jsonNode = objectMapper.readTree(jsonStr);
        
        QueryIntent intent = new QueryIntent();
        intent.setQueryType(jsonNode.get("queryType").asText());
        
        // 解析时间范围
        JsonNode timeRange = jsonNode.get("timeRange");
        if (timeRange != null) {
            intent.setStartDate(LocalDate.parse(timeRange.get("startDate").asText()));
            intent.setEndDate(LocalDate.parse(timeRange.get("endDate").asText()));
        }
        
        // 解析门店标识符
        JsonNode storeNode = jsonNode.get("storeIdentifier");
        if (storeNode != null && !storeNode.isNull()) {
            intent.setStoreIdentifier(storeNode.asText());
        }
        
        // 解析限制数量
        JsonNode limitNode = jsonNode.get("limit");
        if (limitNode != null && !limitNode.isNull()) {
            intent.setLimit(limitNode.asInt());
        }
        
        return intent;
    }
    
    /**
     * 提取响应中的JSON部分
     */
    private String extractJsonFromResponse(String response) {
        int start = response.indexOf('{');
        int end = response.lastIndexOf('}');
        if (start >= 0 && end > start) {
            return response.substring(start, end + 1);
        }
        return response;
    }
    
    /**
     * 备用意图解析（基于关键词）
     */
    private QueryIntent parseIntentFallback(String userQuery) {
        QueryIntent intent = new QueryIntent();
        String query = userQuery.toLowerCase();

        // 检查是否包含未来时间关键词
        if (query.contains("下周") || query.contains("下个星期") ||
            query.contains("下月") || query.contains("下个月") ||
            query.contains("明天") || query.contains("明日") ||
            query.contains("后天") || query.contains("未来")) {
            // 对于未来时间查询，设置一个特殊的查询类型
            intent.setQueryType("未来时间查询");
            return intent;
        }
        
        // 解析查询类型
        if (query.contains("三嫂") || query.contains("三姐妹") ||
            (query.contains("全国") && (query.contains("业绩") || query.contains("销售")))) {
            if (query.contains("排行") || query.contains("排名")) {
                intent.setQueryType("三嫂业绩排行");
                intent.setLimit(10); // 默认前10名
            } else if (query.contains("产品") || query.contains("商品")) {
                intent.setQueryType("三嫂产品分析");
            } else if (query.contains("趋势") || query.contains("走势")) {
                intent.setQueryType("三嫂趋势分析");
            } else {
                intent.setQueryType("全国三嫂业绩");
            }
        } else if (query.contains("平台收入") || query.contains("平台费用") || query.contains("平台佣金")) {
            if (query.contains("明细") || query.contains("详细") || query.contains("列表")) {
                intent.setQueryType("平台收入明细");
            } else if (query.contains("排名") || query.contains("排行")) {
                intent.setQueryType("平台收入排名");
                intent.setLimit(10); // 默认前10名
            } else if (query.contains("总") || query.contains("合计") || query.contains("多少")) {
                intent.setQueryType("平台收入总额");
            } else {
                intent.setQueryType("平台收入概览");
            }
        } else if (query.contains("总业绩") || query.contains("总销售") || query.contains("总收入")) {
            intent.setQueryType("总业绩");
        } else if (query.contains("门店") && (query.contains("排名") || query.contains("排行"))) {
            intent.setQueryType("门店排名");
            intent.setLimit(10); // 默认前10名
        } else if (query.contains("门店")) {
            // 检查是否询问门店的平台收入
            if (query.contains("平台收入") || query.contains("平台费用")) {
                intent.setQueryType("门店平台收入");
            } else {
                intent.setQueryType("门店业绩");
            }
        } else {
            intent.setQueryType("销售概览");
        }
        
        // 解析时间范围
        LocalDate today = LocalDate.now();
        if (query.contains("上周")) {
            LocalDate lastMonday = today.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
            intent.setStartDate(lastMonday);
            intent.setEndDate(lastMonday.plusDays(6));
        } else if (query.contains("本周")) {
            LocalDate thisMonday = today.with(java.time.DayOfWeek.MONDAY);
            intent.setStartDate(thisMonday);
            intent.setEndDate(today);
        } else if (query.contains("上个月")) {
            LocalDate lastMonth = today.minusMonths(1);
            intent.setStartDate(lastMonth.withDayOfMonth(1));
            intent.setEndDate(lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()));
        } else if (query.contains("本月")) {
            intent.setStartDate(today.withDayOfMonth(1));
            intent.setEndDate(today);
        } else {
            // 默认最近7天
            intent.setStartDate(today.minusDays(6));
            intent.setEndDate(today);
        }
        
        return intent;
    }
    
    /**
     * 执行业务查询
     */
    private Map<String, Object> executeBusinessQuery(QueryIntent intent) {
        switch (intent.getQueryType()) {
            case "未来时间查询":
                Map<String, Object> futureError = new HashMap<>();
                futureError.put("error", "未来时间查询");
                futureError.put("message", "哈哈哈哈，您可太幽默了，我没有提前预知的超能力哦~ 😄");
                return futureError;
            case "总业绩":
                return businessQueryService.getTotalSales(intent.getStartDate(), intent.getEndDate());
            case "门店业绩":
                if (intent.getStoreIdentifier() != null) {
                    return businessQueryService.getStoreSales(intent.getStoreIdentifier(),
                            intent.getStartDate(), intent.getEndDate());
                } else {
                    return businessQueryService.getSalesOverview(intent.getStartDate(), intent.getEndDate());
                }
            case "门店排名":
                return businessQueryService.getStoreRanking(intent.getStartDate(), intent.getEndDate(),
                        intent.getLimit() != null ? intent.getLimit() : 10);
            case "平台收入总额":
                return businessQueryService.getTotalPlatformRevenue(intent.getStartDate(), intent.getEndDate());
            case "平台收入明细":
                return businessQueryService.getPlatformRevenueDetails(intent.getStartDate(), intent.getEndDate());
            case "平台收入排名":
                return businessQueryService.getPlatformRevenueRanking(intent.getStartDate(), intent.getEndDate(),
                        intent.getLimit() != null ? intent.getLimit() : 10);
            case "门店平台收入":
                if (intent.getStoreIdentifier() != null) {
                    return businessQueryService.getStorePlatformRevenue(intent.getStoreIdentifier(),
                            intent.getStartDate(), intent.getEndDate());
                } else {
                    return businessQueryService.getPlatformRevenueOverview(intent.getStartDate(), intent.getEndDate());
                }
            case "平台收入概览":
                return businessQueryService.getPlatformRevenueOverview(intent.getStartDate(), intent.getEndDate());
            // 全国三嫂业绩查询会在handleBusinessQuery中被提前处理，这里作为备用
            case "全国三嫂业绩":
            case "三嫂业绩排行":
            case "三嫂产品分析":
            case "三嫂趋势分析":
                // 这些查询类型会被SansaoBusinessService处理，这里返回提示信息
                Map<String, Object> sansaoResult = new HashMap<>();
                sansaoResult.put("message", "全国三嫂业绩查询已转交专门服务处理");
                sansaoResult.put("queryType", intent.getQueryType());
                return sansaoResult;
            case "销售概览":
            default:
                return businessQueryService.getSalesOverview(intent.getStartDate(), intent.getEndDate());
        }
    }
    
    /**
     * 生成友好的回复
     */
    private String generateFriendlyResponse(String userQuery, QueryIntent intent, Map<String, Object> businessData) {
        // 检查是否有错误信息
        if (businessData.containsKey("error")) {
            return "抱歉，" + businessData.get("error");
        }

        // 生成简化的友好回复，避免复杂的格式化
        StringBuilder response = new StringBuilder();
        String queryType = intent.getQueryType();

        if ("平台收入概览".equals(queryType) || "平台收入总额".equals(queryType) || "门店平台收入".equals(queryType)) {
            String period = (String) businessData.get("period");
            Object totalRevenue = businessData.get("totalPlatformRevenue");

            response.append("**平台收入查询结果**\n\n");
            if (period != null) {
                response.append("查询时间：").append(period).append("\n");
            }
            if (totalRevenue != null) {
                response.append("平台总收入：").append(String.format("%,.2f元", ((BigDecimal)totalRevenue).doubleValue())).append("\n");
            }

            // 添加门店统计信息
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> storeStats = (List<Map<String, Object>>) businessData.get("storeStatistics");
            if (storeStats != null && !storeStats.isEmpty()) {
                response.append("\n**门店表现排名：**\n");
                for (int i = 0; i < Math.min(5, storeStats.size()); i++) {
                    Map<String, Object> store = storeStats.get(i);
                    String storeName = (String) store.get("storeName");
                    Object revenue = store.get("totalRevenue");
                    Object recordCount = store.get("recordCount");

                    response.append(String.format("%d. %s：%,.2f元",
                        i + 1, storeName, ((BigDecimal)revenue).doubleValue()));
                    if (recordCount != null) {
                        response.append(String.format("（%d单）", ((Number)recordCount).intValue()));
                    }
                    response.append("\n");
                }
            }
        } else {
            // 对于其他查询类型，使用通用格式
            response.append("**查询结果**\n\n");
            String period = (String) businessData.get("period");
            if (period != null) {
                response.append("查询时间：").append(period).append("\n");
            }

            // 显示主要数据
            Object totalSales = businessData.get("totalSales");
            if (totalSales != null) {
                response.append("总业绩：").append(String.format("%,.2f元", ((BigDecimal)totalSales).doubleValue())).append("\n");
            }
        }

        return response.toString();
    }
    



    /**
     * 为AI格式化业务数据（保留此方法以备将来使用）
     */
    private String formatBusinessDataForAI(Map<String, Object> businessData) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(businessData);
        } catch (JsonProcessingException e) {
            return businessData.toString();
        }
    }

    /**
     * 判断是否为新品成交查询
     */
    private boolean isNewProductSalesQuery(String query) {
        String lowerQuery = query.toLowerCase();
        return lowerQuery.contains("新品") &&
               (lowerQuery.contains("成交") || lowerQuery.contains("销售") ||
                lowerQuery.contains("业绩") || lowerQuery.contains("排行") ||
                lowerQuery.contains("统计") || lowerQuery.contains("情况"));
    }

    /**
     * 处理新品成交查询
     */
    private String handleNewProductSalesQuery(String userQuery) {
        try {
            // 检查是否是不合理的查询
            String unreasonableCheck = checkUnreasonableQuery(userQuery);
            if (unreasonableCheck != null) {
                return unreasonableCheck;
            }

            // 解析时间范围
            LocalDate[] dateRange = parseTimeRange(userQuery);
            LocalDate startDate = dateRange[0];
            LocalDate endDate = dateRange[1];

            // 检查时间范围是否合理
            String timeValidation = validateTimeRange(startDate, endDate, userQuery);
            if (timeValidation != null) {
                return timeValidation;
            }

            Map<String, Object> businessData;

            // 根据查询类型调用不同的服务方法
            if (userQuery.contains("排行")) {
                if (userQuery.contains("营业额") || userQuery.contains("收入")) {
                    businessData = newProductSalesService.getNewProductSalesRankingByRevenue(startDate, endDate, 10);
                } else {
                    businessData = newProductSalesService.getNewProductSalesRankingByOrderCount(startDate, endDate, 10);
                }
            } else {
                businessData = newProductSalesService.getNewProductSalesStatistics(startDate, endDate);
            }

            // 生成友好的回复
            return generateNewProductSalesResponse(userQuery, businessData);

        } catch (Exception e) {
            log.error("处理新品成交查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 生成新品成交查询的友好回复
     */
    private String generateNewProductSalesResponse(String userQuery, Map<String, Object> businessData) {
        if (businessData.containsKey("error")) {
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }

        StringBuilder response = new StringBuilder();
        String queryType = (String) businessData.get("queryType");
        String period = (String) businessData.get("period");

        response.append("**").append(queryType).append("**\n\n");

        if (period != null) {
            response.append("查询时间：").append(period).append("\n");
        }

        response.append("**📊 总体概况**\n");

        // 获取统计数据，优先使用数据库查询结果
        Object totalProductCount = businessData.get("totalProductCount");
        Object totalOrderCount = businessData.get("totalOrderCount");
        Object totalRevenue = businessData.get("totalRevenue");

        log.info("业务数据统计 - 产品数量: {}, 订单数: {}, 总营业额: {}", totalProductCount, totalOrderCount, totalRevenue);

        // 如果数据库统计为空，使用计算出的数据
        Object calculatedRevenue = businessData.get("calculatedTotalRevenue");
        Object calculatedOrders = businessData.get("calculatedTotalOrders");
        Object calculatedProductCount = businessData.get("calculatedProductCount");

        // 确定最终显示的数据
        BigDecimal finalRevenue = null;
        Integer finalOrderCount = null;
        Integer finalProductCount = null;

        // 总营业额
        if (totalRevenue instanceof BigDecimal && ((BigDecimal)totalRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) totalRevenue;
        } else if (calculatedRevenue instanceof BigDecimal && ((BigDecimal)calculatedRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) calculatedRevenue;
        }

        // 总订单数
        if (totalOrderCount instanceof Number && ((Number)totalOrderCount).intValue() > 0) {
            finalOrderCount = ((Number)totalOrderCount).intValue();
        } else if (calculatedOrders instanceof Number && ((Number)calculatedOrders).intValue() > 0) {
            finalOrderCount = ((Number)calculatedOrders).intValue();
        }

        // 新品数量
        if (totalProductCount instanceof Number && ((Number)totalProductCount).intValue() > 0) {
            finalProductCount = ((Number)totalProductCount).intValue();
        } else if (calculatedProductCount instanceof Number && ((Number)calculatedProductCount).intValue() > 0) {
            finalProductCount = ((Number)calculatedProductCount).intValue();
        }

        // 显示统计信息
        if (finalProductCount != null && finalProductCount > 0) {
            response.append("• **新品数量：").append(finalProductCount).append("个** 🛍️\n");
        }

        if (finalOrderCount != null && finalOrderCount > 0) {
            response.append("• **总订单数：").append(finalOrderCount).append("单** 📦\n");
        }

        if (finalRevenue != null && finalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            response.append("• **总营业额：").append(formatCurrency(finalRevenue)).append("** 💰\n");
        }

        // 计算和显示平均值
        if (finalRevenue != null && finalOrderCount != null && finalOrderCount > 0) {
            double avgPerOrder = finalRevenue.doubleValue() / finalOrderCount;
            response.append("• 平均订单金额：").append(formatCurrency(BigDecimal.valueOf(avgPerOrder))).append(" 📊\n");
        }

        if (finalRevenue != null && finalProductCount != null && finalProductCount > 0) {
            double avgPerProduct = finalRevenue.doubleValue() / finalProductCount;
            response.append("• 平均单品营业额：").append(formatCurrency(BigDecimal.valueOf(avgPerProduct))).append(" 📈\n");
        }

        // 如果没有任何统计数据，显示提示
        if (finalRevenue == null && finalOrderCount == null && finalProductCount == null) {
            response.append("• 暂无统计数据\n");
        }

        // 产品明细或排行
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> productStats = (List<Map<String, Object>>) businessData.get("productStatistics");
        if (productStats == null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> productRanking = (List<Map<String, Object>>) businessData.get("productRanking");
            productStats = productRanking;
        }

        if (productStats != null && !productStats.isEmpty()) {
            String rankingType = (String) businessData.get("rankingType");
            if (rankingType != null) {
                response.append(String.format("\n**🏆 产品排行榜（按%s）**\n", rankingType));
            } else {
                response.append("\n**📋 产品明细**\n");
            }

            for (int i = 0; i < Math.min(10, productStats.size()); i++) {
                Map<String, Object> product = productStats.get(i);
                String productName = (String) product.get("productName");
                Object orderCount = product.get("orderCount");
                Object revenue = product.get("totalRevenue");
                String productType = (String) product.get("productType");

                // 排名图标
                String rankIcon = getRankIcon(i);
                response.append(String.format("%s **%s**", rankIcon, productName));

                if (orderCount != null && revenue != null) {
                    int orders = ((Number)orderCount).intValue();
                    BigDecimal productRevenue = (BigDecimal)revenue;
                    response.append(String.format("\n   📦 订单：%d单  💰 营业额：%s",
                        orders, formatCurrency(productRevenue)));

                    // 计算平均订单金额
                    if (orders > 0) {
                        double avgPerOrder = productRevenue.doubleValue() / orders;
                        response.append(String.format("  📊 单均：%s", formatCurrency(BigDecimal.valueOf(avgPerOrder))));
                    }
                }

                if (productType != null && !productType.isEmpty() && !"未知类别".equals(productType)) {
                    response.append(String.format("\n   🏷️ 类别：%s", productType));
                }
                response.append("\n\n");
            }

            // 如果有更多产品，显示提示
            if (productStats.size() > 10) {
                response.append(String.format("... 还有%d个产品未显示\n", productStats.size() - 10));
            }
        }

        return response.toString();
    }

    /**
     * 判断是否为全国交付业绩查询
     */
    private boolean isNationalDeliveryQuery(String query) {
        String lowerQuery = query.toLowerCase();

        // 必须同时包含"全国"和"交付"关键词
        boolean hasNationalAndDelivery = lowerQuery.contains("全国") && lowerQuery.contains("交付");

        // 必须包含业绩相关关键词
        boolean hasPerformanceKeyword = lowerQuery.contains("业绩") || lowerQuery.contains("成交") ||
                lowerQuery.contains("销售") || lowerQuery.contains("排行") ||
                lowerQuery.contains("统计") || lowerQuery.contains("情况");

        // 排除三嫂相关查询
        boolean isNotSansao = !lowerQuery.contains("三嫂") && !lowerQuery.contains("三姐妹");

        boolean result = hasNationalAndDelivery && hasPerformanceKeyword && isNotSansao;

        log.debug("全国交付查询识别 - 查询: '{}', 全国+交付: {}, 业绩关键词: {}, 非三嫂: {}, 结果: {}",
                 query, hasNationalAndDelivery, hasPerformanceKeyword, isNotSansao, result);

        return result;
    }

    /**
     * 处理全国交付业绩查询
     */
    private String handleNationalDeliveryQuery(String userQuery) {
        try {
            // 检查是否是不合理的查询
            String unreasonableCheck = checkUnreasonableQuery(userQuery);
            if (unreasonableCheck != null) {
                return unreasonableCheck;
            }

            // 解析时间范围
            LocalDate[] dateRange = parseTimeRange(userQuery);
            LocalDate startDate = dateRange[0];
            LocalDate endDate = dateRange[1];

            // 检查时间范围是否合理
            String timeValidation = validateTimeRange(startDate, endDate, userQuery);
            if (timeValidation != null) {
                return timeValidation;
            }

            Map<String, Object> businessData;

            // 根据查询类型调用不同的服务方法
            if (userQuery.contains("排行")) {
                if (userQuery.contains("营业额") || userQuery.contains("收入")) {
                    businessData = nationalDeliveryService.getNationalDeliveryRankingByRevenue(startDate, endDate, 10);
                } else {
                    businessData = nationalDeliveryService.getNationalDeliveryRankingByOrderCount(startDate, endDate, 10);
                }
            } else {
                businessData = nationalDeliveryService.getNationalDeliveryStatistics(startDate, endDate);
            }

            // 生成友好的回复
            return generateNationalDeliveryResponse(userQuery, businessData);

        } catch (Exception e) {
            log.error("处理全国交付业绩查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 生成全国交付业绩查询的友好回复
     */
    private String generateNationalDeliveryResponse(String userQuery, Map<String, Object> businessData) {
        if (businessData.containsKey("error")) {
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }

        StringBuilder response = new StringBuilder();
        String queryType = (String) businessData.get("queryType");
        String period = (String) businessData.get("period");

        response.append("**").append(queryType).append("**\n\n");

        if (period != null) {
            response.append("查询时间：").append(period).append("\n");
        }

        response.append("**📊 总体概况**\n");

        // 获取统计数据，使用与新品成交相同的逻辑
        Object totalProductCount = businessData.get("totalProductCount");
        Object totalOrderCount = businessData.get("totalOrderCount");
        Object totalRevenue = businessData.get("totalRevenue");

        log.info("全国交付业务数据统计 - 产品数量: {}, 订单数: {}, 总营业额: {}", totalProductCount, totalOrderCount, totalRevenue);

        // 优先使用计算出的数据
        Object calculatedRevenue = businessData.get("calculatedTotalRevenue");
        Object calculatedOrders = businessData.get("calculatedTotalOrders");
        Object calculatedProductCount = businessData.get("calculatedProductCount");

        // 确定最终显示的数据
        BigDecimal finalRevenue = null;
        Integer finalOrderCount = null;
        Integer finalProductCount = null;

        // 总营业额
        if (totalRevenue instanceof BigDecimal && ((BigDecimal)totalRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) totalRevenue;
        } else if (calculatedRevenue instanceof BigDecimal && ((BigDecimal)calculatedRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) calculatedRevenue;
        }

        // 总订单数
        if (totalOrderCount instanceof Number && ((Number)totalOrderCount).intValue() > 0) {
            finalOrderCount = ((Number)totalOrderCount).intValue();
        } else if (calculatedOrders instanceof Number && ((Number)calculatedOrders).intValue() > 0) {
            finalOrderCount = ((Number)calculatedOrders).intValue();
        }

        // 产品数量
        if (totalProductCount instanceof Number && ((Number)totalProductCount).intValue() > 0) {
            finalProductCount = ((Number)totalProductCount).intValue();
        } else if (calculatedProductCount instanceof Number && ((Number)calculatedProductCount).intValue() > 0) {
            finalProductCount = ((Number)calculatedProductCount).intValue();
        }

        // 显示统计信息
        if (finalProductCount != null && finalProductCount > 0) {
            response.append("• **产品数量：").append(finalProductCount).append("个** 🛍️\n");
        }

        if (finalOrderCount != null && finalOrderCount > 0) {
            response.append("• **总订单数：").append(finalOrderCount).append("单** 📦\n");
        }

        if (finalRevenue != null && finalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            response.append("• **总营业额：").append(formatCurrency(finalRevenue)).append("** 💰\n");
        }

        // 计算和显示平均值
        if (finalRevenue != null && finalOrderCount != null && finalOrderCount > 0) {
            double avgPerOrder = finalRevenue.doubleValue() / finalOrderCount;
            response.append("• 平均订单金额：").append(formatCurrency(BigDecimal.valueOf(avgPerOrder))).append(" 📊\n");
        }

        // 如果没有任何统计数据，显示提示
        if (finalRevenue == null && finalOrderCount == null && finalProductCount == null) {
            response.append("• 暂无统计数据\n");
        }

        // 产品明细或排行（简化版本）
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> productStats = (List<Map<String, Object>>) businessData.get("productStatistics");
        if (productStats == null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> productRanking = (List<Map<String, Object>>) businessData.get("productRanking");
            productStats = productRanking;
        }

        if (productStats != null && !productStats.isEmpty()) {
            String rankingType = (String) businessData.get("rankingType");
            if (rankingType != null) {
                response.append(String.format("\n**🏆 产品排行榜（按%s）**\n", rankingType));
            } else {
                response.append("\n**📋 产品明细**\n");
            }

            for (int i = 0; i < Math.min(5, productStats.size()); i++) {
                Map<String, Object> product = productStats.get(i);
                String productName = (String) product.get("productName");
                Object orderCount = product.get("orderCount");
                Object revenue = product.get("totalRevenue");

                String rankIcon = getRankIcon(i);
                response.append(String.format("%s **%s**", rankIcon, productName));

                if (orderCount != null && revenue != null) {
                    int orders = ((Number)orderCount).intValue();
                    BigDecimal productRevenue = (BigDecimal)revenue;
                    response.append(String.format("：%d单，%s", orders, formatCurrency(productRevenue)));
                }
                response.append("\n");
            }

            if (productStats.size() > 5) {
                response.append(String.format("... 还有%d个产品未显示\n", productStats.size() - 5));
            }
        }

        return response.toString();
    }

    /**
     * 判断是否为驻场保洁和开荒集团结算查询
     */
    private boolean isCleaningSettlementQuery(String query) {
        String lowerQuery = query.toLowerCase();

        // 必须包含驻场保洁或开荒相关关键词（扩展关键词）
        boolean hasCleaningKeyword = lowerQuery.contains("驻场保洁") || lowerQuery.contains("开荒") ||
                                    lowerQuery.contains("保洁") || lowerQuery.contains("清洁") ||
                                    lowerQuery.contains("驻场") ||  // 新增：支持"驻场"单独关键词
                                    lowerQuery.contains("清洗");   // 新增：支持"清洗"关键词

        // 必须包含集团结算相关关键词
        boolean hasSettlementKeyword = lowerQuery.contains("集团结算") || lowerQuery.contains("结算") ||
                                      lowerQuery.contains("业绩") || lowerQuery.contains("统计") ||
                                      lowerQuery.contains("排行") || lowerQuery.contains("情况");

        // 排除其他查询类型
        boolean isNotOtherQuery = !lowerQuery.contains("三嫂") && !lowerQuery.contains("交付") &&
                                 !lowerQuery.contains("新品") && !lowerQuery.contains("金刚");

        boolean result = hasCleaningKeyword && hasSettlementKeyword && isNotOtherQuery;

        log.debug("驻场保洁集团结算查询识别 - 查询: '{}', 保洁关键词: {}, 结算关键词: {}, 非其他查询: {}, 结果: {}",
                 query, hasCleaningKeyword, hasSettlementKeyword, isNotOtherQuery, result);

        return result;
    }

    /**
     * 处理驻场保洁和开荒集团结算查询
     */
    private String handleCleaningSettlementQuery(String userQuery) {
        try {
            // 检查是否是不合理的查询
            String unreasonableCheck = checkUnreasonableQuery(userQuery);
            if (unreasonableCheck != null) {
                return unreasonableCheck;
            }

            // 解析时间范围
            LocalDate[] dateRange = parseTimeRange(userQuery);
            LocalDate startDate = dateRange[0];
            LocalDate endDate = dateRange[1];

            // 检查时间范围是否合理
            String timeValidation = validateTimeRange(startDate, endDate, userQuery);
            if (timeValidation != null) {
                return timeValidation;
            }

            Map<String, Object> businessData;

            // 根据查询类型调用不同的服务方法
            if (userQuery.contains("排行")) {
                if (userQuery.contains("营业额") || userQuery.contains("收入")) {
                    businessData = cleaningSettlementService.getCleaningSettlementRankingByRevenue(startDate, endDate, 10);
                } else {
                    businessData = cleaningSettlementService.getCleaningSettlementRankingByOrderCount(startDate, endDate, 10);
                }
            } else {
                businessData = cleaningSettlementService.getCleaningSettlementStatistics(startDate, endDate);
            }

            // 生成友好的回复
            return generateCleaningSettlementResponse(userQuery, businessData);

        } catch (Exception e) {
            log.error("处理驻场保洁和开荒集团结算查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 生成驻场保洁和开荒集团结算查询的友好回复
     */
    private String generateCleaningSettlementResponse(String userQuery, Map<String, Object> businessData) {
        if (businessData.containsKey("error")) {
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }

        StringBuilder response = new StringBuilder();
        String queryType = (String) businessData.get("queryType");
        String period = (String) businessData.get("period");

        response.append("**").append(queryType).append("**\n\n");

        if (period != null) {
            response.append("查询时间：").append(period).append("\n");
        }

        response.append("**📊 总体概况**\n");

        // 获取统计数据，使用与其他服务相同的逻辑
        Object totalProductCount = businessData.get("totalProductCount");
        Object totalOrderCount = businessData.get("totalOrderCount");
        Object totalRevenue = businessData.get("totalRevenue");

        log.info("驻场保洁集团结算业务数据统计 - 产品数量: {}, 订单数: {}, 总营业额: {}", totalProductCount, totalOrderCount, totalRevenue);

        // 优先使用计算出的数据
        Object calculatedRevenue = businessData.get("calculatedTotalRevenue");
        Object calculatedOrders = businessData.get("calculatedTotalOrders");
        Object calculatedProductCount = businessData.get("calculatedProductCount");

        // 确定最终显示的数据
        BigDecimal finalRevenue = null;
        Integer finalOrderCount = null;
        Integer finalProductCount = null;

        // 总营业额
        if (totalRevenue instanceof BigDecimal && ((BigDecimal)totalRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) totalRevenue;
        } else if (calculatedRevenue instanceof BigDecimal && ((BigDecimal)calculatedRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) calculatedRevenue;
        }

        // 总订单数
        if (totalOrderCount instanceof Number && ((Number)totalOrderCount).intValue() > 0) {
            finalOrderCount = ((Number)totalOrderCount).intValue();
        } else if (calculatedOrders instanceof Number && ((Number)calculatedOrders).intValue() > 0) {
            finalOrderCount = ((Number)calculatedOrders).intValue();
        }

        // 产品数量
        if (totalProductCount instanceof Number && ((Number)totalProductCount).intValue() > 0) {
            finalProductCount = ((Number)totalProductCount).intValue();
        } else if (calculatedProductCount instanceof Number && ((Number)calculatedProductCount).intValue() > 0) {
            finalProductCount = ((Number)calculatedProductCount).intValue();
        }

        // 显示统计信息
        if (finalProductCount != null && finalProductCount > 0) {
            response.append("• **产品数量：").append(finalProductCount).append("个** 🧹\n");
        }

        if (finalOrderCount != null && finalOrderCount > 0) {
            response.append("• **总订单数：").append(finalOrderCount).append("单** 📦\n");
        }

        if (finalRevenue != null && finalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            response.append("• **总营业额：").append(formatCurrency(finalRevenue)).append("** 💰\n");
        }

        // 计算和显示平均值
        if (finalRevenue != null && finalOrderCount != null && finalOrderCount > 0) {
            double avgPerOrder = finalRevenue.doubleValue() / finalOrderCount;
            response.append("• 平均订单金额：").append(formatCurrency(BigDecimal.valueOf(avgPerOrder))).append(" 📊\n");
        }

        // 如果没有任何统计数据，显示提示
        if (finalRevenue == null && finalOrderCount == null && finalProductCount == null) {
            response.append("• 暂无统计数据\n");
        }

        // 产品明细或排行（简化版本）
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> productStats = (List<Map<String, Object>>) businessData.get("productStatistics");
        if (productStats == null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> productRanking = (List<Map<String, Object>>) businessData.get("productRanking");
            productStats = productRanking;
        }

        if (productStats != null && !productStats.isEmpty()) {
            String rankingType = (String) businessData.get("rankingType");
            if (rankingType != null) {
                response.append(String.format("\n**🏆 产品排行榜（按%s）**\n", rankingType));
            } else {
                response.append("\n**📋 产品明细**\n");
            }

            for (int i = 0; i < Math.min(5, productStats.size()); i++) {
                Map<String, Object> product = productStats.get(i);
                String productName = (String) product.get("productName");
                Object orderCount = product.get("orderCount");
                Object revenue = product.get("totalRevenue");

                String rankIcon = getRankIcon(i);
                response.append(String.format("%s **%s**", rankIcon, productName));

                if (orderCount != null && revenue != null) {
                    int orders = ((Number)orderCount).intValue();
                    BigDecimal productRevenue = (BigDecimal)revenue;
                    response.append(String.format("：%d单，%s", orders, formatCurrency(productRevenue)));
                }
                response.append("\n");
            }

            if (productStats.size() > 5) {
                response.append(String.format("... 还有%d个产品未显示\n", productStats.size() - 5));
            }
        }

        return response.toString();
    }

    /**
     * 判断是否为大学收入查询
     */
    private boolean isUniversityIncomeQuery(String query) {
        String lowerQuery = query.toLowerCase();

        // 必须包含大学相关关键词
        boolean hasUniversityKeyword = lowerQuery.contains("大学") || lowerQuery.contains("高校") ||
                                      lowerQuery.contains("学校") || lowerQuery.contains("校园");

        // 必须包含收入相关关键词
        boolean hasIncomeKeyword = lowerQuery.contains("收入") || lowerQuery.contains("营收") ||
                                  lowerQuery.contains("业绩") || lowerQuery.contains("统计") ||
                                  lowerQuery.contains("排行") || lowerQuery.contains("情况");

        // 排除其他查询类型
        boolean isNotOtherQuery = !lowerQuery.contains("三嫂") && !lowerQuery.contains("交付") &&
                                 !lowerQuery.contains("新品") && !lowerQuery.contains("金刚") &&
                                 !lowerQuery.contains("保洁") && !lowerQuery.contains("驻场");

        boolean result = hasUniversityKeyword && hasIncomeKeyword && isNotOtherQuery;

        log.debug("大学收入查询识别 - 查询: '{}', 大学关键词: {}, 收入关键词: {}, 非其他查询: {}, 结果: {}",
                 query, hasUniversityKeyword, hasIncomeKeyword, isNotOtherQuery, result);

        return result;
    }

    /**
     * 处理大学收入查询
     */
    private String handleUniversityIncomeQuery(String userQuery) {
        try {
            // 检查是否是不合理的查询
            String unreasonableCheck = checkUnreasonableQuery(userQuery);
            if (unreasonableCheck != null) {
                return unreasonableCheck;
            }

            // 解析时间范围
            LocalDate[] dateRange = parseTimeRange(userQuery);
            LocalDate startDate = dateRange[0];
            LocalDate endDate = dateRange[1];

            // 检查时间范围是否合理
            String timeValidation = validateTimeRange(startDate, endDate, userQuery);
            if (timeValidation != null) {
                return timeValidation;
            }

            Map<String, Object> businessData;

            // 根据查询类型调用不同的服务方法
            if (userQuery.contains("排行")) {
                if (userQuery.contains("营业额") || userQuery.contains("收入")) {
                    businessData = universityIncomeService.getUniversityIncomeRankingByRevenue(startDate, endDate, 10);
                } else {
                    businessData = universityIncomeService.getUniversityIncomeRankingByOrderCount(startDate, endDate, 10);
                }
            } else {
                businessData = universityIncomeService.getUniversityIncomeStatistics(startDate, endDate);
            }

            // 生成友好的回复
            return generateUniversityIncomeResponse(userQuery, businessData);

        } catch (Exception e) {
            log.error("处理大学收入查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 生成大学收入查询的友好回复
     */
    private String generateUniversityIncomeResponse(String userQuery, Map<String, Object> businessData) {
        if (businessData.containsKey("error")) {
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }

        StringBuilder response = new StringBuilder();
        String queryType = (String) businessData.get("queryType");
        String period = (String) businessData.get("period");

        response.append("**").append(queryType).append("**\n\n");

        if (period != null) {
            response.append("查询时间：").append(period).append("\n");
        }

        response.append("**📊 总体概况**\n");

        // 获取统计数据，使用与其他服务相同的逻辑
        Object totalProductCount = businessData.get("totalProductCount");
        Object totalOrderCount = businessData.get("totalOrderCount");
        Object totalRevenue = businessData.get("totalRevenue");

        log.info("大学收入业务数据统计 - 产品数量: {}, 订单数: {}, 总营业额: {}", totalProductCount, totalOrderCount, totalRevenue);

        // 优先使用计算出的数据
        Object calculatedRevenue = businessData.get("calculatedTotalRevenue");
        Object calculatedOrders = businessData.get("calculatedTotalOrders");
        Object calculatedProductCount = businessData.get("calculatedProductCount");

        // 确定最终显示的数据
        BigDecimal finalRevenue = null;
        Integer finalOrderCount = null;
        Integer finalProductCount = null;

        // 总营业额
        if (totalRevenue instanceof BigDecimal && ((BigDecimal)totalRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) totalRevenue;
        } else if (calculatedRevenue instanceof BigDecimal && ((BigDecimal)calculatedRevenue).compareTo(BigDecimal.ZERO) > 0) {
            finalRevenue = (BigDecimal) calculatedRevenue;
        }

        // 总订单数
        if (totalOrderCount instanceof Number && ((Number)totalOrderCount).intValue() > 0) {
            finalOrderCount = ((Number)totalOrderCount).intValue();
        } else if (calculatedOrders instanceof Number && ((Number)calculatedOrders).intValue() > 0) {
            finalOrderCount = ((Number)calculatedOrders).intValue();
        }

        // 产品数量
        if (totalProductCount instanceof Number && ((Number)totalProductCount).intValue() > 0) {
            finalProductCount = ((Number)totalProductCount).intValue();
        } else if (calculatedProductCount instanceof Number && ((Number)calculatedProductCount).intValue() > 0) {
            finalProductCount = ((Number)calculatedProductCount).intValue();
        }

        // 显示统计信息
        if (finalProductCount != null && finalProductCount > 0) {
            response.append("• **产品数量：").append(finalProductCount).append("个** 🎓\n");
        }

        if (finalOrderCount != null && finalOrderCount > 0) {
            response.append("• **总订单数：").append(finalOrderCount).append("单** 📦\n");
        }

        if (finalRevenue != null && finalRevenue.compareTo(BigDecimal.ZERO) > 0) {
            response.append("• **总收入：").append(formatCurrency(finalRevenue)).append("** 💰\n");
        }

        // 计算和显示平均值
        if (finalRevenue != null && finalOrderCount != null && finalOrderCount > 0) {
            double avgPerOrder = finalRevenue.doubleValue() / finalOrderCount;
            response.append("• 平均订单金额：").append(formatCurrency(BigDecimal.valueOf(avgPerOrder))).append(" 📊\n");
        }

        // 如果没有任何统计数据，显示提示
        if (finalRevenue == null && finalOrderCount == null && finalProductCount == null) {
            response.append("• 暂无统计数据\n");
        }

        // 产品明细或排行（简化版本）
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> productStats = (List<Map<String, Object>>) businessData.get("productStatistics");
        if (productStats == null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> productRanking = (List<Map<String, Object>>) businessData.get("productRanking");
            productStats = productRanking;
        }

        if (productStats != null && !productStats.isEmpty()) {
            String rankingType = (String) businessData.get("rankingType");
            if (rankingType != null) {
                response.append(String.format("\n**🏆 产品排行榜（按%s）**\n", rankingType));
            } else {
                response.append("\n**📋 产品明细**\n");
            }

            for (int i = 0; i < Math.min(5, productStats.size()); i++) {
                Map<String, Object> product = productStats.get(i);
                String productName = (String) product.get("productName");
                Object orderCount = product.get("orderCount");
                Object revenue = product.get("totalRevenue");

                String rankIcon = getRankIcon(i);
                response.append(String.format("%s **%s**", rankIcon, productName));

                if (orderCount != null && revenue != null) {
                    int orders = ((Number)orderCount).intValue();
                    BigDecimal productRevenue = (BigDecimal)revenue;
                    response.append(String.format("：%d单，%s", orders, formatCurrency(productRevenue)));
                }
                response.append("\n");
            }

            if (productStats.size() > 5) {
                response.append(String.format("... 还有%d个产品未显示\n", productStats.size() - 5));
            }
        }

        return response.toString();
    }

    /**
     * 判断是否为私域ABC收款业绩查询
     */
    private boolean isPrivateDomainQuery(String query) {
        String lowerQuery = query.toLowerCase();

        // 必须包含私域相关关键词
        boolean hasPrivateDomainKeyword = lowerQuery.contains("私域") || lowerQuery.contains("abc") ||
                                         lowerQuery.contains("收款");

        // 必须包含业绩相关关键词
        boolean hasPerformanceKeyword = lowerQuery.contains("业绩") || lowerQuery.contains("收入") ||
                                       lowerQuery.contains("营收") || lowerQuery.contains("统计") ||
                                       lowerQuery.contains("排行") || lowerQuery.contains("情况") ||
                                       lowerQuery.contains("完成率") || lowerQuery.contains("目标");

        // 排除其他查询类型
        boolean isNotOtherQuery = !lowerQuery.contains("三嫂") && !lowerQuery.contains("交付") &&
                                 !lowerQuery.contains("新品") && !lowerQuery.contains("金刚") &&
                                 !lowerQuery.contains("保洁") && !lowerQuery.contains("驻场") &&
                                 !lowerQuery.contains("大学") && !lowerQuery.contains("高校");

        boolean result = hasPrivateDomainKeyword && hasPerformanceKeyword && isNotOtherQuery;

        log.debug("私域ABC收款业绩查询识别 - 查询: '{}', 私域关键词: {}, 业绩关键词: {}, 非其他查询: {}, 结果: {}",
                 query, hasPrivateDomainKeyword, hasPerformanceKeyword, isNotOtherQuery, result);

        return result;
    }

    /**
     * 处理私域ABC收款业绩查询
     */
    private String handlePrivateDomainQuery(String userQuery) {
        try {
            // 检查是否是不合理的查询
            String unreasonableCheck = checkUnreasonableQuery(userQuery);
            if (unreasonableCheck != null) {
                return unreasonableCheck;
            }

            // 解析时间范围
            LocalDate[] dateRange = parseTimeRange(userQuery);
            LocalDate startDate = dateRange[0];
            LocalDate endDate = dateRange[1];

            // 检查时间范围是否合理
            String timeValidation = validateTimeRange(startDate, endDate, userQuery);
            if (timeValidation != null) {
                return timeValidation;
            }

            Map<String, Object> businessData;

            // 根据查询类型调用不同的服务方法
            if (userQuery.contains("趋势") || userQuery.contains("每日") || userQuery.contains("日统计")) {
                businessData = privateDomainService.getPrivateDomainTrend(startDate, endDate);
            } else {
                businessData = privateDomainService.getPrivateDomainStatistics(startDate, endDate);
            }

            // 生成友好的回复
            return generatePrivateDomainResponse(userQuery, businessData);

        } catch (Exception e) {
            log.error("处理私域ABC收款业绩查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 生成私域ABC收款业绩查询的友好回复
     */
    private String generatePrivateDomainResponse(String userQuery, Map<String, Object> businessData) {
        if (businessData.containsKey("error")) {
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }

        StringBuilder response = new StringBuilder();
        String queryType = (String) businessData.get("queryType");
        String period = (String) businessData.get("period");

        response.append("**").append(queryType).append("**\n\n");

        if (period != null) {
            response.append("查询时间：").append(period).append("\n");
        }

        // 获取基础数据
        Object totalAmount = businessData.get("totalAmount");
        Object orderAmount = businessData.get("orderAmount");
        Object directAmount = businessData.get("directAmount");
        Object orderCount = businessData.get("orderCount");
        Object directPaymentCount = businessData.get("directPaymentCount");
        Object totalPaymentCount = businessData.get("totalPaymentCount");

        log.info("私域ABC收款业绩数据统计 - 总金额: {}, 订单金额: {}, 直接收款: {}, 订单数: {}, 直接支付数: {}",
                totalAmount, orderAmount, directAmount, orderCount, directPaymentCount);

        response.append("**📊 收款业绩概况**\n");

        if (totalAmount instanceof BigDecimal) {
            BigDecimal total = (BigDecimal) totalAmount;
            response.append("• **总收款金额：").append(formatCurrency(total)).append("** 💰\n");

            if (orderAmount instanceof BigDecimal) {
                response.append("• 订单收款：").append(formatCurrency((BigDecimal) orderAmount)).append(" 📦\n");
            }

            if (directAmount instanceof BigDecimal) {
                response.append("• 直接收款：").append(formatCurrency((BigDecimal) directAmount)).append(" 💳\n");
            }
        }

        if (totalPaymentCount instanceof Number) {
            response.append("• **总支付笔数：").append(totalPaymentCount).append("笔** 📊\n");

            if (orderCount instanceof Number) {
                response.append("• 订单支付：").append(orderCount).append("笔\n");
            }

            if (directPaymentCount instanceof Number) {
                response.append("• 直接支付：").append(directPaymentCount).append("笔\n");
            }
        }

        // 目标完成情况
        Object monthlyTarget = businessData.get("monthlyTarget");
        Object proportionalTarget = businessData.get("proportionalTarget");
        Object completionRate = businessData.get("completionRate");
        Object remainingTarget = businessData.get("remainingTarget");
        Object isTargetAchieved = businessData.get("isTargetAchieved");

        // 月度目标相关数据
        Object monthlyCompletionRate = businessData.get("monthlyCompletionRate");
        Object monthlyRemainingTarget = businessData.get("monthlyRemainingTarget");
        Object isMonthlyTargetAchieved = businessData.get("isMonthlyTargetAchieved");
        Object queryDays = businessData.get("queryDays");
        Object monthDays = businessData.get("monthDays");

        if (monthlyTarget != null || proportionalTarget != null) {
            response.append("\n**🎯 目标完成情况**\n");

            // 如果是期间目标，显示完整的目标对比信息
            if (proportionalTarget instanceof BigDecimal && monthlyTarget instanceof BigDecimal) {
                response.append("• **月度总目标：").append(formatCurrency((BigDecimal) monthlyTarget)).append("** 🎯\n");
                response.append("• **期间目标：").append(formatCurrency((BigDecimal) proportionalTarget)).append("** 📅\n");

                if (queryDays != null && monthDays != null) {
                    response.append("• 查询时间：").append(queryDays).append("天 / ").append(monthDays).append("天\n");
                }

                // 期间目标完成情况
                if (completionRate instanceof BigDecimal) {
                    BigDecimal rate = (BigDecimal) completionRate;
                    String rateStr = String.format("%.2f%%", rate.doubleValue());

                    if (Boolean.TRUE.equals(isTargetAchieved)) {
                        response.append("• **期间完成率：").append(rateStr).append("** ✅ 期间目标已达成！\n");
                    } else {
                        response.append("• **期间完成率：").append(rateStr).append("** 📈\n");
                    }
                }

                if (remainingTarget instanceof BigDecimal) {
                    BigDecimal remaining = (BigDecimal) remainingTarget;
                    if (remaining.compareTo(BigDecimal.ZERO) > 0) {
                        response.append("• **期间剩余：").append(formatCurrency(remaining)).append("** 🚀\n");
                    }
                }

                // 月度目标完成情况
                if (monthlyCompletionRate instanceof BigDecimal) {
                    BigDecimal monthlyRate = (BigDecimal) monthlyCompletionRate;
                    String monthlyRateStr = String.format("%.2f%%", monthlyRate.doubleValue());

                    if (Boolean.TRUE.equals(isMonthlyTargetAchieved)) {
                        response.append("• **月度完成率：").append(monthlyRateStr).append("** ✅ 月度目标已达成！\n");
                    } else {
                        response.append("• **月度完成率：").append(monthlyRateStr).append("** 📊\n");
                    }
                }

                if (monthlyRemainingTarget instanceof BigDecimal) {
                    BigDecimal monthlyRemaining = (BigDecimal) monthlyRemainingTarget;
                    if (monthlyRemaining.compareTo(BigDecimal.ZERO) > 0) {
                        response.append("• **月度剩余：").append(formatCurrency(monthlyRemaining)).append("** 🎯\n");
                    }
                }

            } else if (monthlyTarget instanceof BigDecimal) {
                // 纯月度目标
                response.append("• **月度目标：").append(formatCurrency((BigDecimal) monthlyTarget)).append("** 🎯\n");

                if (completionRate instanceof BigDecimal) {
                    BigDecimal rate = (BigDecimal) completionRate;
                    String rateStr = String.format("%.2f%%", rate.doubleValue());

                    if (Boolean.TRUE.equals(isTargetAchieved)) {
                        response.append("• **完成率：").append(rateStr).append("** ✅ 已达成目标！\n");
                    } else {
                        response.append("• **完成率：").append(rateStr).append("** 📈\n");
                    }
                }

                if (remainingTarget instanceof BigDecimal) {
                    BigDecimal remaining = (BigDecimal) remainingTarget;
                    if (remaining.compareTo(BigDecimal.ZERO) > 0) {
                        response.append("• **剩余目标：").append(formatCurrency(remaining)).append("** 🚀\n");
                    }
                }
            }
        }

        // 计算平均值
        if (totalAmount instanceof BigDecimal && totalPaymentCount instanceof Number) {
            BigDecimal total = (BigDecimal) totalAmount;
            int count = ((Number) totalPaymentCount).intValue();
            if (count > 0) {
                double avgPerPayment = total.doubleValue() / count;
                response.append("\n• 平均支付金额：").append(formatCurrency(BigDecimal.valueOf(avgPerPayment))).append(" 📊\n");
            }
        }

        return response.toString();
    }

    /**
     * 检查不合理的查询
     */
    private String checkUnreasonableQuery(String query) {
        String lowerQuery = query.toLowerCase();

        // 检查未来时间查询
        if (lowerQuery.contains("下周") || lowerQuery.contains("下个星期") ||
            lowerQuery.contains("下月") || lowerQuery.contains("下个月") ||
            lowerQuery.contains("明天") || lowerQuery.contains("明日") ||
            lowerQuery.contains("后天") || lowerQuery.contains("大后天") ||
            lowerQuery.contains("下年") || lowerQuery.contains("明年") ||
            lowerQuery.contains("未来") || lowerQuery.contains("将来")) {
            return "哈哈哈哈，您可太幽默了，我没有提前预知的超能力哦~ 😄\n\n我只能查询已经发生的历史数据，比如：\n• 本月新品成交情况\n• 上周新品成交统计\n• 昨天新品成交数据";
        }

        // 检查过于久远的查询
        if (lowerQuery.contains("去年") || lowerQuery.contains("前年") ||
            lowerQuery.contains("上年") || lowerQuery.contains("几年前")) {
            return "emmm... 您查询的时间太久远了，我的记忆力没那么好哦~ 😅\n\n建议查询近期的数据，比如：\n• 本月新品成交情况\n• 上月新品成交统计\n• 近30天新品成交数据";
        }

        // 检查模糊不清的查询
        if (lowerQuery.contains("什么时候") || lowerQuery.contains("哪天") ||
            lowerQuery.contains("几时") || lowerQuery.contains("何时")) {
            return "您的问题有点模糊哦~ 🤔\n\n请具体说明要查询的时间范围，比如：\n• 本月新品成交情况\n• 上周新品成交排行\n• 近7天新品成交统计";
        }

        return null; // 查询合理
    }

    /**
     * 验证时间范围是否合理
     */
    private String validateTimeRange(LocalDate startDate, LocalDate endDate, String originalQuery) {
        LocalDate today = LocalDate.now();

        // 检查是否查询未来时间
        if (startDate.isAfter(today) || endDate.isAfter(today)) {
            return "哈哈哈哈，您可太幽默了，我没有提前预知的超能力哦~ 😄\n\n" +
                   String.format("您查询的时间范围：%s 到 %s\n", startDate, endDate) +
                   "但今天才是：" + today + "\n\n" +
                   "我只能查询已经发生的历史数据哦！";
        }

        // 检查时间范围是否过大
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        if (daysBetween > 365) {
            return "哇，您要查询的时间跨度太大了！😱\n\n" +
                   String.format("从 %s 到 %s，整整 %d 天呢！\n", startDate, endDate, daysBetween + 1) +
                   "为了更好的查询体验，建议查询时间范围不超过1年哦~";
        }

        // 检查时间范围是否过于久远
        long daysFromToday = ChronoUnit.DAYS.between(startDate, today);
        if (daysFromToday > 730) { // 超过2年
            return "您查询的时间有点久远呢~ ⏰\n\n" +
                   String.format("从 %s 到现在已经过去 %d 天了！\n", startDate, daysFromToday) +
                   "建议查询近期的数据，比如近几个月的新品成交情况~";
        }

        return null; // 时间范围合理
    }

    /**
     * 解析时间范围
     */
    private LocalDate[] parseTimeRange(String query) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate;

        String lowerQuery = query.toLowerCase();

        if (lowerQuery.contains("今天") || lowerQuery.contains("今日")) {
            startDate = endDate;
        } else if (lowerQuery.contains("昨天") || lowerQuery.contains("昨日")) {
            startDate = endDate.minusDays(1);
            endDate = startDate;
        } else if (lowerQuery.contains("上周") || lowerQuery.contains("上星期")) {
            startDate = endDate.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
            endDate = startDate.plusDays(6);
        } else if (lowerQuery.contains("本周") || lowerQuery.contains("这周")) {
            startDate = endDate.with(java.time.DayOfWeek.MONDAY);
        } else if (lowerQuery.contains("上月") || lowerQuery.contains("上个月")) {
            startDate = endDate.minusMonths(1).withDayOfMonth(1);
            endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        } else if (lowerQuery.contains("本月") || lowerQuery.contains("这个月")) {
            startDate = endDate.withDayOfMonth(1);
        } else if (lowerQuery.contains("近7天") || lowerQuery.contains("最近7天")) {
            startDate = endDate.minusDays(6);
        } else if (lowerQuery.contains("近30天") || lowerQuery.contains("最近30天")) {
            startDate = endDate.minusDays(29);
        } else {
            // 默认查询最近7天
            startDate = endDate.minusDays(6);
        }

        return new LocalDate[]{startDate, endDate};
    }

    /**
     * 格式化货币显示
     */
    private String formatCurrency(BigDecimal amount) {
        if (amount == null) {
            return "0.00元";
        }

        double value = amount.doubleValue();
        if (value >= 10000) {
            return String.format("%.2f万元", value / 10000);
        } else {
            return String.format("%,.2f元", value);
        }
    }

    /**
     * 获取排名图标
     */
    private String getRankIcon(int rank) {
        switch (rank) {
            case 0: return "🥇";
            case 1: return "🥈";
            case 2: return "🥉";
            case 3: return "4️⃣";
            case 4: return "5️⃣";
            case 5: return "6️⃣";
            case 6: return "7️⃣";
            case 7: return "8️⃣";
            case 8: return "9️⃣";
            case 9: return "🔟";
            default: return String.format("%d.", rank + 1);
        }
    }

    /**
     * 查询意图数据类
     */
    public static class QueryIntent {
        private String queryType;
        private LocalDate startDate;
        private LocalDate endDate;
        private String storeIdentifier;
        private Integer limit;
        
        // Getters and Setters
        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }
        
        public LocalDate getStartDate() { return startDate; }
        public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
        
        public LocalDate getEndDate() { return endDate; }
        public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
        
        public String getStoreIdentifier() { return storeIdentifier; }
        public void setStoreIdentifier(String storeIdentifier) { this.storeIdentifier = storeIdentifier; }
        
        public Integer getLimit() { return limit; }
        public void setLimit(Integer limit) { this.limit = limit; }
        
        @Override
        public String toString() {
            return String.format("QueryIntent{queryType='%s', startDate=%s, endDate=%s, storeIdentifier='%s', limit=%d}",
                    queryType, startDate, endDate, storeIdentifier, limit);
        }
    }
}
