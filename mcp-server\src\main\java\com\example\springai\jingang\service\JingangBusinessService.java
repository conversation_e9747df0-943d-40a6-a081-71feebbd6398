package com.example.springai.jingang.service;

import com.example.springai.service.impl.TongYiServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * 金刚到家业务智能查询服务
 * 专门处理金刚到家软件收入相关的自然语言查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class JingangBusinessService {

    @Autowired
    private JingangIncomeService jingangIncomeService;

    @Autowired
    private TongYiServiceImpl tongYiService;

    /**
     * 处理金刚到家软件收入查询
     */
    public String handleJingangQuery(String userQuery) {
        log.info("处理金刚到家软件收入查询: {}", userQuery);

        try {
            // 解析查询类型和时间范围
            JingangQueryIntent intent = parseJingangQueryIntent(userQuery);
            log.info("解析的金刚到家查询意图: {}", intent);

            // 执行相应的查询
            String result = executeJingangQuery(intent);

            // 使用AI优化回复
            return enhanceResponseWithAI(userQuery, result);

        } catch (Exception e) {
            log.error("处理金刚到家软件收入查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 解析金刚到家查询意图
     */
    private JingangQueryIntent parseJingangQueryIntent(String userQuery) {
        JingangQueryIntent intent = new JingangQueryIntent();
        String query = userQuery.toLowerCase();

        // 解析查询类型
        if (query.contains("表结构") || query.contains("测试表")) {
            intent.setQueryType("表结构");
        } else if (query.contains("全部") || query.contains("所有") || query.contains("测试收入")) {
            intent.setQueryType("全部收入");
        } else if (query.contains("明细") || query.contains("详细") || query.contains("列表")) {
            intent.setQueryType("明细");
        } else if (query.contains("分类") || query.contains("类别")) {
            intent.setQueryType("分类统计");
        } else if (query.contains("来源") || query.contains("渠道")) {
            intent.setQueryType("来源统计");
        } else if (query.contains("趋势") || query.contains("走势") || query.contains("变化")) {
            intent.setQueryType("趋势分析");
        } else if (query.contains("总") || query.contains("合计") || query.contains("多少")) {
            intent.setQueryType("总额");
        } else {
            intent.setQueryType("总额"); // 默认查询总额
        }

        // 解析时间范围
        intent.setTimeRange(parseTimeRange(query));

        return intent;
    }

    /**
     * 解析时间范围
     */
    private String parseTimeRange(String query) {
        if (query.contains("本月")) {
            return "本月";
        } else if (query.contains("上月") || query.contains("上个月")) {
            return "上个月";
        } else if (query.contains("本周")) {
            return "本周";
        } else if (query.contains("上周") || query.contains("上个周")) {
            return "上周";
        } else if (query.contains("今年")) {
            return "今年";
        } else if (query.contains("去年")) {
            return "去年";
        } else if (query.contains("最近7天") || query.contains("近7天")) {
            return "最近7天";
        } else if (query.contains("最近30天") || query.contains("近30天")) {
            return "最近30天";
        } else {
            return "本月"; // 默认本月
        }
    }

    /**
     * 执行金刚到家查询
     */
    private String executeJingangQuery(JingangQueryIntent intent) {
        // 特殊处理表结构查询
        if (intent.getQueryType().equals("表结构")) {
            return jingangIncomeService.testTableStructure();
        }

        // 特殊处理全部收入查询
        if (intent.getQueryType().equals("全部收入")) {
            return jingangIncomeService.testAllIncomeRecords();
        }

        switch (intent.getQueryType()) {
            case "明细":
                return jingangIncomeService.getJingangIncomeDetails(intent.getTimeRange());
            case "分类统计":
                return jingangIncomeService.getJingangIncomeByCategory(intent.getTimeRange());
            case "总额":
            default:
                return jingangIncomeService.getJingangIncomeSummary(intent.getTimeRange());
        }
    }

    /**
     * 使用AI优化回复
     */
    private String enhanceResponseWithAI(String userQuery, String rawResult) {
        try {
            String prompt = String.format("""
                用户查询："%s"

                查询结果：
                %s

                请基于以上金刚到家软件收入数据，用更友好、专业的语言重新组织回答。要求：
                1. 保持数据的准确性，不要改变任何数字
                2. 突出关键收入指标
                3. 语言简洁明了，易于理解
                4. 如果有明细数据，重点突出重要信息
                5. 适当添加收入分析和趋势说明
                6. 保持原有的格式和结构
                7. 直接返回优化后的文本内容，不要返回JSON格式
                8. 不要包含任何JSON结构，只返回纯文本回答

                """, userQuery, rawResult);

            String enhancedResult = tongYiService.completion(prompt);

            // 如果AI增强失败，返回原始结果
            return enhancedResult != null && !enhancedResult.trim().isEmpty() ? enhancedResult : rawResult;

        } catch (Exception e) {
            log.warn("AI增强回复失败，返回原始结果: {}", e.getMessage());
            return rawResult;
        }
    }

    /**
     * 检查是否为金刚到家软件收入查询
     */
    public boolean isJingangQuery(String userQuery) {
        String query = userQuery.toLowerCase();
        return query.contains("金刚到家") || query.contains("金刚") ||
               (query.contains("软件") && query.contains("收入")) ||
               query.contains("表结构") || query.contains("测试表") ||
               query.contains("全部") || query.contains("所有") || query.contains("测试收入");
    }

    /**
     * 金刚到家查询意图类
     */
    public static class JingangQueryIntent {
        private String queryType;  // 总额、明细、分类统计、来源统计、趋势分析
        private String timeRange;  // 本月、上个月、本周、上周等

        // Getters and Setters
        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }

        public String getTimeRange() { return timeRange; }
        public void setTimeRange(String timeRange) { this.timeRange = timeRange; }

        @Override
        public String toString() {
            return String.format("JingangQueryIntent{queryType='%s', timeRange='%s'}",
                    queryType, timeRange);
        }
    }
}
