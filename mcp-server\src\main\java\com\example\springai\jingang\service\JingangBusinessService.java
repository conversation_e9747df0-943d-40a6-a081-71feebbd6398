package com.example.springai.jingang.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Map;

/**
 * 金刚到家业务智能查询服务
 * 专门处理金刚到家软件收入相关的自然语言查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class JingangBusinessService {

    @Autowired
    private JingangIncomeService jingangIncomeService;



    /**
     * 处理金刚到家软件收入查询
     */
    public String handleJingangQuery(String userQuery) {
        log.info("处理金刚到家软件收入查询: {}", userQuery);

        try {
            // 解析查询类型和时间范围
            JingangQueryIntent intent = parseJingangQueryIntent(userQuery);
            log.info("解析的金刚到家查询意图: {}", intent);

            // 执行相应的查询
            String result = executeJingangQuery(intent);

            // 直接返回查询结果，不使用AI增强
            return result;

        } catch (Exception e) {
            log.error("处理金刚到家软件收入查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 解析金刚到家查询意图
     */
    private JingangQueryIntent parseJingangQueryIntent(String userQuery) {
        JingangQueryIntent intent = new JingangQueryIntent();
        String query = userQuery.toLowerCase();

        // 解析查询类型
        if (query.contains("表结构") || query.contains("测试表")) {
            intent.setQueryType("表结构");
        } else if (query.contains("全部") || query.contains("所有") || query.contains("测试收入")) {
            intent.setQueryType("全部收入");
        } else if (query.contains("明细") || query.contains("详细") || query.contains("列表")) {
            intent.setQueryType("明细");
        } else if (query.contains("分类") || query.contains("类别")) {
            intent.setQueryType("分类统计");
        } else if (query.contains("来源") || query.contains("渠道")) {
            intent.setQueryType("来源统计");
        } else if (query.contains("趋势") || query.contains("走势") || query.contains("变化")) {
            intent.setQueryType("趋势分析");
        } else if (query.contains("统计") && !query.contains("分类")) {
            intent.setQueryType("统计");
        } else if (query.contains("总") || query.contains("合计") || query.contains("多少")) {
            intent.setQueryType("总额");
        } else {
            intent.setQueryType("统计"); // 默认使用新的统计查询
        }

        // 解析时间范围
        intent.setTimeRange(parseTimeRange(query));

        return intent;
    }

    /**
     * 解析时间范围
     */
    private String parseTimeRange(String query) {
        if (query.contains("本月")) {
            return "本月";
        } else if (query.contains("上月") || query.contains("上个月")) {
            return "上个月";
        } else if (query.contains("本周")) {
            return "本周";
        } else if (query.contains("上周") || query.contains("上个周")) {
            return "上周";
        } else if (query.contains("今年")) {
            return "今年";
        } else if (query.contains("去年")) {
            return "去年";
        } else if (query.contains("最近7天") || query.contains("近7天")) {
            return "最近7天";
        } else if (query.contains("最近30天") || query.contains("近30天")) {
            return "最近30天";
        } else {
            return "本月"; // 默认本月
        }
    }

    /**
     * 执行金刚到家查询
     */
    private String executeJingangQuery(JingangQueryIntent intent) {
        // 特殊处理表结构查询
        if (intent.getQueryType().equals("表结构")) {
            return jingangIncomeService.testTableStructure();
        }

        // 特殊处理全部收入查询
        if (intent.getQueryType().equals("全部收入")) {
            return jingangIncomeService.testAllIncomeRecords();
        }

        switch (intent.getQueryType()) {
            case "明细":
                return jingangIncomeService.getJingangIncomeDetails(intent.getTimeRange());
            case "分类统计":
                return jingangIncomeService.getJingangIncomeByCategory(intent.getTimeRange());
            case "统计":
                // 新增：使用新的统计查询方法
                return handleJingangStatisticsQuery(intent.getTimeRange());
            case "总额":
            default:
                return jingangIncomeService.getJingangIncomeSummary(intent.getTimeRange());
        }
    }



    /**
     * 检查是否为金刚到家软件收入查询
     */
    public boolean isJingangQuery(String userQuery) {
        String query = userQuery.toLowerCase();
        return query.contains("金刚到家") || query.contains("金刚") ||
               (query.contains("软件") && query.contains("收入")) ||
               query.contains("表结构") || query.contains("测试表") ||
               query.contains("全部") || query.contains("所有") || query.contains("测试收入");
    }

    /**
     * 金刚到家查询意图类
     */
    public static class JingangQueryIntent {
        private String queryType;  // 总额、明细、分类统计、来源统计、趋势分析
        private String timeRange;  // 本月、上个月、本周、上周等

        // Getters and Setters
        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }

        public String getTimeRange() { return timeRange; }
        public void setTimeRange(String timeRange) { this.timeRange = timeRange; }

        @Override
        public String toString() {
            return String.format("JingangQueryIntent{queryType='%s', timeRange='%s'}",
                    queryType, timeRange);
        }
    }

    /**
     * 处理金刚软件收入统计查询
     */
    private String handleJingangStatisticsQuery(String timeRange) {
        try {
            // 解析时间范围
            LocalDate[] dateRange = parseTimeRangeToDate(timeRange);
            LocalDate startDate = dateRange[0];
            LocalDate endDate = dateRange[1];

            // 调用新的统计方法
            Map<String, Object> statisticsData = jingangIncomeService.getJingangIncomeStatistics(startDate, endDate);

            // 生成友好的回复
            return generateJingangStatisticsResponse(statisticsData);

        } catch (Exception e) {
            log.error("处理金刚软件收入统计查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }

    /**
     * 解析时间范围为LocalDate数组
     */
    private LocalDate[] parseTimeRangeToDate(String timeRange) {
        LocalDate now = LocalDate.now();
        LocalDate startDate, endDate;

        switch (timeRange) {
            case "本月":
                startDate = now.withDayOfMonth(1);
                endDate = now.withDayOfMonth(now.lengthOfMonth());
                break;
            case "上个月":
                LocalDate lastMonth = now.minusMonths(1);
                startDate = lastMonth.withDayOfMonth(1);
                endDate = lastMonth.withDayOfMonth(lastMonth.lengthOfMonth());
                break;
            case "本周":
                startDate = now.minusDays(now.getDayOfWeek().getValue() - 1);
                endDate = now;
                break;
            case "上周":
                LocalDate lastWeek = now.minusWeeks(1);
                startDate = lastWeek.minusDays(lastWeek.getDayOfWeek().getValue() - 1);
                endDate = startDate.plusDays(6);
                break;
            default:
                // 默认本月
                startDate = now.withDayOfMonth(1);
                endDate = now;
                break;
        }

        return new LocalDate[]{startDate, endDate};
    }

    /**
     * 生成金刚软件收入统计的友好回复
     */
    private String generateJingangStatisticsResponse(Map<String, Object> statisticsData) {
        if (statisticsData.containsKey("error")) {
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }

        StringBuilder response = new StringBuilder();
        String queryType = (String) statisticsData.get("queryType");
        String period = (String) statisticsData.get("period");

        response.append("**").append(queryType).append("**\n\n");

        if (period != null) {
            response.append("查询时间：").append(period).append("\n");
        }

        // 获取基础数据
        Object totalAmount = statisticsData.get("totalAmount");
        Object totalCount = statisticsData.get("totalCount");
        Object avgAmount = statisticsData.get("avgAmount");

        response.append("**💰 金刚软件收入概况**\n");

        if (totalAmount instanceof BigDecimal) {
            BigDecimal total = (BigDecimal) totalAmount;
            response.append("• **总收入：").append(formatCurrency(total)).append("** 💰\n");
        }

        if (totalCount instanceof Number) {
            response.append("• **交易笔数：").append(totalCount).append("笔** 📊\n");
        }

        if (avgAmount instanceof BigDecimal) {
            response.append("• 平均交易金额：").append(formatCurrency((BigDecimal) avgAmount)).append(" 📈\n");
        }

        // 目标完成情况
        Object monthlyTarget = statisticsData.get("monthlyTarget");
        Object completionRate = statisticsData.get("completionRate");
        Object remainingTarget = statisticsData.get("remainingTarget");
        Object isTargetAchieved = statisticsData.get("isTargetAchieved");

        if (monthlyTarget != null && completionRate != null) {
            response.append("\n**🎯 目标完成情况**\n");
            response.append("• **月度目标：").append(formatCurrency((BigDecimal) monthlyTarget)).append("** 🎯\n");

            BigDecimal rate = (BigDecimal) completionRate;
            String rateStr = rate.setScale(2, RoundingMode.HALF_UP).toString() + "%";

            if (Boolean.TRUE.equals(isTargetAchieved)) {
                response.append("• **完成率：").append(rateStr).append("** ✅ 已达成目标！\n");
            } else {
                response.append("• **完成率：").append(rateStr).append("** 📈\n");
            }

            if (remainingTarget != null) {
                response.append("• **剩余目标：").append(formatCurrency((BigDecimal) remainingTarget)).append("** 🚀\n");
            }
        }

        return response.toString();
    }

    /**
     * 格式化货币显示
     */
    private String formatCurrency(BigDecimal amount) {
        if (amount.compareTo(BigDecimal.valueOf(10000)) >= 0) {
            BigDecimal wan = amount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP);
            return wan.toString() + "万元";
        } else {
            return amount.setScale(2, RoundingMode.HALF_UP).toString() + "元";
        }
    }
}
