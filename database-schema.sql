-- =====================================================
-- Spring AI Alibaba MCP 业务查询所需的数据库表结构
-- 数据库：SQL Server
-- =====================================================

-- 门店表
CREATE TABLE stores (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    store_code NVARCHAR(50) NOT NULL UNIQUE,  -- 门店编码
    store_name NVARCHAR(100) NOT NULL,        -- 门店名称
    address NVARCHAR(255),                    -- 门店地址
    manager NVARCHAR(50),                     -- 门店经理
    phone NVARCHAR(20),                       -- 门店电话
    status NVARCHAR(20) DEFAULT 'ACTIVE',    -- 门店状态：ACTIVE, CLOSED, MAINTENANCE
    store_type NVARCHAR(20) DEFAULT 'COOP',  -- 门店类型：COOP-共创店, DIRECT-直营店, FRANCHISE-加盟店
    created_at DATETIME2 DEFAULT GETDATE(),  -- 创建时间
    updated_at DATETIME2 DEFAULT GETDATE()   -- 更新时间
);

-- 销售记录表
CREATE TABLE sales_records (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    store_id BIGINT NOT NULL,                 -- 关联门店ID
    sale_date DATE NOT NULL,                  -- 销售日期
    amount DECIMAL(10,2) NOT NULL,            -- 销售金额
    order_count INT NOT NULL,                 -- 订单数量
    customer_count INT,                       -- 客户数量
    sale_type NVARCHAR(20),                   -- 销售类型：ONLINE, OFFLINE, MIXED
    remarks NVARCHAR(255),                    -- 备注
    created_at DATETIME2 DEFAULT GETDATE(),  -- 创建时间
    updated_at DATETIME2 DEFAULT GETDATE(),  -- 更新时间
    
    FOREIGN KEY (store_id) REFERENCES stores(id)
);

-- 平台收入表（StoreRevenue）
CREATE TABLE StoreRevenue (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    storeId BIGINT NOT NULL,                  -- 关联门店ID
    platformUsageFeeMoney DECIMAL(10,2) NOT NULL, -- 平台收入金额
    revenue_date DATE,                        -- 收入日期
    order_no NVARCHAR(100),                   -- 订单编号或交易编号
    revenue_type NVARCHAR(50),                -- 收入类型
    remarks NVARCHAR(255),                    -- 备注
    created_at DATETIME2 DEFAULT GETDATE(),  -- 创建时间
    updated_at DATETIME2 DEFAULT GETDATE(),  -- 更新时间

    FOREIGN KEY (storeId) REFERENCES stores(id)
);

-- 创建索引以提高查询性能
CREATE INDEX IX_sales_records_store_id ON sales_records(store_id);
CREATE INDEX IX_sales_records_sale_date ON sales_records(sale_date);
CREATE INDEX IX_sales_records_store_date ON sales_records(store_id, sale_date);

CREATE INDEX IX_store_revenue_store_id ON StoreRevenue(storeId);
CREATE INDEX IX_store_revenue_date ON StoreRevenue(revenue_date);
CREATE INDEX IX_store_revenue_store_date ON StoreRevenue(storeId, revenue_date);

-- =====================================================
-- 示例数据插入（可选）
-- =====================================================

-- 插入示例门店数据
INSERT INTO stores (store_code, store_name, address, manager, phone, status, store_type) VALUES
('ST001', '王府井旗舰店', '北京市东城区王府井大街138号', '张经理', '010-12345678', 'ACTIVE', 'COOP'),
('ST002', '三里屯时尚店', '北京市朝阳区三里屯路19号', '李经理', '010-23456789', 'ACTIVE', 'COOP'),
('ST003', '西单购物中心店', '北京市西城区西单北大街120号', '王经理', '010-34567890', 'ACTIVE', 'DIRECT'),
('ST004', '中关村科技店', '北京市海淀区中关村大街27号', '刘经理', '010-45678901', 'ACTIVE', 'COOP'),
('ST005', '国贸商务店', '北京市朝阳区建国门外大街1号', '陈经理', '010-56789012', 'ACTIVE', 'FRANCHISE');

-- 插入示例销售数据（最近一周）
DECLARE @StartDate DATE = DATEADD(DAY, -7, GETDATE());
DECLARE @EndDate DATE = GETDATE();
DECLARE @CurrentDate DATE = @StartDate;

WHILE @CurrentDate <= @EndDate
BEGIN
    -- 为每个门店每天插入1-3条销售记录
    INSERT INTO sales_records (store_id, sale_date, amount, order_count, customer_count, sale_type)
    SELECT 
        s.id,
        @CurrentDate,
        ROUND(RAND() * 10000 + 5000, 2),  -- 5000-15000的随机金额
        FLOOR(RAND() * 50 + 10),          -- 10-59的随机订单数
        FLOOR(RAND() * 40 + 8),           -- 8-47的随机客户数
        CASE FLOOR(RAND() * 3)
            WHEN 0 THEN 'ONLINE'
            WHEN 1 THEN 'OFFLINE'
            ELSE 'MIXED'
        END
    FROM stores s
    WHERE s.status = 'ACTIVE';
    
    SET @CurrentDate = DATEADD(DAY, 1, @CurrentDate);
END;

-- 插入示例平台收入数据（仅共创店）
SET @StartDate = DATEADD(DAY, -30, GETDATE());
SET @EndDate = GETDATE();
SET @CurrentDate = @StartDate;

WHILE @CurrentDate <= @EndDate
BEGIN
    -- 为共创店每天插入1-2条平台收入记录
    INSERT INTO StoreRevenue (storeId, platformUsageFeeMoney, revenue_date, order_no, revenue_type)
    SELECT
        s.id,
        ROUND(RAND() * 500 + 100, 2),  -- 100-600的随机平台收入
        @CurrentDate,
        'ORD' + FORMAT(@CurrentDate, 'yyyyMMdd') + RIGHT('000' + CAST(s.id AS VARCHAR), 3) + RIGHT('00' + CAST(FLOOR(RAND() * 99) + 1 AS VARCHAR), 2),
        CASE FLOOR(RAND() * 3)
            WHEN 0 THEN '交易佣金'
            WHEN 1 THEN '服务费'
            ELSE '平台使用费'
        END
    FROM stores s
    WHERE s.store_type = 'COOP' AND s.status = 'ACTIVE';

    SET @CurrentDate = DATEADD(DAY, 1, @CurrentDate);
END;

-- =====================================================
-- 使用说明
-- =====================================================

/*
1. 如果您已有现有的门店和销售数据表，请：
   - 修改 mcp-server/src/main/java/com/example/springai/entity/Store.java 中的 @Table(name = "your_store_table")
   - 修改 mcp-server/src/main/java/com/example/springai/entity/SalesRecord.java 中的 @Table(name = "your_sales_table")
   - 调整实体类中的字段名和数据类型以匹配您的表结构

2. 如果您的表结构不同，请：
   - 修改实体类中的 @Column 注解以匹配您的列名
   - 调整数据类型和约束
   - 更新 Repository 中的查询方法

3. 业务查询功能支持的查询类型：
   - 总业绩查询："上周的门店总业绩多少"
   - 门店排名："本月销售排名前5的门店"
   - 特定门店业绩："王府井旗舰店上周的销售业绩"
   - 销售概览："最近30天的销售概览"
   - 平台收入总额："本月平台收入总共多少"
   - 平台收入明细："列一个本月平台收入的明细给我"
   - 门店平台收入："王府井旗舰店上个月的平台收入"
   - 平台收入排名："本月平台收入排名前5的共创店"

4. 时间范围支持：
   - 上周、本周、上个月、本月
   - 昨天、今天
   - 最近N天（如"最近7天"、"最近30天"）
*/
