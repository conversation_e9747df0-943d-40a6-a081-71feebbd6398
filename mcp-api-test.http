### Spring AI Alibaba MCP API 测试文件
### 使用 IntelliJ IDEA 或 VS Code 的 REST Client 插件运行

### ========== MCP客户端API测试 (端口8081) ==========

### 1. MCP客户端健康检查
GET http://localhost:8081/mcp/health
Accept: application/json

###

### 2. 连接到MCP服务器
POST http://localhost:8081/mcp/connect
Accept: application/json

###

### 3. 连接到MCP服务器（指定URL）
POST http://localhost:8081/mcp/connect?serverUrl=ws://localhost:8080/mcp
Accept: application/json

###

### 4. 获取MCP工具列表
GET http://localhost:8081/mcp/tools
Accept: application/json

###

### 5. 调用AI对话工具
POST http://localhost:8081/mcp/tools/chat?message=你好，请介绍一下MCP协议
Accept: application/json

###

### 6. 调用AI对话工具 - 技术问题
POST http://localhost:8081/mcp/tools/chat?message=什么是Model Context Protocol？
Accept: application/json

###

### 7. 调用AI流式对话工具
POST http://localhost:8081/mcp/tools/stream-chat?message=请详细解释Spring AI的架构设计
Accept: application/json

###

### 8. 调用AI图像生成工具
POST http://localhost:8081/mcp/tools/image?prompt=一只可爱的小猫在花园里玩耍
Accept: application/json

###

### 9. 调用AI图像生成工具 - 自定义尺寸
POST http://localhost:8081/mcp/tools/image?prompt=未来科技城市夜景&width=1280&height=720
Accept: application/json

###

### 10. 调用业务查询工具 - 总业绩查询
POST http://localhost:8081/mcp/tools/business-query?query=上周的门店总业绩多少
Accept: application/json

###

### 11. 调用业务查询工具 - 门店排名
POST http://localhost:8081/mcp/tools/business-query?query=本月销售排名前5的门店
Accept: application/json

###

### 12. 调用业务查询工具 - 特定门店业绩
POST http://localhost:8081/mcp/tools/business-query?query=王府井旗舰店上周的销售业绩
Accept: application/json

###

### 13. 调用业务查询工具 - 销售概览
POST http://localhost:8081/mcp/tools/business-query?query=最近30天的销售概览
Accept: application/json

###

### 14. 调用业务查询工具 - 平台收入总额（共创店：storeType=2，加盟店）
POST http://localhost:8081/mcp/tools/business-query?query=本月平台收入总共多少
Accept: application/json

###

### 15. 调用业务查询工具 - 平台收入明细（共创店：storeType=2，加盟店）
POST http://localhost:8081/mcp/tools/business-query?query=列一个本月平台收入的明细给我
Accept: application/json

###

### 16. 调用业务查询工具 - 上个月平台收入（共创店：storeType=2，加盟店）
POST http://localhost:8081/mcp/tools/business-query?query=上个月的平台收入总共有多少
Accept: application/json

###

### 17. 调用业务查询工具 - 门店平台收入（共创店：storeType=2，加盟店）
POST http://localhost:8081/mcp/tools/business-query?query=王府井旗舰店上个月的平台收入
Accept: application/json

###

### 18. 调用业务查询工具 - 平台收入排名（共创店：storeType=2，加盟店）
POST http://localhost:8081/mcp/tools/business-query?query=本月平台收入排名前5的共创店
Accept: application/json

###

### 19. 获取MCP资源列表
GET http://localhost:8081/mcp/resources
Accept: application/json

###

### 15. 读取AI模型信息资源
GET http://localhost:8081/mcp/resources/read?uri=ai://model/info
Accept: application/json

###

### 16. 读取对话历史资源
GET http://localhost:8081/mcp/resources/read?uri=ai://chat/history
Accept: application/json

###

### 17. 读取系统状态资源
GET http://localhost:8081/mcp/resources/read?uri=ai://system/status
Accept: application/json

###

### ========== MCP服务器传统REST API测试 (端口8080) ==========

### 18. 服务器健康检查
GET http://localhost:8080/ai/health
Accept: application/json

###

### 19. 服务器文本对话
GET http://localhost:8080/ai/chat?message=你好，请介绍一下Spring AI
Accept: application/json

###

### 20. 服务器流式对话
GET http://localhost:8080/ai/chat/stream?message=请详细介绍Spring Boot的特性
Accept: application/json

###

### 21. 服务器图像生成
GET http://localhost:8080/ai/image?prompt=一只可爱的小猫在花园里玩耍
Accept: application/json

###

### 22. 服务器语音合成
GET http://localhost:8080/ai/speech?text=欢迎使用Spring AI Alibaba MCP
Accept: application/json

###

### ========== 数据库信息 ==========

### 23. 数据库连接信息
# 数据库类型: SQL Server
# 服务器: rm-wz9yqo4g4u00p43c3.sqlserver.rds.aliyuncs.com
# 数据库: xyjdata
# 用户名: xyj20221
# 注意：请确保数据库中有相应的门店和销售数据表

### 24. 门店类型说明
# storeType字段（数字类型）：
# 0 - 平台
# 1 - 自营
# 2 - 加盟（共创店）← 平台收入查询只针对此类型
# 3 - 定制
# 4 - 合伙
# 5 - 承包

###
