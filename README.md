# Spring AI Alibaba MCP 多模块项目

这是一个基于 Spring Boot 3.x 和 Spring Cloud Alibaba AI 的完整示例项目，采用 **Model Context Protocol (MCP)** 架构设计，展示了如何集成阿里云通义千问大模型，实现标准化的AI工具和资源服务。

## 🏗️ 项目架构

项目采用多模块设计，实现了完整的MCP client-server架构：

```
javaMcp/
├── mcp-common/          # 共享模块 - MCP协议定义
├── mcp-server/          # MCP服务器 - 提供AI工具和资源
├── mcp-client/          # MCP客户端 - 消费AI工具和资源
├── pom.xml             # 父级POM管理
└── start-*.bat         # 启动脚本
```

## 🚀 项目特性

### 核心AI功能
- **文本对话**：基于通义千问的智能对话功能
- **流式对话**：支持流式响应的实时对话
- **图像生成**：文本到图像的AI绘画功能
- **语音合成**：文本到语音的转换功能（当前版本暂不支持）

### MCP架构特性
- **模块化设计**：独立的client和server模块，职责分离
- **MCP服务器** (端口8080)：提供标准化的AI工具和资源
- **MCP客户端** (端口8081)：消费AI工具的标准化客户端
- **共享协议**：统一的MCP协议定义模块
- **JSON-RPC通信**：基于WebSocket的标准协议通信
- **工具系统**：标准化的AI工具调用接口
- **资源系统**：标准化的AI资源访问接口

### 技术特性
- **双重API**：同时支持传统REST API和MCP协议
- **WebSocket通信**：实时双向通信支持
- **错误处理**：完善的异常处理机制
- **日志记录**：详细的操作日志

## 📋 技术栈

- **Java 17**
- **Spring Boot 3.2.0**
- **Spring Cloud Alibaba 2023.0.1.0**
- **Spring AI**
- **Spring WebSocket** - WebSocket通信支持
- **JSON-RPC** - MCP协议通信
- **Jackson** - JSON序列化/反序列化
- **Maven**
- **Lombok**

## 🛠️ 快速开始

### 1. 环境要求

- JDK 17 或更高版本
- Maven 3.6 或更高版本
- 阿里云通义千问 API Key

### 2. 获取 API Key

1. 访问 [阿里云模型服务灵积](https://dashscope.console.aliyun.com/model)
2. 注册并登录账号
3. 创建 API Key
4. 复制 API Key 备用

### 3. 配置项目

编辑 `mcp-server/src/main/resources/application.yml` 文件，替换 API Key：

```yaml
spring:
  cloud:
    ai:
      tongyi:
        api-key: your-actual-api-key-here
```

或者通过环境变量设置：

```bash
export TONGYI_API_KEY=your-actual-api-key-here
```

### 4. 运行项目

**方式一：启动完整系统**
```bash
# 启动服务器和客户端
start-all.bat
```

**方式二：分别启动**
```bash
# 先启动MCP服务器 (端口8080)
start-server.bat

# 再启动MCP客户端 (端口8081)
start-client.bat
```

### 5. 访问应用

- **MCP服务器**: http://localhost:8080
- **MCP客户端**: http://localhost:8081

## 📚 API 接口

### MCP协议接口

#### MCP服务器端点
- **WebSocket端点**: `ws://localhost:8080/mcp`

#### MCP客户端REST API (端口8081)

**连接管理**
```http
POST http://localhost:8081/mcp/connect                    # 连接到MCP服务器
GET http://localhost:8081/mcp/health                      # MCP健康检查
```

**工具调用**
```http
GET http://localhost:8081/mcp/tools                       # 获取可用工具列表
POST http://localhost:8081/mcp/tools/chat?message=你好     # AI文本对话
POST http://localhost:8081/mcp/tools/stream-chat?message=介绍Spring  # AI流式对话
POST http://localhost:8081/mcp/tools/image?prompt=可爱的猫  # AI图像生成
```

**资源访问**
```http
GET http://localhost:8081/mcp/resources                   # 获取可用资源列表
GET http://localhost:8081/mcp/resources/read?uri=ai://model/info     # 读取AI模型信息
GET http://localhost:8081/mcp/resources/read?uri=ai://system/status  # 读取系统状态
```

### MCP服务器传统REST API (端口8080)

**基础功能**
```http
GET http://localhost:8080/ai/chat?message=你好，请介绍一下Spring AI
GET http://localhost:8080/ai/chat/stream?message=请详细介绍Spring Boot的特性
GET http://localhost:8080/ai/image?prompt=一只可爱的小猫在花园里玩耍
GET http://localhost:8080/ai/speech?text=欢迎使用Spring AI Alibaba
GET http://localhost:8080/ai/health
```

## 🔧 配置说明

### 模型配置

在 `application.yml` 中可以配置不同的模型参数：

```yaml
spring:
  cloud:
    ai:
      tongyi:
        chat:
          options:
            model: qwen-turbo  # 可选：qwen-turbo, qwen-plus, qwen-max
            temperature: 0.7   # 随机性：0.0-2.0
            max-tokens: 1000   # 最大生成长度
            top-p: 0.8        # 核采样参数
```

### 日志配置

项目配置了详细的日志记录，可以在控制台查看请求处理过程。

## 📁 项目结构

```
javaMcp/
├── pom.xml                              # 父级POM管理
├── mcp-common/                          # 共享模块
│   ├── pom.xml
│   └── src/main/java/com/example/springai/mcp/protocol/
│       ├── McpMessage.java              # MCP消息定义
│       ├── McpError.java                # MCP错误定义
│       ├── McpTool.java                 # MCP工具定义
│       └── McpResource.java             # MCP资源定义
├── mcp-server/                          # MCP服务器模块
│   ├── pom.xml
│   └── src/main/java/com/example/springai/
│       ├── McpServerApplication.java    # 服务器启动类
│       ├── controller/
│       │   └── TongYiController.java    # 传统REST控制器
│       ├── service/                     # AI服务层
│       └── mcp/
│           ├── server/McpServer.java    # MCP服务器实现
│           └── config/                  # WebSocket配置
└── mcp-client/                          # MCP客户端模块
    ├── pom.xml
    └── src/main/java/com/example/springai/
        ├── McpClientApplication.java    # 客户端启动类
        └── mcp/
            ├── client/McpClient.java    # MCP客户端实现
            └── controller/McpController.java # MCP REST API
```

## 🚨 注意事项

1. **API Key 安全**：请勿将 API Key 提交到版本控制系统
2. **费用控制**：使用 API 会产生费用，请注意控制调用频率
3. **网络环境**：确保网络可以访问阿里云服务
4. **Java版本**：项目需要 Java 17 或更高版本

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**Happy Coding! 🎉**
