#!/bin/bash

echo "=========================================="
echo "    MCP项目打包脚本"
echo "=========================================="

echo ""
echo "1. 清理项目..."
./mvnw clean

echo ""
echo "2. 编译项目..."
./mvnw compile

echo ""
echo "3. 运行测试..."
./mvnw test -DskipTests=true

echo ""
echo "4. 打包MCP服务器..."
./mvnw package -pl mcp-server -DskipTests=true

echo ""
echo "5. 打包MCP客户端..."
./mvnw package -pl mcp-client -DskipTests=true

echo ""
echo "6. 创建部署目录..."
mkdir -p deploy/server
mkdir -p deploy/client
mkdir -p deploy/logs
mkdir -p deploy/view

echo ""
echo "7. 复制JAR文件..."
cp mcp-server/target/mcp-server-1.0.0.jar deploy/server/
cp mcp-client/target/mcp-client-1.0.0.jar deploy/client/

echo ""
echo "8. 复制HTML页面..."
cp view/*.html deploy/view/

echo ""
echo "9. 创建启动脚本..."
cat > deploy/start-server.sh << 'EOF'
#!/bin/bash
echo "启动MCP服务器..."
java -jar -Xms512m -Xmx1024m -Dspring.profiles.active=prod server/mcp-server-1.0.0.jar
EOF

cat > deploy/start-client.sh << 'EOF'
#!/bin/bash
echo "启动MCP客户端..."
java -jar -Xms256m -Xmx512m -Dspring.profiles.active=prod client/mcp-client-1.0.0.jar
EOF

cat > deploy/start-all.sh << 'EOF'
#!/bin/bash
echo "启动MCP完整服务..."

# 启动服务器
echo "启动MCP服务器..."
nohup java -jar -Xms512m -Xmx1024m -Dspring.profiles.active=prod server/mcp-server-1.0.0.jar > logs/server.log 2>&1 &
SERVER_PID=$!
echo "MCP服务器已启动，PID: $SERVER_PID"

# 等待服务器启动
echo "等待服务器启动..."
sleep 10

# 启动客户端
echo "启动MCP客户端..."
nohup java -jar -Xms256m -Xmx512m -Dspring.profiles.active=prod client/mcp-client-1.0.0.jar > logs/client.log 2>&1 &
CLIENT_PID=$!
echo "MCP客户端已启动，PID: $CLIENT_PID"

# 保存PID
echo $SERVER_PID > logs/server.pid
echo $CLIENT_PID > logs/client.pid

echo ""
echo "服务启动完成！"
echo "MCP服务器: http://localhost:8080"
echo "MCP客户端: http://localhost:8081"
echo "AI智能问数页面: 请打开 view/ai-query.html"
echo ""
echo "查看日志:"
echo "  服务器日志: tail -f logs/server.log"
echo "  客户端日志: tail -f logs/client.log"
EOF

cat > deploy/stop-all.sh << 'EOF'
#!/bin/bash
echo "停止MCP服务..."

# 停止服务器
if [ -f logs/server.pid ]; then
    SERVER_PID=$(cat logs/server.pid)
    if kill -0 $SERVER_PID 2>/dev/null; then
        kill $SERVER_PID
        echo "MCP服务器已停止 (PID: $SERVER_PID)"
    fi
    rm -f logs/server.pid
fi

# 停止客户端
if [ -f logs/client.pid ]; then
    CLIENT_PID=$(cat logs/client.pid)
    if kill -0 $CLIENT_PID 2>/dev/null; then
        kill $CLIENT_PID
        echo "MCP客户端已停止 (PID: $CLIENT_PID)"
    fi
    rm -f logs/client.pid
fi

echo "服务已停止！"
EOF

echo ""
echo "10. 设置脚本权限..."
chmod +x deploy/start-server.sh
chmod +x deploy/start-client.sh
chmod +x deploy/start-all.sh
chmod +x deploy/stop-all.sh

echo ""
echo "=========================================="
echo "    打包完成！"
echo "=========================================="
echo ""
echo "部署文件位置: deploy/"
echo ""
echo "包含文件:"
echo "  - server/mcp-server-1.0.0.jar     (MCP服务器)"
echo "  - client/mcp-client-1.0.0.jar     (MCP客户端)"
echo "  - view/ai-query.html               (AI智能问数页面)"
echo "  - start-server.sh                  (启动服务器)"
echo "  - start-client.sh                  (启动客户端)"
echo "  - start-all.sh                     (启动全部服务)"
echo "  - stop-all.sh                      (停止全部服务)"
echo "  - logs/                            (日志目录)"
echo ""
echo "部署说明:"
echo "  1. 将 deploy 文件夹复制到服务器"
echo "  2. 确保服务器安装了 Java 17+"
echo "  3. 运行 ./start-all.sh 启动服务"
echo "  4. 访问 http://服务器IP:8081 使用MCP客户端"
echo "  5. 打开 view/ai-query.html 使用AI智能问数"
echo ""
