package com.example.springai.service;

import com.example.springai.service.impl.TongYiServiceImpl;
import com.example.springai.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.regex.Pattern;

/**
 * 全国三嫂业绩智能查询服务
 * 专门处理全国三嫂业绩相关的自然语言查询
 */
@Slf4j
@Service
public class SansaoBusinessService {
    
    @Autowired
    private SansaoPerformanceService sansaoPerformanceService;
    
    @Autowired
    private TongYiServiceImpl tongYiService;
    
    /**
     * 处理全国三嫂业绩查询
     */
    public String handleSansaoQuery(String userQuery) {
        log.info("处理全国三嫂业绩查询: {}", userQuery);
        
        try {
            // 解析查询类型和时间范围
            SansaoQueryIntent intent = parseSansaoQueryIntent(userQuery);
            log.info("解析的三嫂查询意图: {}", intent);
            
            // 执行相应的查询
            String result = executeSansaoQuery(intent);
            
            // 使用AI优化回复
            return enhanceResponseWithAI(userQuery, result);
            
        } catch (Exception e) {
            log.error("处理全国三嫂业绩查询失败", e);
            return "当前问题我还没学会，请给我点时间，我正在学习中哦~";
        }
    }
    
    /**
     * 解析全国三嫂查询意图
     */
    private SansaoQueryIntent parseSansaoQueryIntent(String userQuery) {
        SansaoQueryIntent intent = new SansaoQueryIntent();
        String query = userQuery.toLowerCase();
        
        // 解析查询类型
        if (query.contains("排行") || query.contains("排名")) {
            intent.setQueryType("排行");
            // 提取排名数量
            Pattern pattern = Pattern.compile("前(\\d+)");
            java.util.regex.Matcher matcher = pattern.matcher(query);
            if (matcher.find()) {
                intent.setTopN(Integer.parseInt(matcher.group(1)));
            } else {
                intent.setTopN(10); // 默认前10名
            }
        } else if (query.contains("产品") || query.contains("商品")) {
            intent.setQueryType("产品分析");
        } else if (query.contains("趋势") || query.contains("走势") || query.contains("变化")) {
            intent.setQueryType("趋势分析");
        } else if (query.contains("总") || query.contains("合计") || query.contains("多少")) {
            intent.setQueryType("总额");
        } else {
            intent.setQueryType("总额"); // 默认查询总额
        }
        
        // 解析时间范围
        intent.setTimeRange(parseTimeRange(query));
        
        return intent;
    }
    
    /**
     * 解析时间范围
     */
    private String parseTimeRange(String query) {
        if (query.contains("本月")) {
            return "本月";
        } else if (query.contains("上月") || query.contains("上个月")) {
            return "上个月";
        } else if (query.contains("本周")) {
            return "本周";
        } else if (query.contains("上周") || query.contains("上个周")) {
            return "上周";
        } else if (query.contains("今年")) {
            return "今年";
        } else if (query.contains("去年")) {
            return "去年";
        } else if (query.contains("最近7天") || query.contains("近7天")) {
            return "最近7天";
        } else if (query.contains("最近30天") || query.contains("近30天")) {
            return "最近30天";
        } else {
            return "本月"; // 默认本月
        }
    }
    
    /**
     * 执行全国三嫂查询
     */
    private String executeSansaoQuery(SansaoQueryIntent intent) {
        switch (intent.getQueryType()) {
            case "排行":
                return sansaoPerformanceService.getSansaoPerformanceRanking(intent.getTimeRange(), intent.getTopN());
            case "产品分析":
                return sansaoPerformanceService.getSansaoPerformanceByProduct(intent.getTimeRange());
            case "趋势分析":
                return sansaoPerformanceService.getSansaoPerformanceTrend(intent.getTimeRange());
            case "总额":
            default:
                return sansaoPerformanceService.getSansaoPerformanceSummary(intent.getTimeRange());
        }
    }
    
    /**
     * 使用AI优化回复
     */
    private String enhanceResponseWithAI(String userQuery, String rawResult) {
        try {
            String prompt = String.format("""
                用户查询："%s"
                
                查询结果：
                %s
                
                请基于以上全国三嫂业绩数据，用更友好、专业的语言重新组织回答。要求：
                1. 保持数据的准确性
                2. 突出关键业绩指标
                3. 语言简洁明了，易于理解
                4. 如果有排名数据，重点突出前几名
                5. 适当添加业绩分析和趋势说明
                6. 保持原有的格式和结构
                
                """, userQuery, rawResult);
            
            String enhancedResult = tongYiService.completion(prompt);
            
            // 如果AI增强失败，返回原始结果
            return enhancedResult != null && !enhancedResult.trim().isEmpty() ? enhancedResult : rawResult;
            
        } catch (Exception e) {
            log.warn("AI增强回复失败，返回原始结果: {}", e.getMessage());
            return rawResult;
        }
    }
    
    /**
     * 检查是否为全国三嫂业绩查询
     */
    public boolean isSansaoQuery(String userQuery) {
        String query = userQuery.toLowerCase();

        // 明确包含三嫂关键词
        boolean hasSansaoKeyword = query.contains("三嫂") || query.contains("三姐妹");

        // 或者包含全国+三嫂相关的业绩查询（但不能包含"交付"关键词）
        boolean isNationalSansaoQuery = query.contains("全国") &&
                                       (query.contains("业绩") || query.contains("销售")) &&
                                       !query.contains("交付") &&
                                       !query.contains("新品");

        boolean result = hasSansaoKeyword || isNationalSansaoQuery;

        log.debug("三嫂查询识别 - 查询: '{}', 三嫂关键词: {}, 全国三嫂查询: {}, 结果: {}",
                 userQuery, hasSansaoKeyword, isNationalSansaoQuery, result);

        return result;
    }
    
    /**
     * 全国三嫂查询意图类
     */
    public static class SansaoQueryIntent {
        private String queryType;  // 总额、排行、产品分析、趋势分析
        private String timeRange;  // 本月、上个月、本周、上周等
        private Integer topN;      // 排行榜数量
        
        // Getters and Setters
        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }
        
        public String getTimeRange() { return timeRange; }
        public void setTimeRange(String timeRange) { this.timeRange = timeRange; }
        
        public Integer getTopN() { return topN; }
        public void setTopN(Integer topN) { this.topN = topN; }
        
        @Override
        public String toString() {
            return String.format("SansaoQueryIntent{queryType='%s', timeRange='%s', topN=%d}",
                    queryType, timeRange, topN);
        }
    }
}
