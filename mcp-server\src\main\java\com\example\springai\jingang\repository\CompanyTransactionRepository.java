package com.example.springai.jingang.repository;

import com.example.springai.jingang.entity.CompanyTransaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 金刚到家公司交易记录数据访问接口
 * 
 * <AUTHOR>
 */
@Repository
@Transactional("jingangTransactionManager")
public interface CompanyTransactionRepository extends JpaRepository<CompanyTransaction, Long> {

    /**
     * 查询指定时间范围内的软件续费收入总额
     */
    @Query(value = "SELECT COALESCE(SUM(amount), 0) " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    BigDecimal calculateIncomeTotal(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内的软件续费收入记录数量
     */
    @Query(value = "SELECT COUNT(*) " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    Long countIncomeRecords(@Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内的软件续费收入汇总信息
     */
    @Query(value = "SELECT COALESCE(SUM(amount), 0), COUNT(*) " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime", nativeQuery = true)
    Object[] findIncomeSummary(@Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内的软件续费收入明细列表
     */
    @Query(value = "SELECT transaction_time as 交易时间, amount as 交易金额, description as 交易描述, remark as 交易备注 " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime " +
           "ORDER BY transaction_time DESC, amount DESC", nativeQuery = true)
    List<Object[]> findIncomeDetails(@Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内按分类统计的软件续费收入
     */
    @Query(value = "SELECT business_type, COALESCE(SUM(amount), 0), COUNT(*) " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime " +
           "GROUP BY business_type " +
           "ORDER BY SUM(amount) DESC", nativeQuery = true)
    List<Object[]> findIncomeByCategory(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内按来源统计的软件续费收入
     */
    @Query(value = "SELECT pay_type, COALESCE(SUM(amount), 0), COUNT(*) " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime " +
           "GROUP BY pay_type " +
           "ORDER BY SUM(amount) DESC", nativeQuery = true)
    List<Object[]> findIncomeBySource(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内按日期统计的软件续费收入（用于趋势分析）
     */
    @Query(value = "SELECT DATE(transaction_time), COALESCE(SUM(amount), 0), COUNT(*) " +
           "FROM company_transaction " +
           "WHERE transaction_status = 'SUCCESS' " +
           "AND business_type = 'SOFTWARE_RENEWAL' " +
           "AND transaction_time BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(transaction_time) " +
           "ORDER BY DATE(transaction_time) DESC", nativeQuery = true)
    List<Object[]> findIncomeByDate(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 测试查询 - 获取表结构信息
     */
    @Query(value = "SHOW COLUMNS FROM company_transaction", nativeQuery = true)
    List<Object[]> showTableColumns();

    /**
     * 测试查询 - 获取前几条记录查看字段
     */
    @Query(value = "SELECT * FROM company_transaction LIMIT 5", nativeQuery = true)
    List<Object[]> findSampleRecords();

    /**
     * 测试查询 - 获取所有软件续费收入记录
     */
    @Query(value = "SELECT transaction_time, amount, business_type, description, remark FROM company_transaction WHERE transaction_status = 'SUCCESS' AND business_type = 'SOFTWARE_RENEWAL' ORDER BY transaction_time DESC", nativeQuery = true)
    List<Object[]> findAllIncomeRecords();

    /**
     * 测试查询 - 不限时间的软件续费收入汇总
     */
    @Query(value = "SELECT COALESCE(SUM(amount), 0), COUNT(*) FROM company_transaction WHERE transaction_status = 'SUCCESS' AND business_type = 'SOFTWARE_RENEWAL'", nativeQuery = true)
    Object[] findAllIncomeSummary();
}
