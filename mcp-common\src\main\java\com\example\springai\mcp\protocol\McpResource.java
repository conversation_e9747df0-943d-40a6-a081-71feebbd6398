package com.example.springai.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * MCP资源定义
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpResource {
    
    /**
     * 资源URI
     */
    @JsonProperty("uri")
    private String uri;
    
    /**
     * 资源名称
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 资源描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 资源MIME类型
     */
    @JsonProperty("mimeType")
    private String mimeType;
    
    /**
     * 创建AI模型信息资源
     */
    public static McpResource createModelInfoResource() {
        return McpResource.builder()
                .uri("ai://model/info")
                .name("AI模型信息")
                .description("获取当前AI模型的详细信息")
                .mimeType("application/json")
                .build();
    }
    
    /**
     * 创建对话历史资源
     */
    public static McpResource createChatHistoryResource() {
        return McpResource.builder()
                .uri("ai://chat/history")
                .name("对话历史")
                .description("获取最近的对话历史记录")
                .mimeType("application/json")
                .build();
    }
    
    /**
     * 创建系统状态资源
     */
    public static McpResource createSystemStatusResource() {
        return McpResource.builder()
                .uri("ai://system/status")
                .name("系统状态")
                .description("获取AI系统的运行状态信息")
                .mimeType("application/json")
                .build();
    }
}
