# 交付业绩查询功能说明

## 🎯 功能概述

新增交付业绩数据查询服务，基于复杂的CTE（Common Table Expression）查询，支持自然语言查询交付业绩统计，包含复购分析和目标管理功能。

## 📊 数据源说明

### SQL基础查询（复杂CTE查询）
```sql
WITH o AS (
    SELECT
        id, billno, BuChannel, memberid, ProductName, RealTotalAmount 
    FROM [Order] 
    WHERE OrderState <> 99 
      AND PaySettlementTime BETWEEN '2025/7/1' AND '2025/7/10 23:59' 
      AND IsGroup = 0 
),
os AS (
    SELECT
        id, billno, BuChannel, memberid, ProductName, RealTotalAmount,
        ROW_NUMBER() OVER (partition BY MemberId ORDER BY id) AS num 
    FROM [Order] 
    WHERE OrderState <> 99 
      AND PaySettlementTime BETWEEN '2025/7/1' AND '2025/7/10 23:59' 
      AND IsGroup = 0 
),
m AS (
    SELECT DISTINCT(memberid) AS mid 
    FROM [Order] 
    WHERE OrderState <> 99 
      AND PaySettlementTime BETWEEN '2025/7/1' AND '2025/7/10 23:59' 
      AND IsGroup = 0
)
SELECT
    COUNT(1) 总订单数,
    SUM(RealTotalAmount) 总业绩,
    COUNT(DISTINCT(o.memberid)) AS 下单人数,
    SUM(复购业绩) AS 复购业绩,
    COUNT(复购人数) AS 复购人数,
    CASE WHEN 下单人数 > 0 THEN 
        ROUND(CAST(复购人数 AS FLOAT) / 下单人数, 2) 
    ELSE 0 END AS 复购率
FROM o, os, m
```

### 查询条件
- **OrderState <> 99** - 排除无效订单
- **PaySettlementTime** - 基于支付结算时间范围
- **IsGroup = 0** - 排除团购订单
- **复购分析** - 使用ROW_NUMBER()窗口函数分析用户购买次序

## 🎯 目标管理功能

### 月度目标设置
- **固定月度目标：480万元**
- **自动计算完成率**
- **剩余目标金额计算**
- **目标达成状态判断**

### 目标计算逻辑
```java
// 月度目标
MONTHLY_TARGET = 4,800,000元

// 完成率计算
completionRate = (实际业绩 / 目标金额) × 100%

// 剩余目标
remainingTarget = 目标金额 - 实际业绩

// 比例目标（非整月查询）
proportionalTarget = 月度目标 × (查询天数 / 当月总天数)
```

## 🔍 支持的查询类型

### 1. 基础统计查询
**触发关键词：** "交付"（排除"全国交付"） + "业绩"、"收入"、"统计"

**查询示例：**
- "本月交付业绩"
- "上周交付业绩统计"
- "交付业绩情况"
- "交付业绩目标完成率"

**返回内容：**
- 总业绩和总订单数
- 下单人数统计
- 复购分析（复购业绩、复购人数、复购率）
- 新客分析（新客业绩、新客人数）
- 目标完成率

### 2. 产品明细查询
**触发关键词：** "交付" + "产品"、"明细"

**查询示例：**
- "交付业绩产品明细"
- "交付业绩产品统计"

**返回内容：**
- 产品业绩排行
- 各产品订单数和用户数
- 产品业绩占比

### 3. 时间范围支持
- **今天、昨天**
- **本周、上周**
- **本月、上月**
- **近7天、近30天**
- **自定义时间范围**

## 📋 返回数据格式

### 统计查询返回示例
```
**交付业绩统计**

查询时间：30天

**📊 交付业绩概况**
• **总业绩：120.50万元** 💰
• **总订单数：1,856单** 📦
• **下单人数：1,234人** 👥

**🔄 复购分析**
• **复购业绩：45.20万元** 🔄
• **复购人数：356人** 👥
• **复购率：28.85%** 📈
• **新客业绩：75.30万元** ✨
• **新客人数：878人** 🆕

**🎯 目标完成情况**
• **月度总目标：480.00万元** 🎯
• **期间目标：160.00万元** 📅
• 查询时间：10天 / 30天
• **期间完成率：75.31%** 📈
• **期间剩余：39.50万元** 🚀
• **月度完成率：25.10%** 📊
• **月度剩余：359.50万元** 🎯

• 平均订单金额：649.46元 📊
• 客单价：976.58元 💎
```

### 产品明细查询返回示例
```
**交付业绩产品明细**

查询时间：30天

**📊 交付业绩概况**
• **总业绩：120.50万元** 💰
• **总订单数：1,856单** 📦

**📋 产品明细**
🥇 **家政服务套餐A**：456单，35.60万元，298人
🥈 **清洁服务套餐B**：389单，28.90万元，245人
🥉 **保洁服务套餐C**：312单，22.40万元，198人
🏅 **维修服务套餐D**：278单，18.70万元，167人
🏅 **其他服务**：421单，14.90万元，326人
... 还有3个产品未显示
```

## 🔧 技术架构

### 1. 服务层结构
- **DeliveryPerformanceRepository** - 数据访问层
- **DeliveryPerformanceService** - 业务逻辑层
- **BusinessAIService** - AI集成层

### 2. 核心方法
```java
// 基础统计查询（含复购分析）
getDeliveryPerformanceStatistics(startDate, endDate)

// 产品明细查询
getDeliveryPerformanceProductDetails(startDate, endDate)

// 渠道统计查询
getDeliveryPerformanceChannelStats(startDate, endDate)

// 每日趋势查询
getDeliveryPerformanceDailyTrend(startDate, endDate)
```

### 3. 数据处理特性
- **复杂CTE查询** - 支持多层嵌套查询和窗口函数
- **复购分析** - 自动计算复购率和新客占比
- **目标管理** - 自动计算完成率和剩余目标
- **货币格式化** - 大金额自动转换为万元显示

## 🧪 测试用例

### 正常查询测试
```
✅ "本月交付业绩" → 返回本月统计数据和目标完成率
✅ "交付业绩复购分析" → 返回复购分析数据
✅ "上周交付业绩统计" → 返回上周数据
✅ "交付业绩产品明细" → 返回产品明细排行
```

### 目标完成率测试
```
✅ 月度查询 → 使用480万月度目标
✅ 非月度查询 → 使用比例目标
✅ 超额完成 → 显示"已达成目标"
✅ 未完成 → 显示剩余目标金额
```

### 复购分析测试
```
✅ 复购率计算 → 复购人数 ÷ 下单人数
✅ 新客分析 → 总业绩 - 复购业绩
✅ 复购业绩统计 → 基于ROW_NUMBER()窗口函数
```

### 不合理查询测试
```
❌ "下月交付业绩" → 幽默拒绝
❌ "明天交付业绩" → 未来时间拒绝
❌ "去年交付业绩" → 久远时间提示
```

## 📈 数据统计特性

### 1. 复购分析
- **复购识别** - 使用ROW_NUMBER()窗口函数按用户排序
- **复购业绩** - 统计用户第二次及以后的订单金额
- **复购率** - 复购人数 ÷ 总下单人数
- **新客分析** - 总业绩减去复购业绩

### 2. 智能目标管理
- **月度目标** - 固定480万元月度目标
- **比例目标** - 非整月查询按天数比例计算
- **完成率** - 实时计算完成百分比
- **剩余目标** - 自动计算还需完成的金额

### 3. 数据验证
- **订单过滤** - OrderState <> 99排除无效订单
- **时间精确** - 基于PaySettlementTime支付结算时间
- **团购排除** - IsGroup = 0排除团购订单

## 🎨 用户体验优化

### 1. 友好的回复格式
- 使用💰📦👥🔄等emoji图标增强视觉效果
- 清晰的层级结构
- 重要数据加粗显示

### 2. 智能查询识别
- 支持多种表达方式
- 自动识别查询意图
- 容错性强的关键词匹配

### 3. 复购可视化
- 复购率百分比显示
- 新客vs复购对比
- 复购贡献度分析

## 🔍 查询识别逻辑

### 关键词匹配
```java
// 交付相关关键词（排除全国交付）
"交付" && !"全国交付"

// 业绩相关关键词  
"业绩" || "收入" || "营收" || "统计" || "复购" || "明细"

// 排除其他查询
!("三嫂" || "新品" || "金刚" || "保洁" || "驻场" || "大学" || "私域")
```

### 优先级设置
1. 新品成交查询
2. 全国交付业绩查询
3. 驻场保洁集团结算查询
4. 大学收入查询
5. 私域ABC收款业绩查询
6. **交付业绩查询** ← 新增
7. 三嫂业绩查询
8. 金刚查询

## 🚀 使用示例

### 基础查询
```
用户：本月交付业绩
系统：返回本月交付业绩统计数据、复购分析和目标完成率
```

### 复购分析
```
用户：交付业绩复购分析
系统：返回复购率、复购业绩、新客分析等详细数据
```

### 产品明细
```
用户：交付业绩产品明细
系统：返回产品业绩排行和明细统计
```

## 💡 特殊功能

#### **复购分析算法**
- 使用ROW_NUMBER()窗口函数为每个用户的订单编号
- num > 1的订单被识别为复购订单
- 自动计算复购率和新客占比

#### **目标完成率计算**
- 查询"本月交付业绩"，实际120万，目标480万
- 自动计算：完成率 = 120/480 × 100% = 25.00%
- 剩余目标 = 480 - 120 = 360万元

#### **智能查询区分**
- "交付业绩" → 交付业绩查询
- "全国交付业绩" → 全国交付业绩查询
- 通过关键词精确区分不同的业务查询

通过这个交付业绩查询功能，用户可以方便地了解交付业务的整体表现、复购情况和目标完成进度！
