# 私域ABC收款SQL更新说明

## 🔄 SQL更新概述

根据最新的业务需求，更新私域ABC收款业绩查询的SQL语句，主要变化是将store表的查询字段从`storeid`改为`id`。

## 📊 SQL变化对比

### 变化前的SQL
```sql
-- 有订单号的支付查询
o.storeid IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))

-- 无订单号的支付查询  
AND storeid NOT IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))
```

### 变化后的SQL
```sql
-- 有订单号的支付查询
o.storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))

-- 无订单号的支付查询
AND storeid NOT IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
```

## 🔧 具体更新内容

### 1. 总金额查询SQL更新
**文件：** `PrivateDomainRepository.getPrivateDomainTotalAmount()`

**更新内容：**
```sql
-- 更新前
o.storeid IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))
AND storeid NOT IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))

-- 更新后  
o.storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
AND storeid NOT IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
```

### 2. 详细统计查询SQL更新
**文件：** `PrivateDomainRepository.getPrivateDomainDetailedStats()`

**更新内容：**
```sql
-- 更新前
o.storeid IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))
AND storeid NOT IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))

-- 更新后
o.storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
AND storeid NOT IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
```

### 3. 每日统计查询SQL更新
**文件：** `PrivateDomainRepository.getPrivateDomainDailyStats()`

**更新内容：**
```sql
-- 更新前
storeid IN (SELECT storeid FROM store WHERE StoreType IN (0, 1, 5))

-- 更新后
storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5))
```

## 📋 完整的更新后SQL

### 主查询SQL（基于用户提供的源SQL）
```sql
SELECT
    t1.amount + t2.amounts as 私域ABC收款业绩
FROM
    (
    SELECT SUM(o.Amount) AS amount 
    FROM
        (SELECT OrderNo, Amount FROM Payment 
         WHERE PaymentStatus = '2' 
           AND PayTime BETWEEN '2025/7/1' AND '2025/7/10 23:59' 
           AND OrderNo IS NOT NULL) t
        LEFT JOIN [Order] o ON t.OrderNo = o.id 
    WHERE
        o.storeid IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5)) 
        AND o.productid NOT IN (SELECT id FROM Product WHERE ProductCategoryid IN (17, 18, 19, 20, 27, 37, 22, 23)) 
        AND o.storeid NOT IN (88, 89) 
    ) t1
    LEFT JOIN (
    SELECT SUM(amount) amounts 
    FROM Payment 
    WHERE
        PaymentStatus = '2' 
        AND PayTime BETWEEN '2025/7/1' AND '2025/7/10 23:59:59' 
        AND OrderNo IS NULL 
        AND memberid NOT IN (
        SELECT memberid 
        FROM
            (
            SELECT *,
                ROW_NUMBER() OVER (PARTITION BY memberid ORDER BY CreateTime DESC) AS rn 
            FROM [Order] 
            WHERE memberid IN (
                SELECT DISTINCT memberid FROM Payment 
                WHERE PaymentStatus = '2' 
                  AND PayTime BETWEEN '2025/7/1' AND '2025/7/10 23:59:59' 
                  AND OrderNo IS NULL
            ) 
            ) a 
        WHERE a.rn = 1 
          AND storeid NOT IN (SELECT id FROM store WHERE StoreType IN (0, 1, 5)) 
        ) 
    ) t2 ON 1 = 1
```

## 🔍 变化原因分析

### 数据库结构变化
- **store表字段调整** - 查询字段从`storeid`改为`id`
- **保持业务逻辑不变** - 仍然过滤StoreType IN (0, 1, 5)的门店
- **数据一致性** - 确保查询结果的准确性

### 影响范围
- ✅ **查询逻辑不变** - 业务逻辑保持一致
- ✅ **过滤条件不变** - 仍然过滤相同的门店类型
- ✅ **返回结果不变** - 查询结果格式和内容保持一致
- ✅ **性能影响最小** - 只是字段名变化，不影响查询性能

## 🧪 验证方法

### 1. 编译检查
```bash
mvn clean compile
```

### 2. 启动应用
```bash
mvn spring-boot:run
```

### 3. 功能测试
```
查询：本月私域ABC收款业绩
预期：正常返回收款统计和目标完成率
验证：数据准确性和完整性
```

### 4. 对比测试
```
更新前查询结果 vs 更新后查询结果
确保：数据一致性和业务逻辑正确性
```

## 📈 业务影响

### 正面影响
- ✅ **数据准确性** - 基于最新的数据库结构查询
- ✅ **业务连续性** - 保持原有的业务逻辑和功能
- ✅ **系统稳定性** - 避免因字段变化导致的查询错误

### 注意事项
- 🔍 **数据验证** - 确保更新后的查询结果正确
- 🔍 **性能监控** - 关注查询性能是否有变化
- 🔍 **业务测试** - 验证各种查询场景的正确性

## 🚀 部署建议

### 1. 测试环境验证
- 在测试环境先验证SQL更新
- 对比更新前后的查询结果
- 确保数据一致性

### 2. 生产环境部署
- 选择业务低峰期部署
- 准备回滚方案
- 监控部署后的查询性能

### 3. 后续监控
- 监控查询错误率
- 验证业务数据准确性
- 收集用户反馈

## 💡 技术要点

### SQL优化
- 保持原有的查询结构和逻辑
- 只修改必要的字段引用
- 确保索引使用效率

### 代码维护
- 更新相关的注释和文档
- 保持代码的可读性
- 便于后续维护和扩展

通过这次SQL更新，私域ABC收款业绩查询功能将基于最新的数据库结构正常工作！
