package com.example.springai.mcp.controller;

import com.example.springai.mcp.client.McpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MCP控制器
 * 提供MCP客户端的REST API接口
 * 
 * <AUTHOR> AI Alibaba MCP Demo
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class McpController {
    
    private final McpClient mcpClient;
    
    @Autowired
    public McpController(McpClient mcpClient) {
        this.mcpClient = mcpClient;
    }
    
    /**
     * 连接到MCP服务器
     */
    @PostMapping("/connect")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> connect(
            @RequestParam(value = "serverUrl", defaultValue = "ws://localhost:8080/mcp") String serverUrl) {
        
        log.info("连接到MCP服务器: {}", serverUrl);
        
        return mcpClient.connect(serverUrl)
                .thenCompose(v -> mcpClient.initialize())
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "MCP连接建立成功");
                    response.put("serverInfo", result);
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("MCP连接失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "MCP连接失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.internalServerError().body(response);
                });
    }
    
    /**
     * 获取可用工具列表
     */
    @GetMapping("/tools")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> listTools() {
        log.info("获取MCP工具列表");
        
        return mcpClient.listTools()
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "获取工具列表成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("获取工具列表失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "获取工具列表失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.internalServerError().body(response);
                });
    }
    
    /**
     * 调用AI对话工具
     */
    @PostMapping("/tools/chat")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> callChatTool(
            @RequestParam(value = "message") String message) {
        
        log.info("调用AI对话工具: {}", message);
        
        Map<String, Object> arguments = Map.of("message", message);
        
        return mcpClient.callTool("ai_chat", arguments)
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "AI对话成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("AI对话失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "AI对话失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.internalServerError().body(response);
                });
    }
    
    /**
     * 调用AI流式对话工具
     */
    @PostMapping("/tools/stream-chat")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> callStreamChatTool(
            @RequestParam(value = "message") String message) {
        
        log.info("调用AI流式对话工具: {}", message);
        
        Map<String, Object> arguments = Map.of("message", message);
        
        return mcpClient.callTool("ai_stream_chat", arguments)
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "AI流式对话成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("AI流式对话失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "AI流式对话失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.internalServerError().body(response);
                });
    }
    
    /**
     * 调用AI图像生成工具
     */
    @PostMapping("/tools/image")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> callImageTool(
            @RequestParam(value = "prompt") String prompt,
            @RequestParam(value = "width", defaultValue = "1024") int width,
            @RequestParam(value = "height", defaultValue = "1024") int height) {

        log.info("调用AI图像生成工具: {}", prompt);

        Map<String, Object> arguments = Map.of(
            "prompt", prompt,
            "width", width,
            "height", height
        );

        return mcpClient.callTool("ai_image", arguments)
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "AI图像生成成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());

                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("AI图像生成失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "AI图像生成失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());

                    return ResponseEntity.internalServerError().body(response);
                });
    }

    /**
     * 调用业务查询工具
     */
    @PostMapping("/tools/business-query")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> callBusinessQueryTool(
            @RequestParam(value = "query") String query) {

        log.info("调用业务查询工具: {}", query);

        Map<String, Object> arguments = Map.of("query", query);

        return mcpClient.callTool("business_query", arguments)
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "业务查询成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());

                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("业务查询失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
                    response.put("data", "当前问题我还没学会，请给我点时间，我正在学习中哦~");
                    response.put("timestamp", System.currentTimeMillis());

                    return ResponseEntity.ok().body(response);
                });
    }
    
    /**
     * 获取可用资源列表
     */
    @GetMapping("/resources")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> listResources() {
        log.info("获取MCP资源列表");
        
        return mcpClient.listResources()
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "获取资源列表成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("获取资源列表失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "获取资源列表失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.internalServerError().body(response);
                });
    }
    
    /**
     * 读取指定资源
     */
    @GetMapping("/resources/read")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> readResource(
            @RequestParam(value = "uri") String uri) {
        
        log.info("读取MCP资源: {}", uri);
        
        return mcpClient.readResource(uri)
                .thenApply(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "读取资源成功");
                    response.put("data", result);
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(throwable -> {
                    log.error("读取资源失败", throwable);
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "读取资源失败: " + throwable.getMessage());
                    response.put("timestamp", System.currentTimeMillis());
                    
                    return ResponseEntity.internalServerError().body(response);
                });
    }
    
    /**
     * MCP健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("application", "Spring AI Alibaba MCP Demo");
        response.put("version", "1.0.0");
        response.put("mcpServer", "ws://localhost:8080/mcp");
        response.put("connected", mcpClient.isConnected());
        response.put("initialized", mcpClient.isInitialized());
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 检查MCP连接状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> status() {
        Map<String, Object> response = new HashMap<>();
        response.put("connected", mcpClient.isConnected());
        response.put("initialized", mcpClient.isInitialized());
        response.put("timestamp", System.currentTimeMillis());

        if (mcpClient.isConnected() && mcpClient.isInitialized()) {
            response.put("status", "READY");
            response.put("message", "MCP客户端已连接并初始化完成");
        } else if (mcpClient.isConnected()) {
            response.put("status", "CONNECTED");
            response.put("message", "MCP客户端已连接，但未初始化");
        } else {
            response.put("status", "DISCONNECTED");
            response.put("message", "MCP客户端未连接");
        }

        return ResponseEntity.ok(response);
    }
}
