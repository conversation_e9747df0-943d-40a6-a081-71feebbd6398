# 驻场保洁和开荒集团结算查询功能说明

## 🎯 功能概述

新增驻场保洁和开荒集团结算数据查询服务，基于DepartId=47的订单数据，支持自然语言查询驻场保洁和开荒集团结算统计、排行等信息。

## 📊 数据源说明

### SQL基础查询
```sql
SELECT
    t.ProductName AS 产品名称,
    t.Amount AS 订单金额,
    t.StartTime AS 订单时间
FROM [Order] AS t
WHERE t.OrderState <> 99 
    AND t.PaySettlementTime BETWEEN '2025/6/1' AND '2025/7/1' 
    AND t.DepartId = 47
```

### 查询条件
- **DepartId = 47** - 驻场保洁和开荒集团结算专用部门ID
- **OrderState <> 99** - 排除无效订单
- **PaySettlementTime** - 基于支付结算时间范围
- **Amount字段** - 使用订单金额而非实际金额

## 🔍 支持的查询类型

### 1. 基础统计查询
**触发关键词：** "驻场保洁"、"开荒"、"保洁"、"集团结算"、"结算"

**查询示例：**
- "本月驻场保洁集团结算"
- "上周开荒集团结算统计"
- "驻场保洁结算情况"
- "保洁集团结算业绩"

**返回内容：**
- 总体概况（产品数量、订单数、总营业额）
- 平均订单金额
- 产品明细列表

### 2. 排行榜查询
**触发关键词：** "驻场保洁排行"、"开荒结算排行"、"保洁结算排行"

**查询示例：**
- "驻场保洁结算排行"
- "开荒集团结算营业额排行"
- "保洁结算订单数排行"

**排序方式：**
- 默认按订单数排序
- 包含"营业额"或"收入"时按营业额排序

### 3. 时间范围支持
- **今天、昨天**
- **本周、上周**
- **本月、上月**
- **近7天、近30天**
- **自定义时间范围**

## 📋 返回数据格式

### 统计查询返回示例
```
**驻场保洁和开荒集团结算统计**

查询时间：30天

**📊 总体概况**
• **产品数量：15个** 🧹
• **总订单数：89单** 📦
• **总营业额：32.52万元** 💰
• 平均订单金额：365.40元 📊

**📋 产品明细**
🥇 **深度保洁服务**：25单，8,999.75元
🥈 **开荒保洁套餐**：18单，6,456.00元
🥉 **驻场保洁月套餐**：15单，5,234.50元
🏅 **精细保洁服务**：12单，4,123.25元
🏅 **开荒深度清洁**：10单，3,567.80元
```

### 排行榜查询返回示例
```
**驻场保洁和开荒集团结算排行(订单数)**

查询时间：30天

**📊 总体概况**
• **产品数量：15个** 🧹
• **总订单数：89单** 📦
• **总营业额：32.52万元** 💰
• 平均订单金额：365.40元 📊

**🏆 产品排行榜（按订单数）**
🥇 **深度保洁服务**：25单，8,999.75元
🥈 **开荒保洁套餐**：18单，6,456.00元
🥉 **驻场保洁月套餐**：15单，5,234.50元
... 还有12个产品未显示
```

## 🔧 技术架构

### 1. 服务层结构
- **CleaningSettlementRepository** - 数据访问层
- **CleaningSettlementService** - 业务逻辑层
- **BusinessAIService** - AI集成层

### 2. 核心方法
```java
// 基础统计查询
getCleaningSettlementStatistics(startDate, endDate)

// 按订单数排行
getCleaningSettlementRankingByOrderCount(startDate, endDate, limit)

// 按营业额排行
getCleaningSettlementRankingByRevenue(startDate, endDate, limit)

// 总体统计概览
getCleaningSettlementOverview(startDate, endDate)
```

### 3. 数据处理特性
- **累加统计验证** - 从明细数据累加验证总体统计
- **智能数据选择** - 优先使用准确的非零数据
- **货币格式化** - 大金额自动转换为万元显示
- **排名图标** - 前5名使用emoji图标显示

## 🧪 测试用例

### 正常查询测试
```
✅ "本月驻场保洁集团结算" → 返回本月统计数据
✅ "驻场保洁结算排行" → 返回排行榜数据
✅ "上周开荒集团结算统计" → 返回上周数据
✅ "保洁集团结算营业额排行" → 按营业额排序
```

### 不合理查询测试
```
❌ "下月驻场保洁结算" → 幽默拒绝
❌ "明天保洁集团结算" → 未来时间拒绝
❌ "去年驻场保洁统计" → 久远时间提示
```

### 边界情况测试
```
🔍 无数据时 → 显示"暂无统计数据"
🔍 单个产品 → 正常显示统计信息
🔍 大量产品 → 限制显示前5个，提示剩余数量
```

## 📈 数据统计特性

### 1. 多维度统计
- **产品维度** - 按产品分组统计
- **时间维度** - 支持各种时间范围
- **排序维度** - 订单数/营业额双重排序

### 2. 智能计算
- **平均订单金额** - 总营业额 ÷ 总订单数
- **平均单品营业额** - 总营业额 ÷ 产品数量
- **占比分析** - 头部产品占总体的百分比

### 3. 数据验证
- **数据库统计** - SQL聚合查询结果
- **程序计算** - 从明细数据累加计算
- **一致性检查** - 确保数据准确性

## 🎨 用户体验优化

### 1. 友好的回复格式
- 使用🧹emoji图标增强视觉效果
- 清晰的层级结构
- 重要数据加粗显示

### 2. 智能查询识别
- 支持多种表达方式
- 自动识别查询意图
- 容错性强的关键词匹配

### 3. 合理的数据展示
- 限制显示数量避免信息过载
- 提供剩余数据数量提示
- 突出显示关键统计信息

## 🔍 查询识别逻辑

### 关键词匹配
```java
// 保洁相关关键词
"驻场保洁" || "开荒" || "保洁" || "清洁"

// 结算相关关键词  
"集团结算" || "结算" || "业绩" || "统计" || "排行" || "情况"

// 排除其他查询
!("三嫂" || "交付" || "新品" || "金刚")
```

### 优先级设置
1. 新品成交查询
2. 全国交付业绩查询
3. **驻场保洁集团结算查询** ← 新增
4. 三嫂业绩查询
5. 金刚查询

## 🚀 使用示例

### 基础查询
```
用户：本月驻场保洁集团结算
系统：返回本月驻场保洁和开荒集团结算统计数据
```

### 排行查询
```
用户：驻场保洁结算排行
系统：返回按订单数排序的产品排行榜
```

### 营业额排行
```
用户：开荒集团结算营业额排行
系统：返回按营业额排序的产品排行榜
```

通过这个驻场保洁和开荒集团结算查询功能，用户可以方便地了解保洁业务的整体表现和产品排行情况！
