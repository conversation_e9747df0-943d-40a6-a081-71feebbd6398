package com.example.springai.entity;

import jakarta.persistence.*;

/**
 * 产品实体类
 */
@Entity
@Table(name = "Product")
public class Product {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "ProductName")
    private String productName;

    @Column(name = "name")
    private String name;

    @Column(name = "ProductType")
    private String productType;

    @Column(name = "Lable")
    private Integer lable;

    @Column(name = "ProductCategoryId")
    private Long productCategoryId;
    
    // 构造函数
    public Product() {}
    
    public Product(Long id, String productName, Long productCategoryId) {
        this.id = id;
        this.productName = productName;
        this.productCategoryId = productCategoryId;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Long getProductCategoryId() {
        return productCategoryId;
    }
    
    public void setProductCategoryId(Long productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public Integer getLable() {
        return lable;
    }

    public void setLable(Integer lable) {
        this.lable = lable;
    }

    /**
     * 判断是否为新品
     * 新品条件：Lable = 688 OR ProductCategoryId = 47
     */
    public boolean isNewProduct() {
        return (lable != null && lable == 688) ||
               (productCategoryId != null && productCategoryId == 47);
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", productName='" + productName + '\'' +
                ", productCategoryId=" + productCategoryId +
                '}';
    }
}
