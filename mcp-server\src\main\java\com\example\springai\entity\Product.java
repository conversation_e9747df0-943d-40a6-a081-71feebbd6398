package com.example.springai.entity;

import jakarta.persistence.*;

/**
 * 产品实体类
 */
@Entity
@Table(name = "Product")
public class Product {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "ProductName")
    private String productName;
    
    @Column(name = "ProductCategoryId")
    private Long productCategoryId;
    
    // 构造函数
    public Product() {}
    
    public Product(Long id, String productName, Long productCategoryId) {
        this.id = id;
        this.productName = productName;
        this.productCategoryId = productCategoryId;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Long getProductCategoryId() {
        return productCategoryId;
    }
    
    public void setProductCategoryId(Long productCategoryId) {
        this.productCategoryId = productCategoryId;
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", productName='" + productName + '\'' +
                ", productCategoryId=" + productCategoryId +
                '}';
    }
}
